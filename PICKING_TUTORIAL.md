# Quildora Picking Tutorial: Step-by-Step Examples

## Tutorial 1: Emergency Stock Pull (Direct Release)

**Scenario:** Production line needs 25 widgets urgently for equipment repair.

### Step-by-Step Instructions

1. **Open Picking Interface**
   - Navigate to **Picking** from main menu
   - Ensure you're in the correct warehouse

2. **Search for Items**
   - Type "widget" in the search bar
   - Results appear instantly (no waiting!)
   - Click on "Widget Model A" from results

3. **View Available Locations**
   - See all pallets containing widgets
   - Note quantities available on each pallet
   - Example: PAL-001 has 50, PAL-003 has 30

4. **Add Items to Cart**
   - Click "Add to Cart" on PAL-001
   - Adjust quantity to 25
   - Cart shows: "25x Widget Model A from PAL-001"

5. **Release Items Directly**
   - Click **"Release Items Directly"** (orange button)
   - Enter "Production Team A" in "Released To" field
   - Add note: "Emergency repair for Line 3"
   - Click **"Release Items"**

6. **Confirmation**
   - Confetti animation confirms success! 🎉
   - Items are immediately released
   - <PERSON>t log records the transaction

**Result:** Production team gets widgets immediately, no paperwork delays.

---

## Tutorial 2: Customer Order Fulfillment (Traditional Shipment)

**Scenario:** Customer ABC ordered 15 widgets, 8 gadgets, and 12 connectors.

### Step-by-Step Instructions

1. **Start Picking Session**
   - Open **Picking** interface
   - Clear any existing cart items

2. **Pick First Item: Widgets**
   - Search "widget"
   - Select "Widget Model A"
   - Add 15 from PAL-001 to cart

3. **Pick Second Item: Gadgets**
   - Search "gadget"
   - Select "Gadget Type B"
   - Add 8 from PAL-005 to cart

4. **Pick Third Item: Connectors**
   - Search "connector"
   - Select "Connector 12mm"
   - Add 12 from PAL-002 to cart

5. **Review Cart**
   - Cart shows 35 total items
   - Three different source pallets
   - All quantities correct

6. **Create Shipment**
   - Click **"Ship Items"** (green button)
   - Enter destination: "Customer ABC Warehouse"
   - Enter destination code: "12345"
   - Click **"Create Shipment"**

7. **Process Shipment**
   - View generated packing list
   - Print packing list for warehouse team
   - Update status to "Packed" when ready
   - Update status to "Shipped" when dispatched

**Result:** Professional shipment with full documentation and tracking.

---

## Tutorial 3: Large Mixed Order (Enhanced Shipment)

**Scenario:** Customer XYZ needs 20 specific widgets plus 2 complete pallets already labeled for them.

### Step-by-Step Instructions

1. **Pick Individual Items**
   - Search and add 20 widgets to cart
   - Verify cart shows correct quantities

2. **Open Enhanced Shipment**
   - Click **"Add Pallets"** (blue button)
   - Enhanced shipment modal opens

3. **Review Items Tab**
   - See your 20 widgets listed
   - Confirm source pallet information

4. **Select Complete Pallets**
   - Click **"Pallets"** tab
   - See available pallets for shipping
   - Check boxes for PAL-010 and PAL-011
   - Both show "Customer XYZ" as destination

5. **Verify Mixed Content**
   - Items tab: 20 individual widgets
   - Pallets tab: 2 complete pallets selected
   - Tab shows "Pallets (2)" indicating selection

6. **Create Mixed Shipment**
   - Enter destination: "Customer XYZ Distribution Center"
   - Enter code: "67890"
   - Click **"Create Shipment"**

7. **Process Complex Shipment**
   - Packing list shows both individual items and complete pallets
   - Warehouse team handles mixed content appropriately
   - Single tracking number for entire shipment

**Result:** Efficient handling of complex orders with mixed content types.

---

## Tutorial 4: Multi-Workflow Day

**Scenario:** Busy warehouse day with different types of requests.

### Morning: Stock Pull for Maintenance
- **9:00 AM:** Maintenance needs 5 bearings
- Use **Direct Release** to "Maintenance Team"
- Quick and efficient, no paperwork

### Mid-Morning: Customer Order
- **10:30 AM:** Customer DEF order arrives
- Use **Traditional Shipment** for 50 widgets, 25 gaskets
- Full documentation for customer delivery

### Afternoon: Large Customer Order
- **2:00 PM:** Major customer GHI order
- 100 individual items + 3 complete pallets
- Use **Enhanced Shipment** for mixed content

### End of Day: Emergency Request
- **4:30 PM:** Production line emergency
- Use **Direct Release** for immediate supply
- No delays, items released instantly

**Key Insight:** Different workflows for different needs throughout the day.

---

## Common Workflow Patterns

### Pattern 1: High-Volume Picking
```
Search → Multiple Items → Large Quantities → Traditional Shipment
```
- Best for standard customer orders
- Full documentation and tracking
- Professional packing lists

### Pattern 2: Emergency Response
```
Search → Specific Item → Quick Add → Direct Release
```
- Fastest possible fulfillment
- Minimal documentation
- Immediate availability

### Pattern 3: Complex Orders
```
Individual Items + Complete Pallets → Enhanced Shipment
```
- Handles mixed content efficiently
- Single shipment for complex orders
- Comprehensive tracking

### Pattern 4: Batch Processing
```
Multiple Small Orders → Sequential Traditional Shipments
```
- Process several orders in sequence
- Each gets proper documentation
- Efficient for order fulfillment teams

---

## Pro Tips for Efficient Picking

### Search Optimization
- **Use partial terms:** "wid" finds "widget" faster
- **Try SKU codes:** Often more specific than names
- **Check spelling:** Search is exact match

### Cart Management
- **Review before action:** Double-check quantities
- **Use source info:** Know which pallet items came from
- **Clear between orders:** Start fresh for each customer

### Workflow Selection
- **Emergency = Orange:** Direct release for urgency
- **Standard = Green:** Traditional shipment for customers
- **Complex = Blue:** Enhanced for mixed orders

### Mobile Efficiency
- **Use large buttons:** Designed for tablet/phone use
- **Landscape mode:** Better for cart management
- **Touch targets:** All buttons sized for fingers

### Quality Control
- **Verify quantities:** System validates against inventory
- **Check destinations:** Autocomplete helps with accuracy
- **Review before submit:** Final check prevents errors

---

## Troubleshooting Common Scenarios

### "I can't find the item"
1. Check warehouse context (top right)
2. Try different search terms
3. Verify item exists in your warehouse
4. Contact supervisor if item should be available

### "Not enough inventory"
1. Check quantities on each pallet
2. Consider multiple source pallets
3. Verify pallet status is "Stored"
4. Check if items are reserved for other orders

### "Wrong destination selected"
1. Use clear, specific destination names
2. Double-check destination codes
3. Contact customer service for clarification
4. Use autocomplete for consistency

### "Cart disappeared"
1. Cart persists across page refreshes
2. Check if you accidentally cleared it
3. Warehouse context changes clear cart
4. Start over if needed - search is fast

---

**Remember:** Practice makes perfect! Try each workflow type to become comfortable with all options available in Quildora.
