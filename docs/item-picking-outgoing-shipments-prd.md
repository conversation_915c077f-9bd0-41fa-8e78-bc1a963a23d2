# Product Requirements Document: Item-Based Picking & Outgoing Shipments

**Version:** 1.0  
**Date:** 2025-07-24  
**Development Phase:** Week 1 MVP Implementation  
**Target Completion:** 20 tasks × 20 minutes (6.7 hours)

## Executive Summary

This PRD defines the specifications for implementing Item-Based Picking and Outgoing Shipment Management features in Quildora's warehouse management system. These features create a cohesive pick-pack-ship workflow that differentiates Quildora from Oracle WMS by providing mobile-first, SMB-focused inventory operations.

**Core Features:**

1. **Item-Based Picking System** - Enhanced picking workflows that operate at the item level rather than just destination-based
2. **Outgoing Shipment Management** - Complete shipment lifecycle from creation through shipping confirmation

**Business Value:**

- Complete end-to-end fulfillment workflow (pick → pack → ship)
- Professional packing list generation for customer satisfaction
- Real-time inventory tracking and release management
- Mobile-optimized warehouse floor operations
- Competitive differentiation against legacy WMS solutions

## 1. System Architecture Overview

### High-Level Architecture

The features integrate seamlessly with Quildora's existing multi-tenant SaaS architecture:

```
┌─────────────────────────────────────────────────────────────┐
│                    Frontend (Next.js)                      │
├─────────────────────────────────────────────────────────────┤
│ ItemPickingView │ ShipmentCreation │ PackingListView       │
│ ItemSearchInput │ ShipmentStatus   │ InventoryRelease      │
├─────────────────────────────────────────────────────────────┤
│           Warehouse Context Provider                        │
│           React Query State Management                      │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                   Backend (NestJS)                         │
├─────────────────────────────────────────────────────────────┤
│ Items Controller    │ Shipments Controller                 │
│ ItemLocations API   │ PackingList API                      │
├─────────────────────────────────────────────────────────────┤
│        @RequireWarehouseAccess Decorators                  │
│        @LogAction Audit Logging                            │
├─────────────────────────────────────────────────────────────┤
│              Prisma ORM Layer                              │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│              PostgreSQL via Supabase                       │
│   Items │ ItemLocations │ Shipments │ ShipmentItems        │
└─────────────────────────────────────────────────────────────┘
```

### Technology Stack Justification

- **NestJS Backend:** Leverages existing decorator patterns (@RequireWarehouseAccess, @LogAction) for consistent security and audit logging
- **Next.js Frontend:** Maintains mobile-first responsive design with existing shadcn/ui component library
- **PostgreSQL via Supabase:** Extends current schema with new entities while preserving warehouse-scoped access control
- **Prisma ORM:** Provides type-safe database operations with existing transaction patterns
- **React Query:** Implements optimistic UI updates and warehouse context invalidation

### Integration Points

**Existing Pallet Management:**

- Item locations reference existing Pallet and PalletItem entities
- Shipments can include both individual items and complete pallets
- Maintains existing barcode and destination code functionality

**Destination Code System:**

- Shipments inherit destination information from picked items
- Auto-population of destination codes in shipment creation
- Unified destination lookup across picking and shipping workflows

**Audit Logging:**

- New actions: SEARCH_ITEMS, PICK_ITEMS, CREATE_SHIPMENT, RELEASE_INVENTORY, SHIP_PACKAGE
- Maintains existing audit trail patterns with warehouse context
- Detailed before/after states for inventory changes

### Deployment Architecture

**Multi-Tenant Considerations:**

- All new entities include tenantId for data isolation
- Warehouse-scoped access control enforced at API level
- Database indexes optimized for tenant + warehouse queries
- Horizontal scaling support through tenant partitioning

## 2. Database Schema Design

### New Entity Definitions

Based on analysis of existing schema at `/apps/backend/prisma/schema.prisma`, the following new entities extend the current warehouse-scoped architecture:

#### ItemLocation Entity

```prisma
model ItemLocation {
  id          String   @id @default(cuid())
  quantity    Int
  lastUpdated DateTime @updatedAt
  createdAt   DateTime @default(now())

  // Relationships
  itemId   String
  item     Item   @relation(fields: [itemId], references: [id])

  palletId String
  pallet   Pallet @relation(fields: [palletId], references: [id])

  // Warehouse scope derived through pallet.location.warehouse
  @@unique([itemId, palletId], name: "item_pallet_location_unique")
  @@index([itemId]) // Fast item location lookup
  @@index([palletId]) // Fast pallet contents lookup
}
```

#### OutgoingShipment Entity

```prisma
model OutgoingShipment {
  id              String   @id @default(cuid())
  shipmentNumber  String   // Auto-generated: OS-YYYYMMDD-XXXX
  status          ShipmentStatus @default(PREPARING)
  destination     String?
  destinationCode String?
  notes           String?
  packedAt        DateTime?
  shippedAt       DateTime?
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Tenant relationship (required for multi-tenant isolation)
  tenantId String
  tenant   Tenant @relation(fields: [tenantId], references: [id])

  // Warehouse relationship (derived through items/pallets)
  warehouseId String?
  warehouse   Warehouse? @relation(fields: [warehouseId], references: [id])

  // Relationships
  shipmentItems OutgoingShipmentItem[]
  shipmentPallets OutgoingShipmentPallet[]

  @@unique([tenantId, shipmentNumber])
  @@index([tenantId, status])
  @@index([tenantId, warehouseId, status])
  @@index([destination])
  @@index([destinationCode])
}

enum ShipmentStatus {
  PREPARING
  PACKED
  SHIPPED
  DELIVERED
  CANCELLED
}
```

#### OutgoingShipmentItem Entity

```prisma
model OutgoingShipmentItem {
  id       String @id @default(cuid())
  quantity Int
  addedAt  DateTime @default(now())

  // Relationships
  shipmentId String
  shipment   OutgoingShipment @relation(fields: [shipmentId], references: [id])

  itemId String
  item   Item   @relation(fields: [itemId], references: [id])

  // Optional: Track which pallet the item came from
  sourcePalletId String?
  sourcePallet   Pallet? @relation(fields: [sourcePalletId], references: [id])

  @@unique([shipmentId, itemId], name: "shipment_item_unique")
}
```

#### OutgoingShipmentPallet Entity

```prisma
model OutgoingShipmentPallet {
  id      String @id @default(cuid())
  addedAt DateTime @default(now())

  // Relationships
  shipmentId String
  shipment   OutgoingShipment @relation(fields: [shipmentId], references: [id])

  palletId String
  pallet   Pallet @relation(fields: [palletId], references: [id])

  @@unique([shipmentId, palletId], name: "shipment_pallet_unique")
}
```

### Schema Extensions to Existing Entities

#### Item Entity Extensions

```prisma
// Add to existing Item model
model Item {
  // ... existing fields ...

  // New relationships
  itemLocations      ItemLocation[]
  outgoingShipmentItems OutgoingShipmentItem[]
}
```

#### Pallet Entity Extensions

```prisma
// Add to existing Pallet model
model Pallet {
  // ... existing fields ...

  // New relationships
  itemLocations         ItemLocation[]
  outgoingShipmentItems OutgoingShipmentItem[] @relation("SourcePallet")
  outgoingShipmentPallets OutgoingShipmentPallet[]
}
```

#### Warehouse Entity Extensions

```prisma
// Add to existing Warehouse model
model Warehouse {
  // ... existing fields ...

  // New relationships
  outgoingShipments OutgoingShipment[]
}
```

#### Tenant Entity Extensions

```prisma
// Add to existing Tenant model
model Tenant {
  // ... existing fields ...

  // New relationships
  outgoingShipments OutgoingShipment[]
}
```

### Indexing Strategy

**Performance Optimization Indexes:**

1. **Item Search Performance:**

   - `@@index([tenantId, name])` - Fast item name search within tenant
   - `@@index([tenantId, sku])` - Fast SKU lookup within tenant
   - `@@index([tenantId, status])` - Active item filtering

2. **Location Lookup Performance:**

   - `@@index([itemId])` - Find all locations for an item
   - `@@index([palletId])` - Find all items on a pallet
   - Composite index on `[itemId, palletId]` for unique constraint

3. **Shipment Operations:**
   - `@@index([tenantId, warehouseId, status])` - Warehouse-scoped shipment filtering
   - `@@index([destination])` - Destination-based shipment lookup
   - `@@index([destinationCode])` - Code-based shipment search

### Migration Strategy

**Backward Compatibility Approach:**

1. **Phase 1:** Add new tables without foreign key constraints
2. **Phase 2:** Populate ItemLocation data from existing PalletItem relationships
3. **Phase 3:** Add foreign key constraints and indexes
4. **Phase 4:** Update application code to use new entities
5. **Phase 5:** Add audit logging for new operations

**Data Migration Script:**

```sql
-- Populate ItemLocation from existing PalletItem data
INSERT INTO ItemLocation (id, itemId, palletId, quantity, createdAt, lastUpdated)
SELECT
  gen_random_uuid(),
  pi.itemId,
  pi.palletId,
  pi.quantity,
  pi.dateAdded,
  NOW()
FROM PalletItem pi
WHERE pi.quantity > 0;
```

## 3. Comprehensive API Design

### Item Search & Location APIs

#### GET /api/items/search

**Purpose:** Search items by name or SKU with warehouse context

**Request:**

```typescript
GET /api/items/search?q=search_term&warehouseId=warehouse_id&limit=20&offset=0

Headers:
  Authorization: Bearer <jwt_token>
  X-Warehouse-ID: <warehouse_id>
```

**Response:**

```json
{
  "items": [
    {
      "id": "item_123",
      "sku": "SKU-001",
      "name": "Widget A",
      "description": "High-quality widget",
      "unitOfMeasure": "Each",
      "status": "Active",
      "totalQuantity": 150,
      "availableQuantity": 120,
      "locationCount": 3
    }
  ],
  "pagination": {
    "total": 45,
    "limit": 20,
    "offset": 0,
    "hasMore": true
  }
}
```

**Security:** @RequireWarehouseAccess()  
**Audit:** @LogAction({ action: "SEARCH_ITEMS", entity: "Item" })

#### GET /api/items/:id/locations

**Purpose:** Get all pallet locations for a specific item

**Request:**

```typescript
GET /api/items/item_123/locations?warehouseId=warehouse_id

Headers:
  Authorization: Bearer <jwt_token>
  X-Warehouse-ID: <warehouse_id>
```

**Response:**

```json
{
  "itemId": "item_123",
  "itemName": "Widget A",
  "totalQuantity": 150,
  "locations": [
    {
      "palletId": "pallet_456",
      "palletBarcode": "PLT-2024-001",
      "quantity": 50,
      "location": {
        "id": "loc_789",
        "name": "A-01-01",
        "category": "Storage",
        "type": "RACK"
      },
      "lastUpdated": "2024-07-24T10:30:00Z"
    }
  ]
}
```

**Security:** @RequireWarehouseAccess()  
**Audit:** @LogAction({ action: "VIEW_ITEM_LOCATIONS", entity: "Item" })

### Shipment Management APIs

#### POST /api/shipments

**Purpose:** Create new outgoing shipment

**Request:**

```typescript
POST /api/shipments

Headers:
  Authorization: Bearer <jwt_token>
  X-Warehouse-ID: <warehouse_id>

Body:
{
  "destination": "Customer ABC Warehouse",
  "destinationCode": "12345",
  "notes": "Fragile items - handle with care",
  "items": [
    {
      "itemId": "item_123",
      "quantity": 25,
      "sourcePalletId": "pallet_456"
    }
  ],
  "pallets": ["pallet_789"]
}
```

**Response:**

```json
{
  "id": "shipment_001",
  "shipmentNumber": "***********-0001",
  "status": "PREPARING",
  "destination": "Customer ABC Warehouse",
  "destinationCode": "12345",
  "notes": "Fragile items - handle with care",
  "createdAt": "2024-07-24T10:30:00Z",
  "items": [
    {
      "id": "si_001",
      "itemId": "item_123",
      "itemName": "Widget A",
      "quantity": 25,
      "sourcePalletId": "pallet_456"
    }
  ],
  "pallets": [
    {
      "id": "sp_001",
      "palletId": "pallet_789",
      "palletBarcode": "PLT-2024-002"
    }
  ]
}
```

**Security:** @RequireWarehouseAccess()  
**Audit:** @LogAction({ action: "CREATE_SHIPMENT", entity: "OutgoingShipment" })

#### GET /api/shipments/:id/packing-list

**Purpose:** Generate packing list for shipment

**Request:**

```typescript
GET /api/shipments/shipment_001/packing-list

Headers:
  Authorization: Bearer <jwt_token>
  X-Warehouse-ID: <warehouse_id>
```

**Response:**

```json
{
  "shipmentId": "shipment_001",
  "shipmentNumber": "***********-0001",
  "destination": "Customer ABC Warehouse",
  "destinationCode": "12345",
  "generatedAt": "2024-07-24T10:30:00Z",
  "items": [
    {
      "sku": "SKU-001",
      "name": "Widget A",
      "quantity": 25,
      "unitOfMeasure": "Each",
      "sourcePallet": "PLT-2024-001"
    }
  ],
  "pallets": [
    {
      "barcode": "PLT-2024-002",
      "description": "Mixed items for Customer ABC",
      "itemCount": 3,
      "totalQuantity": 75
    }
  ],
  "summary": {
    "totalItems": 4,
    "totalQuantity": 100,
    "totalPallets": 2
  }
}
```

**Security:** @RequireWarehouseAccess()  
**Audit:** @LogAction({ action: "GENERATE_PACKING_LIST", entity: "OutgoingShipment" })

#### POST /api/shipments/:id/items

**Purpose:** Add items to existing shipment

**Request:**

```typescript
POST /api/shipments/shipment_001/items

Headers:
  Authorization: Bearer <jwt_token>
  X-Warehouse-ID: <warehouse_id>

Body:
{
  "items": [
    {
      "itemId": "item_456",
      "quantity": 10,
      "sourcePalletId": "pallet_123"
    }
  ]
}
```

**Response:**

```json
{
  "shipmentId": "shipment_001",
  "addedItems": [
    {
      "id": "si_002",
      "itemId": "item_456",
      "itemName": "Widget B",
      "quantity": 10,
      "sourcePalletId": "pallet_123",
      "addedAt": "2024-07-24T10:35:00Z"
    }
  ],
  "updatedTotals": {
    "totalItems": 5,
    "totalQuantity": 110
  }
}
```

**Security:** @RequireWarehouseAccess()  
**Audit:** @LogAction({ action: "ADD_ITEMS_TO_SHIPMENT", entity: "OutgoingShipment" })

#### PUT /api/shipments/:id/status

**Purpose:** Update shipment status (preparing → packed → shipped)

**Request:**

```typescript
PUT /api/shipments/shipment_001/status

Headers:
  Authorization: Bearer <jwt_token>
  X-Warehouse-ID: <warehouse_id>

Body:
{
  "status": "PACKED",
  "notes": "All items packed and ready for pickup"
}
```

**Response:**

```json
{
  "id": "shipment_001",
  "status": "PACKED",
  "packedAt": "2024-07-24T11:00:00Z",
  "notes": "All items packed and ready for pickup",
  "updatedAt": "2024-07-24T11:00:00Z"
}
```

**Security:** @RequireWarehouseAccess()  
**Audit:** @LogAction({ action: "UPDATE_SHIPMENT_STATUS", entity: "OutgoingShipment" })

#### POST /api/shipments/:id/release

**Purpose:** Release inventory from warehouse (marks items as shipped)

**Request:**

```typescript
POST /api/shipments/shipment_001/release

Headers:
  Authorization: Bearer <jwt_token>
  X-Warehouse-ID: <warehouse_id>

Body:
{
  "releaseNotes": "Released to carrier XYZ",
  "trackingNumber": "1Z999AA1234567890"
}
```

**Response:**

```json
{
  "shipmentId": "shipment_001",
  "status": "SHIPPED",
  "shippedAt": "2024-07-24T11:30:00Z",
  "trackingNumber": "1Z999AA1234567890",
  "releasedItems": [
    {
      "itemId": "item_123",
      "quantity": 25,
      "releasedFromPallet": "pallet_456"
    }
  ],
  "releasedPallets": ["pallet_789"],
  "inventoryUpdates": {
    "itemsReleased": 2,
    "palletsReleased": 1,
    "totalQuantityReleased": 100
  }
}
```

**Security:** @RequireWarehouseAccess()  
**Audit:** @LogAction({ action: "RELEASE_INVENTORY", entity: "OutgoingShipment" })

#### GET /api/shipments/search

**Purpose:** Search and filter shipments

**Request:**

```typescript
GET /api/shipments/search?status=PREPARING&destination=Customer&warehouseId=warehouse_id&limit=20&offset=0

Headers:
  Authorization: Bearer <jwt_token>
  X-Warehouse-ID: <warehouse_id>
```

**Response:**

```json
{
  "shipments": [
    {
      "id": "shipment_001",
      "shipmentNumber": "***********-0001",
      "status": "PREPARING",
      "destination": "Customer ABC Warehouse",
      "destinationCode": "12345",
      "itemCount": 4,
      "palletCount": 2,
      "createdAt": "2024-07-24T10:30:00Z"
    }
  ],
  "pagination": {
    "total": 15,
    "limit": 20,
    "offset": 0,
    "hasMore": false
  },
  "filters": {
    "status": ["PREPARING", "PACKED", "SHIPPED"],
    "destinations": ["Customer ABC", "Customer XYZ"]
  }
}
```

**Security:** @RequireWarehouseAccess()  
**Audit:** @LogAction({ action: "SEARCH_SHIPMENTS", entity: "OutgoingShipment" })

### Error Handling Strategy

**HTTP Status Codes:**

- `200` - Success
- `201` - Created (new shipment)
- `400` - Bad Request (validation errors)
- `401` - Unauthorized (invalid token)
- `403` - Forbidden (warehouse access denied)
- `404` - Not Found (item/shipment not found)
- `409` - Conflict (insufficient inventory)
- `422` - Unprocessable Entity (business logic errors)
- `500` - Internal Server Error

**Error Response Format:**

```json
{
  "error": {
    "code": "INSUFFICIENT_INVENTORY",
    "message": "Not enough quantity available for item Widget A",
    "details": {
      "itemId": "item_123",
      "requestedQuantity": 50,
      "availableQuantity": 25,
      "warehouseId": "warehouse_456"
    }
  }
}
```

**Rate Limiting:**

- Search endpoints: 100 requests/minute per user
- Create/Update operations: 60 requests/minute per user
- Bulk operations: 20 requests/minute per user

## 4. Frontend Architecture

### Component Hierarchy

The new components integrate with existing PickingScreen patterns and warehouse context:

```
PickingScreen (existing)
├── ItemPickingView (NEW)
│   ├── ItemSearchInput (NEW)
│   ├── ItemLocationList (NEW)
│   └── PickedItemsCart (NEW)
├── ShipmentCreationModal (NEW)
│   ├── DestinationSelector (reuse existing)
│   ├── ItemSelector (NEW)
│   └── PalletSelector (reuse existing)
└── ShipmentManagement (NEW)
    ├── ShipmentStatusDashboard (NEW)
    ├── PackingListView (NEW)
    ├── InventoryReleaseModal (NEW)
    └── ShipmentSearchFilter (NEW)
```

### Component Specifications

#### ItemPickingView.tsx

**Purpose:** Main interface for item-based picking operations

**Props Interface:**

```typescript
interface ItemPickingViewProps {
  onItemPicked: (item: PickedItem) => void;
  pickedItems: PickedItem[];
  onCreateShipment: () => void;
}

interface PickedItem {
  itemId: string;
  itemName: string;
  quantity: number;
  sourcePalletId: string;
  sourcePalletBarcode: string;
  locationName: string;
}
```

**Key Features:**

- Integrates with existing warehouse context via `useWarehouse()`
- Uses `useSuspenseQuery` for instant loading without spinners
- Mobile-optimized with 48px touch targets
- Follows existing shadcn/ui design patterns

#### ItemSearchInput.tsx

**Purpose:** Autocomplete search for items with barcode scanning support

**Props Interface:**

```typescript
interface ItemSearchInputProps {
  onItemSelect: (item: Item) => void;
  placeholder?: string;
  autoFocus?: boolean;
  enableBarcodeScanning?: boolean;
}
```

**Key Features:**

- 300ms debounced search following existing DestinationFilter patterns
- Keyboard navigation (arrow keys, enter, escape)
- Screen reader accessibility with ARIA labels
- Camera-based barcode scanning integration
- Warehouse-scoped search results

#### ShipmentCreationModal.tsx

**Purpose:** Modal for creating outgoing shipments from picked items

**Props Interface:**

```typescript
interface ShipmentCreationModalProps {
  isOpen: boolean;
  onClose: () => void;
  initialItems?: PickedItem[];
  onShipmentCreated: (shipment: OutgoingShipment) => void;
}
```

**Key Features:**

- Follows existing modal patterns (CreatePalletDialog, ReleasePalletModal)
- `max-h-[90vh] overflow-y-auto` for mobile compatibility
- `sm:max-w-[600px]` responsive sizing
- Destination auto-population from existing destination code system
- Optimistic UI updates with React Query mutations

#### PackingListView.tsx

**Purpose:** Generate and display professional packing lists

**Props Interface:**

```typescript
interface PackingListViewProps {
  shipmentId: string;
  onPrint: () => void;
  onDownload: () => void;
  readOnly?: boolean;
}
```

**Key Features:**

- PDF generation following existing placard printing patterns
- Print functionality with popup blocker workarounds
- Professional formatting for customer-facing documents
- Mobile-optimized viewing with zoom controls

#### ShipmentStatusDashboard.tsx

**Purpose:** Overview of all shipments with status filtering

**Props Interface:**

```typescript
interface ShipmentStatusDashboardProps {
  onShipmentSelect: (shipment: OutgoingShipment) => void;
  defaultStatus?: ShipmentStatus;
}
```

**Key Features:**

- Card-based layout following existing dashboard patterns
- Real-time status updates via React Query
- Warehouse-scoped filtering
- Mobile-first responsive design

### State Management with React Query

**Query Key Structure:**

```typescript
// Item-related queries
["items", "search", warehouseId, searchTerm][
  ("items", itemId, "locations", warehouseId)
][
  // Shipment-related queries
  ("shipments", warehouseId, filters)
][("shipments", shipmentId)][("shipments", shipmentId, "packing-list")];
```

**Warehouse Context Integration:**

```typescript
// Automatic invalidation on warehouse change
const { currentWarehouse } = useWarehouse();

useEffect(() => {
  queryClient.invalidateQueries({
    queryKey: ["items", currentWarehouse?.id],
  });
  queryClient.invalidateQueries({
    queryKey: ["shipments", currentWarehouse?.id],
  });
}, [currentWarehouse?.id]);
```

**Optimistic Updates Pattern:**

```typescript
const createShipmentMutation = useMutation({
  mutationFn: createShipment,
  onMutate: async (newShipment) => {
    // Cancel outgoing refetches
    await queryClient.cancelQueries({
      queryKey: ["shipments", warehouseId],
    });

    // Snapshot previous value
    const previousShipments = queryClient.getQueryData([
      "shipments",
      warehouseId,
    ]);

    // Optimistically update
    queryClient.setQueryData(
      ["shipments", warehouseId],
      (old: OutgoingShipment[]) => [...old, newShipment]
    );

    return { previousShipments };
  },
  onError: (err, newShipment, context) => {
    // Rollback on error
    queryClient.setQueryData(
      ["shipments", warehouseId],
      context?.previousShipments
    );
  },
  onSettled: () => {
    // Refetch to ensure consistency
    queryClient.invalidateQueries({
      queryKey: ["shipments", warehouseId],
    });
  },
});
```

### Mobile-First Responsive Design

**Touch Target Optimization:**

- Minimum 48px touch targets for all interactive elements
- Increased spacing between buttons and form fields
- Large, easy-to-tap search and filter controls

**Warehouse Lighting Optimization:**

- High contrast color schemes for warehouse environments
- Large, readable fonts (minimum 16px)
- Clear visual hierarchy with bold headings and borders

**Navigation Integration:**

```typescript
// Existing warehouse routing patterns
const router = useRouter();

const handleShipmentCreated = (shipment: OutgoingShipment) => {
  // Confetti animation for completion
  triggerConfetti();

  // Navigate to shipment details
  router.push(`/shipments/${shipment.id}`);
};
```

## 5. Detailed CRUD Operations

### Items Entity Operations

#### Create Operations

**Validation Rules:**

- SKU must be unique within tenant (if provided)
- Name is required and must be 1-255 characters
- UnitOfMeasure defaults to "Each"
- Status defaults to "Active"

**Business Logic Constraints:**

- Only WAREHOUSE_MANAGER or TENANT_ADMIN can create items
- Items are tenant-scoped, accessible across all warehouses
- Automatic audit logging with CREATE_ITEM action

**API Implementation:**

```typescript
@Post()
@RequireWarehouseAccess({ minimumRole: Role.WAREHOUSE_MANAGER })
@LogAction({ action: "CREATE_ITEM", entity: "Item" })
async createItem(
  @Body() createItemDto: CreateItemDto,
  @Req() req: RequestWithWarehouseContext
) {
  return this.itemsService.create(createItemDto, req.user);
}
```

#### Read Operations

**Filtering Capabilities:**

- Search by name (case-insensitive, partial match)
- Search by SKU (exact match)
- Filter by status (Active, Inactive)
- Warehouse-scoped availability filtering

**Pagination Implementation:**

- Default limit: 20 items
- Maximum limit: 100 items
- Offset-based pagination for simplicity
- Total count included in response

**Sorting Options:**

- Name (A-Z, Z-A)
- SKU (A-Z, Z-A)
- Created date (newest first, oldest first)
- Total quantity (high to low, low to high)

#### Update Operations

**Partial Update Support:**

- Name, description, unitOfMeasure can be updated
- SKU changes require uniqueness validation
- Status changes affect availability calculations

**Optimistic UI Patterns:**

- Immediate UI feedback on form submission
- Rollback on server error with toast notification
- Real-time validation with debounced API calls

**Conflict Resolution:**

- Last-write-wins for concurrent updates
- Version-based optimistic locking for critical fields
- User notification of conflicts with merge options

#### Delete Operations

**Soft Delete Policy:**

- Items are marked as "Inactive" rather than hard deleted
- Preserves historical data and audit trails
- Inactive items excluded from search by default

**Cascading Effects:**

- ItemLocation records remain but marked as inactive
- Existing shipments retain item references
- Audit logs preserve complete history

### ItemLocations Entity Operations

#### Create Operations

**Validation Rules:**

- ItemId must reference existing active item
- PalletId must reference existing pallet in same warehouse
- Quantity must be positive integer
- Unique constraint on (itemId, palletId)

**Business Logic Constraints:**

- Automatic creation when items added to pallets
- Warehouse scope validation through pallet relationship
- Real-time inventory level updates

#### Read Operations

**Location Lookup Performance:**

- Indexed queries on itemId for fast location discovery
- Warehouse-scoped filtering through pallet relationships
- Aggregated quantity calculations across locations

**API Response Format:**

```typescript
interface ItemLocationResponse {
  itemId: string;
  itemName: string;
  totalQuantity: number;
  locations: Array<{
    palletId: string;
    palletBarcode: string;
    quantity: number;
    location: {
      id: string;
      name: string;
      category: LocationCategory;
      type: LocationType;
    };
    lastUpdated: string;
  }>;
}
```

#### Update Operations

**Quantity Adjustments:**

- Support for increment/decrement operations
- Validation against available inventory
- Automatic audit logging of quantity changes

**Location Transfers:**

- Move items between pallets within warehouse
- Validation of destination pallet capacity
- Atomic transactions for data consistency

#### Delete Operations

**Inventory Release:**

- Remove items from locations when shipped
- Maintain audit trail of release operations
- Update aggregate inventory levels

### OutgoingShipments Entity Operations

#### Create Operations

**Validation Rules:**

- Destination is required (name or code)
- At least one item or pallet must be included
- Quantities must not exceed available inventory
- Warehouse access validation for all included items/pallets

**Business Logic Constraints:**

- Auto-generate shipment number: OS-YYYYMMDD-XXXX
- Default status: PREPARING
- Warehouse derived from included items/pallets
- Inventory reservation on creation

**Transaction Handling:**

```typescript
async createShipment(data: CreateShipmentDto, user: EnhancedUserPayload) {
  return this.prisma.$transaction(async (tx) => {
    // 1. Validate inventory availability
    await this.validateInventoryAvailability(data.items, tx);

    // 2. Create shipment record
    const shipment = await tx.outgoingShipment.create({
      data: {
        ...data,
        shipmentNumber: await this.generateShipmentNumber(),
        tenantId: user.tenantId,
      }
    });

    // 3. Create shipment items
    await this.createShipmentItems(shipment.id, data.items, tx);

    // 4. Reserve inventory
    await this.reserveInventory(data.items, tx);

    // 5. Create audit log
    await this.auditLogService.log({
      action: "CREATE_SHIPMENT",
      entity: "OutgoingShipment",
      entityId: shipment.id,
      userId: user.id,
      tenantId: user.tenantId,
    });

    return shipment;
  });
}
```

#### Read Operations

**Filtering Capabilities:**

- Status-based filtering (PREPARING, PACKED, SHIPPED)
- Destination name/code search
- Date range filtering (created, packed, shipped)
- Warehouse-scoped access control

**Pagination and Sorting:**

- Default sort: newest first
- Support for status, destination, date sorting
- Efficient pagination with cursor-based approach for large datasets

#### Update Operations

**Status Transitions:**

- PREPARING → PACKED (requires all items confirmed)
- PACKED → SHIPPED (requires inventory release)
- Any status → CANCELLED (with inventory restoration)

**Item Management:**

- Add items to existing shipments
- Remove items (with inventory restoration)
- Quantity adjustments with validation

**Optimistic UI Updates:**

```typescript
const updateShipmentStatus = useMutation({
  mutationFn: ({ shipmentId, status }) =>
    updateShipmentStatusAPI(shipmentId, status),
  onMutate: async ({ shipmentId, status }) => {
    // Optimistically update UI
    queryClient.setQueryData(
      ["shipments", shipmentId],
      (old: OutgoingShipment) => ({ ...old, status })
    );
  },
  onError: (error, variables, context) => {
    // Rollback on error
    queryClient.setQueryData(
      ["shipments", variables.shipmentId],
      context?.previousShipment
    );
    toast.error("Failed to update shipment status");
  },
});
```

#### Delete Operations

**Cancellation Policy:**

- Only PREPARING and PACKED shipments can be cancelled
- Inventory restoration for all reserved items
- Audit trail preservation with cancellation reason

**Cascading Effects:**

- ShipmentItem records marked as cancelled
- Inventory reservations released
- Pallet assignments restored to available status

## 6. User Experience Flow

### Item-Based Picking Workflow

**User Journey Map:**

1. **Search Phase**

   - User opens ItemPickingView from main picking screen
   - Enters item name/SKU in ItemSearchInput with autocomplete
   - System shows matching items with availability counts
   - User selects desired item from dropdown

2. **Locate Phase**

   - System displays ItemLocationList showing all pallet locations
   - Each location shows: pallet barcode, location name, available quantity
   - User navigates to physical location using mobile device
   - Scans pallet barcode to confirm correct location

3. **Pick Phase**

   - User enters quantity to pick (with validation against available)
   - System shows picked items in cart with running total
   - User can pick from multiple locations for same item
   - Visual feedback with progress indicators and success animations

4. **Add to Shipment Phase**
   - User reviews picked items in cart summary
   - Clicks "Create Shipment" to open ShipmentCreationModal
   - System auto-populates destination from picked items (if consistent)
   - User confirms shipment creation with confetti animation

**Wireframe Descriptions:**

**ItemPickingView Layout:**

```
┌─────────────────────────────────────────┐
│ [🔍 Search items...] [📷 Scan]          │
├─────────────────────────────────────────┤
│ 📦 Widget A (SKU-001)                   │
│    Available: 150 units in 3 locations │
│    [View Locations] [Pick Items]        │
├─────────────────────────────────────────┤
│ 🛒 Picked Items Cart (5 items)          │
│    Widget A: 25 units from PLT-001     │
│    Widget B: 10 units from PLT-002     │
│    [Create Shipment] [Clear Cart]       │
└─────────────────────────────────────────┘
```

**ItemLocationList Layout:**

```
┌─────────────────────────────────────────┐
│ 📦 Widget A - All Locations             │
├─────────────────────────────────────────┤
│ 🏷️ PLT-2024-001 | A-01-01 | 50 units   │
│    Last updated: 2 hours ago           │
│    [Pick from this location]           │
├─────────────────────────────────────────┤
│ 🏷️ PLT-2024-015 | B-03-05 | 75 units   │
│    Last updated: 1 day ago             │
│    [Pick from this location]           │
├─────────────────────────────────────────┤
│ 🏷️ PLT-2024-023 | C-02-12 | 25 units   │
│    Last updated: 3 hours ago           │
│    [Pick from this location]           │
└─────────────────────────────────────────┘
```

### Outgoing Shipment Management Workflow

**User Journey Map:**

1. **Creation Phase**

   - User creates shipment from picked items or manually
   - ShipmentCreationModal opens with destination auto-population
   - User adds additional items/pallets if needed
   - System validates inventory availability and creates shipment

2. **Packing Phase**

   - User opens PackingListView to see professional packing list
   - Prints packing list using existing popup blocker workaround
   - Physically packs items according to packing list
   - Updates shipment status to PACKED when complete

3. **Release Phase**

   - User opens InventoryReleaseModal to confirm shipment
   - Reviews all items and pallets being released
   - Enters tracking number and carrier information
   - Confirms inventory release with final validation

4. **Completion Phase**
   - System updates shipment status to SHIPPED
   - Triggers confetti animation for successful completion
   - Updates inventory levels and audit logs
   - Navigates back to shipment dashboard

**Wireframe Descriptions:**

**ShipmentCreationModal Layout:**

```
┌─────────────────────────────────────────┐
│ ✨ Create Outgoing Shipment             │
├─────────────────────────────────────────┤
│ Destination: [Customer ABC (12345)  ▼] │
│ Notes: [Optional shipping notes...    ] │
├─────────────────────────────────────────┤
│ Items to Ship:                          │
│ ✓ Widget A: 25 units from PLT-001      │
│ ✓ Widget B: 10 units from PLT-002      │
│ [+ Add More Items]                      │
├─────────────────────────────────────────┤
│ Pallets to Ship:                        │
│ ✓ PLT-2024-005 (Mixed items)           │
│ [+ Add Pallets]                         │
├─────────────────────────────────────────┤
│ [Cancel] [Create Shipment]              │
└─────────────────────────────────────────┘
```

**PackingListView Layout:**

```
┌─────────────────────────────────────────┐
│ 📋 Packing List - ***********-0001      │
├─────────────────────────────────────────┤
│ To: Customer ABC Warehouse (12345)      │
│ Date: July 24, 2024                    │
├─────────────────────────────────────────┤
│ Items:                                  │
│ • Widget A (SKU-001): 25 Each          │
│   From: PLT-2024-001                   │
│ • Widget B (SKU-002): 10 Each          │
│   From: PLT-2024-002                   │
├─────────────────────────────────────────┤
│ Pallets:                                │
│ • PLT-2024-005: Mixed items (3 types)  │
├─────────────────────────────────────────┤
│ Total: 4 item types, 35 units, 1 pallet│
│ [🖨️ Print] [📱 Download] [✅ Mark Packed]│
└─────────────────────────────────────────┘
```

### Loading States and Error Handling

**Loading State Patterns:**

- Skeleton loaders for search results (following existing patterns)
- Shimmer effects for location lists during data fetching
- Progress indicators for shipment creation and status updates
- Optimistic UI updates with immediate visual feedback

**Error Handling from Warehouse Employee Perspective:**

**Item Not Found:**

```
┌─────────────────────────────────────────┐
│ ⚠️ Item Not Found                       │
│ "Widget XYZ" doesn't exist in this     │
│ warehouse. Check the item name or SKU.  │
│ [Try Again] [Browse Items]              │
└─────────────────────────────────────────┘
```

**Insufficient Inventory:**

```
┌─────────────────────────────────────────┐
│ ❌ Not Enough Inventory                 │
│ Only 15 units of Widget A available.   │
│ You requested 25 units.                │
│ [Pick 15 units] [Find More Locations]  │
└─────────────────────────────────────────┘
```

**Network Connection Issues:**

```
┌─────────────────────────────────────────┐
│ 📶 Connection Problem                   │
│ Can't connect to server. Your changes  │
│ are saved locally and will sync when   │
│ connection is restored.                 │
│ [Retry Now] [Work Offline]              │
└─────────────────────────────────────────┘
```

### Integration with Existing Confetti Animations

**Completion Workflows:**

- Shipment creation: 2-second confetti burst with green colors
- Packing completion: Gold confetti with package icons
- Inventory release: Blue confetti with shipping truck icons
- Item picking milestones: Quick sparkle effects for each item picked

**Animation Triggers:**

```typescript
import { triggerConfetti } from "@/lib/confetti";

const handleShipmentCreated = () => {
  triggerConfetti({
    particleCount: 100,
    spread: 70,
    origin: { y: 0.6 },
    colors: ["#10B981", "#34D399", "#6EE7B7"],
  });

  setTimeout(() => {
    router.push(`/shipments/${shipment.id}`);
  }, 2000);
};
```

### Expansion into Advanced Picking and Kitting

**Future Enhancement Pathways:**

The item-based picking foundation enables natural expansion into advanced features:

**Cross-Pallet Kitting:**

- ItemLocationList already shows multiple pallet locations per item
- ShipmentCreationModal can aggregate items from different pallets
- Database schema supports sourcePalletId tracking for audit trails

**Job Site Management:**

- OutgoingShipment entity can be extended with jobSiteId field
- Shipment grouping by project/job site for complex orders
- Multi-destination shipments for large construction projects

**Pick Path Optimization:**

- ItemLocationList provides location data for route planning
- Integration with existing Location entity for warehouse layout
- Mobile-first navigation with turn-by-turn picking directions

**Batch Picking:**

- Multiple shipments can share common items for efficiency
- Pick once, distribute to multiple shipments workflow
- Warehouse employee productivity optimization

## 7. Security Considerations

### Authentication Flow

**Supabase Auth Integration:**
All new endpoints leverage existing authentication patterns:

```typescript
// Existing pattern maintained
@UseGuards(JwtAuthGuard, WarehousePermissionGuard)
@Controller("items")
export class ItemsController {
  @Get("search")
  @RequireWarehouseAccess()
  async searchItems(@Req() req: RequestWithWarehouseContext) {
    // Warehouse context automatically validated
    const { warehouseId } = req.warehouseContext;
    return this.itemsService.search(req.query, warehouseId);
  }
}
```

**Token Validation:**

- JWT tokens validated on every request
- Warehouse context extracted from user permissions
- Automatic token refresh handled by frontend auth provider

### Authorization Matrix

**Role-Based Access Control:**

| Operation              | WAREHOUSE_MEMBER    | WAREHOUSE_MANAGER   | TENANT_ADMIN       |
| ---------------------- | ------------------- | ------------------- | ------------------ |
| Search Items           | ✅ Read-only        | ✅ Full access      | ✅ Full access     |
| View Item Locations    | ✅ Warehouse-scoped | ✅ Warehouse-scoped | ✅ All warehouses  |
| Create Shipments       | ✅ Basic shipments  | ✅ All shipments    | ✅ All shipments   |
| Generate Packing Lists | ✅ View/Print       | ✅ View/Print/Edit  | ✅ View/Print/Edit |
| Release Inventory      | ❌ No access        | ✅ Warehouse-scoped | ✅ All warehouses  |
| Cancel Shipments       | ❌ No access        | ✅ Warehouse-scoped | ✅ All warehouses  |
| View Audit Logs        | ❌ No access        | ✅ Warehouse-scoped | ✅ All warehouses  |

**Warehouse-Scoped Data Access:**

```typescript
// Automatic warehouse filtering in service layer
async findItemLocations(itemId: string, warehouseId: string) {
  return this.prisma.itemLocation.findMany({
    where: {
      itemId,
      pallet: {
        location: {
          warehouseId // Automatic warehouse scoping
        }
      }
    },
    include: {
      pallet: {
        include: {
          location: true
        }
      }
    }
  });
}
```

### Data Validation Rules

**Input Sanitization:**

**Item Search Validation:**

```typescript
class ItemSearchDto {
  @IsString()
  @Length(1, 100)
  @Matches(/^[a-zA-Z0-9\s\-_]+$/) // Alphanumeric, spaces, hyphens, underscores only
  q: string;

  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number = 20;

  @IsOptional()
  @IsInt()
  @Min(0)
  offset?: number = 0;
}
```

**Shipment Creation Validation:**

```typescript
class CreateShipmentDto {
  @IsString()
  @Length(1, 255)
  destination: string;

  @IsOptional()
  @IsString()
  @Matches(/^\d+$/) // Numeric destination codes only
  destinationCode?: string;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ShipmentItemDto)
  items: ShipmentItemDto[];

  @IsOptional()
  @IsString()
  @Length(0, 1000)
  notes?: string;
}

class ShipmentItemDto {
  @IsString()
  @IsUUID()
  itemId: string;

  @IsInt()
  @Min(1)
  @Max(10000)
  quantity: number;

  @IsOptional()
  @IsString()
  @IsUUID()
  sourcePalletId?: string;
}
```

**Cross-Warehouse Data Prevention:**

```typescript
// Service-level validation
async validateWarehouseAccess(itemId: string, warehouseId: string) {
  const itemLocations = await this.prisma.itemLocation.findMany({
    where: {
      itemId,
      pallet: {
        location: {
          warehouseId
        }
      }
    }
  });

  if (itemLocations.length === 0) {
    throw new ForbiddenException(
      'Item not available in specified warehouse'
    );
  }
}
```

### Audit Logging Integration

**New Audit Actions:**

```typescript
// Extended audit action types
export enum AuditAction {
  // Existing actions...
  SEARCH_ITEMS = "SEARCH_ITEMS",
  VIEW_ITEM_LOCATIONS = "VIEW_ITEM_LOCATIONS",
  CREATE_SHIPMENT = "CREATE_SHIPMENT",
  ADD_ITEMS_TO_SHIPMENT = "ADD_ITEMS_TO_SHIPMENT",
  UPDATE_SHIPMENT_STATUS = "UPDATE_SHIPMENT_STATUS",
  GENERATE_PACKING_LIST = "GENERATE_PACKING_LIST",
  RELEASE_INVENTORY = "RELEASE_INVENTORY",
  CANCEL_SHIPMENT = "CANCEL_SHIPMENT",
}
```

**Detailed Audit Logging:**

```typescript
@LogAction({
  action: 'CREATE_SHIPMENT',
  entity: 'OutgoingShipment',
  getEntityId: (context, result) => result?.id,
  getDetails: (context, result) => {
    const request = context.switchToHttp().getRequest();
    return {
      destination: result?.destination,
      destinationCode: result?.destinationCode,
      itemCount: result?.shipmentItems?.length || 0,
      palletCount: result?.shipmentPallets?.length || 0,
      warehouseId: request.warehouseContext?.warehouseId,
      userRole: request.warehouseContext?.userRole
    };
  }
})
```

**Sensitive Data Protection:**

- Personal information excluded from audit logs
- Warehouse context included for security analysis
- User actions tracked with role information
- IP address and timestamp for security monitoring

## 8. Testing Strategy

### Unit Test Requirements

**Backend API Endpoints:**

```typescript
// Example test structure for item search endpoint
describe("ItemsController", () => {
  describe("GET /items/search", () => {
    it("should return items matching search query", async () => {
      // Arrange
      const mockItems = [{ id: "1", name: "Widget A", sku: "SKU-001" }];
      jest.spyOn(itemsService, "search").mockResolvedValue(mockItems);

      // Act
      const result = await controller.searchItems(mockRequest, {
        q: "Widget",
        limit: 20,
        offset: 0,
      });

      // Assert
      expect(result).toEqual(mockItems);
      expect(itemsService.search).toHaveBeenCalledWith(
        { q: "Widget", limit: 20, offset: 0 },
        "warehouse-id"
      );
    });

    it("should enforce warehouse access control", async () => {
      // Test warehouse scoping
      const unauthorizedRequest = {
        ...mockRequest,
        warehouseContext: { warehouseId: "different-warehouse" },
      };

      await expect(
        controller.searchItems(unauthorizedRequest, { q: "Widget" })
      ).rejects.toThrow(ForbiddenException);
    });
  });
});
```

**React Component Testing:**

```typescript
// Example test for ItemSearchInput component
describe("ItemSearchInput", () => {
  it("should debounce search input", async () => {
    const mockOnItemSelect = jest.fn();
    render(<ItemSearchInput onItemSelect={mockOnItemSelect} />);

    const searchInput = screen.getByPlaceholderText("Search items...");

    // Type rapidly
    fireEvent.change(searchInput, { target: { value: "W" } });
    fireEvent.change(searchInput, { target: { value: "Wi" } });
    fireEvent.change(searchInput, { target: { value: "Widget" } });

    // Should only trigger search after debounce delay
    expect(mockSearchAPI).not.toHaveBeenCalled();

    await waitFor(
      () => {
        expect(mockSearchAPI).toHaveBeenCalledTimes(1);
        expect(mockSearchAPI).toHaveBeenCalledWith("Widget");
      },
      { timeout: 400 }
    );
  });

  it("should handle keyboard navigation", async () => {
    render(<ItemSearchInput onItemSelect={mockOnItemSelect} />);

    // Type to show results
    fireEvent.change(searchInput, { target: { value: "Widget" } });
    await waitFor(() => screen.getByText("Widget A"));

    // Navigate with arrow keys
    fireEvent.keyDown(searchInput, { key: "ArrowDown" });
    expect(screen.getByText("Widget A")).toHaveClass("highlighted");

    // Select with Enter
    fireEvent.keyDown(searchInput, { key: "Enter" });
    expect(mockOnItemSelect).toHaveBeenCalledWith(mockWidgetA);
  });
});
```

### Integration Test Scenarios

**Warehouse-Scoped Access Control:**

```typescript
describe("Warehouse Access Control Integration", () => {
  it("should prevent cross-warehouse data access", async () => {
    // Setup: Create items in different warehouses
    const warehouse1 = await createTestWarehouse("Warehouse 1");
    const warehouse2 = await createTestWarehouse("Warehouse 2");

    const item1 = await createTestItem(warehouse1.id, "Widget A");
    const item2 = await createTestItem(warehouse2.id, "Widget B");

    // Test: User with warehouse1 access should not see warehouse2 items
    const user = await createTestUser([warehouse1.id]);
    const token = generateTestToken(user);

    const response = await request(app)
      .get("/api/items/search?q=Widget")
      .set("Authorization", `Bearer ${token}`)
      .set("X-Warehouse-ID", warehouse1.id);

    expect(response.status).toBe(200);
    expect(response.body.items).toHaveLength(1);
    expect(response.body.items[0].id).toBe(item1.id);
    expect(response.body.items).not.toContainEqual(
      expect.objectContaining({ id: item2.id })
    );
  });
});
```

**Complete Pick-Pack-Ship Workflow:**

```typescript
describe("Pick-Pack-Ship Workflow Integration", () => {
  it("should complete full workflow from item search to shipment", async () => {
    // Setup test data
    const warehouse = await createTestWarehouse();
    const item = await createTestItem(warehouse.id);
    const pallet = await createTestPallet(warehouse.id);
    await createTestItemLocation(item.id, pallet.id, 100);

    const user = await createTestUser([warehouse.id]);
    const token = generateTestToken(user);

    // Step 1: Search for item
    const searchResponse = await request(app)
      .get(`/api/items/search?q=${item.name}`)
      .set("Authorization", `Bearer ${token}`)
      .set("X-Warehouse-ID", warehouse.id);

    expect(searchResponse.status).toBe(200);
    expect(searchResponse.body.items[0].id).toBe(item.id);

    // Step 2: Get item locations
    const locationsResponse = await request(app)
      .get(`/api/items/${item.id}/locations`)
      .set("Authorization", `Bearer ${token}`)
      .set("X-Warehouse-ID", warehouse.id);

    expect(locationsResponse.status).toBe(200);
    expect(locationsResponse.body.locations[0].palletId).toBe(pallet.id);

    // Step 3: Create shipment
    const shipmentResponse = await request(app)
      .post("/api/shipments")
      .set("Authorization", `Bearer ${token}`)
      .set("X-Warehouse-ID", warehouse.id)
      .send({
        destination: "Test Customer",
        items: [{ itemId: item.id, quantity: 25, sourcePalletId: pallet.id }],
      });

    expect(shipmentResponse.status).toBe(201);
    const shipmentId = shipmentResponse.body.id;

    // Step 4: Generate packing list
    const packingListResponse = await request(app)
      .get(`/api/shipments/${shipmentId}/packing-list`)
      .set("Authorization", `Bearer ${token}`)
      .set("X-Warehouse-ID", warehouse.id);

    expect(packingListResponse.status).toBe(200);
    expect(packingListResponse.body.items[0].quantity).toBe(25);

    // Step 5: Update status to packed
    const packedResponse = await request(app)
      .put(`/api/shipments/${shipmentId}/status`)
      .set("Authorization", `Bearer ${token}`)
      .set("X-Warehouse-ID", warehouse.id)
      .send({ status: "PACKED" });

    expect(packedResponse.status).toBe(200);

    // Step 6: Release inventory
    const releaseResponse = await request(app)
      .post(`/api/shipments/${shipmentId}/release`)
      .set("Authorization", `Bearer ${token}`)
      .set("X-Warehouse-ID", warehouse.id)
      .send({ releaseNotes: "Test release" });

    expect(releaseResponse.status).toBe(200);
    expect(releaseResponse.body.status).toBe("SHIPPED");

    // Verify inventory was updated
    const updatedLocations = await request(app)
      .get(`/api/items/${item.id}/locations`)
      .set("Authorization", `Bearer ${token}`)
      .set("X-Warehouse-ID", warehouse.id);

    expect(updatedLocations.body.locations[0].quantity).toBe(75); // 100 - 25
  });
});
```

### End-to-End Test Flows

**Mobile Device Testing:**

```typescript
// Playwright E2E tests for mobile workflows
describe("Mobile Pick-Pack-Ship Workflow", () => {
  test("should complete picking workflow on tablet", async ({ page }) => {
    // Setup mobile viewport
    await page.setViewportSize({ width: 768, height: 1024 });

    // Login and navigate to picking
    await page.goto("/auth/login");
    await page.fill("[data-testid=email]", "<EMAIL>");
    await page.fill("[data-testid=password]", "password");
    await page.click("[data-testid=login-button]");

    await page.goto("/picking");

    // Switch to item-based picking
    await page.click("[data-testid=item-picking-tab]");

    // Search for item
    await page.fill("[data-testid=item-search]", "Widget A");
    await page.waitForSelector("[data-testid=search-results]");
    await page.click("[data-testid=item-result-0]");

    // View locations
    await page.click("[data-testid=view-locations]");
    await page.waitForSelector("[data-testid=location-list]");

    // Pick from first location
    await page.click("[data-testid=pick-from-location-0]");
    await page.fill("[data-testid=pick-quantity]", "25");
    await page.click("[data-testid=confirm-pick]");

    // Verify item added to cart
    await expect(page.locator("[data-testid=cart-item-count]")).toHaveText("1");

    // Create shipment
    await page.click("[data-testid=create-shipment]");
    await page.fill("[data-testid=destination]", "Test Customer");
    await page.click("[data-testid=create-shipment-confirm]");

    // Verify confetti animation
    await expect(page.locator("[data-testid=confetti]")).toBeVisible();

    // Verify navigation to shipment details
    await page.waitForURL(/\/shipments\/.*$/);
    await expect(page.locator("[data-testid=shipment-status]")).toHaveText(
      "PREPARING"
    );
  });
});
```

**Performance Testing:**

```typescript
describe("Performance Tests", () => {
  test("item search should respond within 200ms", async () => {
    const startTime = Date.now();

    const response = await request(app)
      .get("/api/items/search?q=Widget")
      .set("Authorization", `Bearer ${token}`)
      .set("X-Warehouse-ID", warehouseId);

    const responseTime = Date.now() - startTime;

    expect(response.status).toBe(200);
    expect(responseTime).toBeLessThan(200);
  });

  test("should handle 100 concurrent item searches", async () => {
    const promises = Array.from({ length: 100 }, () =>
      request(app)
        .get("/api/items/search?q=Widget")
        .set("Authorization", `Bearer ${token}`)
        .set("X-Warehouse-ID", warehouseId)
    );

    const responses = await Promise.all(promises);

    responses.forEach((response) => {
      expect(response.status).toBe(200);
    });
  });
});
```

## 9. Data Management

### Caching Strategies

**Item Search Results:**

```typescript
// React Query configuration for item search
const useItemSearch = (searchTerm: string, warehouseId: string) => {
  return useSuspenseQuery({
    queryKey: ["items", "search", warehouseId, searchTerm],
    queryFn: () => searchItems(searchTerm, warehouseId),
    staleTime: 5 * 60 * 1000, // 5 minutes for item data
    gcTime: 10 * 60 * 1000, // 10 minutes garbage collection
    enabled: searchTerm.length >= 2, // Only search with 2+ characters
  });
};

// Backend Redis caching for frequently searched items
@Injectable()
export class ItemsService {
  async searchItems(query: string, warehouseId: string) {
    const cacheKey = `items:search:${warehouseId}:${query}`;

    // Check cache first
    const cached = await this.redis.get(cacheKey);
    if (cached) {
      return JSON.parse(cached);
    }

    // Query database
    const results = await this.prisma.item.findMany({
      where: {
        AND: [
          { tenantId: user.tenantId },
          {
            OR: [
              { name: { contains: query, mode: "insensitive" } },
              { sku: { contains: query, mode: "insensitive" } },
            ],
          },
        ],
      },
      include: {
        itemLocations: {
          where: {
            pallet: {
              location: { warehouseId },
            },
          },
        },
      },
    });

    // Cache for 5 minutes
    await this.redis.setex(cacheKey, 300, JSON.stringify(results));
    return results;
  }
}
```

**Location Data Caching:**

```typescript
// Aggressive caching for item locations (changes less frequently)
const useItemLocations = (itemId: string, warehouseId: string) => {
  return useSuspenseQuery({
    queryKey: ["items", itemId, "locations", warehouseId],
    queryFn: () => getItemLocations(itemId, warehouseId),
    staleTime: 15 * 60 * 1000, // 15 minutes for location data
    gcTime: 30 * 60 * 1000, // 30 minutes garbage collection
  });
};
```

**Shipment Status Caching:**

```typescript
// Real-time updates for shipment status
const useShipmentStatus = (shipmentId: string) => {
  return useSuspenseQuery({
    queryKey: ["shipments", shipmentId, "status"],
    queryFn: () => getShipmentStatus(shipmentId),
    staleTime: 30 * 1000, // 30 seconds for status data
    refetchInterval: 60 * 1000, // Poll every minute for status updates
  });
};
```

### Pagination Implementation

**Offset-Based Pagination for Simplicity:**

```typescript
interface PaginationParams {
  limit: number; // Default: 20, Max: 100
  offset: number; // Default: 0
}

interface PaginatedResponse<T> {
  items: T[];
  pagination: {
    total: number;
    limit: number;
    offset: number;
    hasMore: boolean;
  };
}

// Backend implementation
async searchItemsPaginated(
  query: string,
  warehouseId: string,
  pagination: PaginationParams
): Promise<PaginatedResponse<Item>> {
  const [items, total] = await Promise.all([
    this.prisma.item.findMany({
      where: this.buildSearchWhere(query, warehouseId),
      skip: pagination.offset,
      take: pagination.limit,
      orderBy: { name: 'asc' }
    }),
    this.prisma.item.count({
      where: this.buildSearchWhere(query, warehouseId)
    })
  ]);

  return {
    items,
    pagination: {
      total,
      limit: pagination.limit,
      offset: pagination.offset,
      hasMore: pagination.offset + pagination.limit < total
    }
  };
}
```

**Frontend Infinite Scroll for Large Datasets:**

```typescript
const useInfiniteItemSearch = (searchTerm: string, warehouseId: string) => {
  return useInfiniteQuery({
    queryKey: ["items", "search", "infinite", warehouseId, searchTerm],
    queryFn: ({ pageParam = 0 }) =>
      searchItemsPaginated(searchTerm, warehouseId, {
        limit: 20,
        offset: pageParam,
      }),
    getNextPageParam: (lastPage) =>
      lastPage.pagination.hasMore
        ? lastPage.pagination.offset + lastPage.pagination.limit
        : undefined,
    enabled: searchTerm.length >= 2,
  });
};
```

### Real-Time Data Requirements

**Inventory Level Updates:**

```typescript
// WebSocket integration for real-time inventory updates
@WebSocketGateway({
  cors: { origin: process.env.FRONTEND_URL },
  namespace: "/warehouse",
})
export class WarehouseGateway {
  @SubscribeMessage("subscribe-inventory")
  handleInventorySubscription(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { warehouseId: string; itemIds: string[] }
  ) {
    // Subscribe client to inventory updates for specific items
    client.join(`inventory:${data.warehouseId}`);
    data.itemIds.forEach((itemId) => {
      client.join(`item:${itemId}:inventory`);
    });
  }

  // Emit inventory updates when items are picked/shipped
  async notifyInventoryUpdate(
    itemId: string,
    warehouseId: string,
    newQuantity: number
  ) {
    this.server.to(`item:${itemId}:inventory`).emit("inventory-updated", {
      itemId,
      warehouseId,
      newQuantity,
      timestamp: new Date().toISOString(),
    });
  }
}
```

**Shipment Status Updates:**

```typescript
// Real-time shipment status broadcasting
async updateShipmentStatus(shipmentId: string, status: ShipmentStatus) {
  const updatedShipment = await this.prisma.outgoingShipment.update({
    where: { id: shipmentId },
    data: {
      status,
      ...(status === 'PACKED' && { packedAt: new Date() }),
      ...(status === 'SHIPPED' && { shippedAt: new Date() })
    }
  });

  // Broadcast to all clients watching this shipment
  this.warehouseGateway.server
    .to(`shipment:${shipmentId}`)
    .emit('shipment-status-updated', {
      shipmentId,
      status,
      timestamp: updatedShipment.updatedAt
    });

  return updatedShipment;
}
```

### Data Lifecycle Policies

**Completed Shipment Archival:**

```typescript
// Archive shipments after 90 days of completion
@Cron('0 2 * * *') // Daily at 2 AM
async archiveCompletedShipments() {
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - 90);

  const shipmentsToArchive = await this.prisma.outgoingShipment.findMany({
    where: {
      status: 'SHIPPED',
      shippedAt: { lt: cutoffDate }
    }
  });

  for (const shipment of shipmentsToArchive) {
    // Move to archive table
    await this.prisma.archivedShipment.create({
      data: {
        ...shipment,
        archivedAt: new Date()
      }
    });

    // Remove from active table
    await this.prisma.outgoingShipment.delete({
      where: { id: shipment.id }
    });
  }

  this.logger.log(`Archived ${shipmentsToArchive.length} completed shipments`);
}
```

**Audit Log Retention:**

```typescript
// Retain audit logs for 7 years for compliance
@Cron('0 3 * * 0') // Weekly on Sunday at 3 AM
async cleanupAuditLogs() {
  const retentionDate = new Date();
  retentionDate.setFullYear(retentionDate.getFullYear() - 7);

  const deletedCount = await this.prisma.auditLog.deleteMany({
    where: {
      timestamp: { lt: retentionDate }
    }
  });

  this.logger.log(`Cleaned up ${deletedCount.count} old audit log entries`);
}
```

## 10. Error Handling & Logging

### Structured Logging Format

**Audit Log Integration:**

```typescript
// Extended audit log structure for new operations
interface AuditLogEntry {
  id: string;
  timestamp: Date;
  userId: string;
  userEmail: string;
  action: AuditAction;
  entity: string;
  entityId: string;
  tenantId: string;
  warehouseId?: string; // NEW: Warehouse context
  details: {
    // Standard fields
    before?: any;
    after?: any;

    // Item-specific fields
    itemId?: string;
    itemName?: string;
    quantity?: number;
    sourcePalletId?: string;

    // Shipment-specific fields
    shipmentNumber?: string;
    destination?: string;
    destinationCode?: string;
    shipmentStatus?: ShipmentStatus;

    // Context fields
    userRole?: Role;
    ipAddress?: string;
    userAgent?: string;
  };
}
```

**Structured Application Logging:**

```typescript
// Winston logger configuration for structured logs
const logger = winston.createLogger({
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: {
    service: "quildora-api",
    version: process.env.APP_VERSION,
  },
  transports: [
    new winston.transports.File({
      filename: "logs/error.log",
      level: "error",
    }),
    new winston.transports.File({
      filename: "logs/combined.log",
    }),
  ],
});

// Structured logging in services
export class ItemsService {
  async searchItems(
    query: string,
    warehouseId: string,
    user: EnhancedUserPayload
  ) {
    const startTime = Date.now();

    try {
      logger.info("Item search initiated", {
        operation: "SEARCH_ITEMS",
        userId: user.id,
        warehouseId,
        query: query.substring(0, 50), // Truncate for privacy
        userRole: user.role,
      });

      const results = await this.performSearch(query, warehouseId);

      logger.info("Item search completed", {
        operation: "SEARCH_ITEMS",
        userId: user.id,
        warehouseId,
        resultCount: results.length,
        duration: Date.now() - startTime,
      });

      return results;
    } catch (error) {
      logger.error("Item search failed", {
        operation: "SEARCH_ITEMS",
        userId: user.id,
        warehouseId,
        error: error.message,
        stack: error.stack,
        duration: Date.now() - startTime,
      });
      throw error;
    }
  }
}
```

### Error Classification

**Business Logic Errors:**

```typescript
// Custom exception classes for business logic
export class InsufficientInventoryException extends BadRequestException {
  constructor(itemId: string, requested: number, available: number) {
    super({
      code: "INSUFFICIENT_INVENTORY",
      message: `Not enough inventory available`,
      details: {
        itemId,
        requestedQuantity: requested,
        availableQuantity: available,
      },
    });
  }
}

export class InvalidShipmentStatusException extends BadRequestException {
  constructor(currentStatus: ShipmentStatus, attemptedStatus: ShipmentStatus) {
    super({
      code: "INVALID_STATUS_TRANSITION",
      message: `Cannot change shipment status from ${currentStatus} to ${attemptedStatus}`,
      details: {
        currentStatus,
        attemptedStatus,
        validTransitions: this.getValidTransitions(currentStatus),
      },
    });
  }

  private getValidTransitions(status: ShipmentStatus): ShipmentStatus[] {
    const transitions = {
      PREPARING: ["PACKED", "CANCELLED"],
      PACKED: ["SHIPPED", "CANCELLED"],
      SHIPPED: ["DELIVERED"],
      DELIVERED: [],
      CANCELLED: [],
    };
    return transitions[status] || [];
  }
}
```

**Validation Errors:**

```typescript
// Comprehensive validation with user-friendly messages
export class ValidationErrorHandler {
  static formatValidationErrors(errors: ValidationError[]): string {
    return errors
      .map((error) => {
        const constraints = Object.values(error.constraints || {});
        return constraints.join(", ");
      })
      .join("; ");
  }

  static createUserFriendlyMessage(error: ValidationError): string {
    const field = error.property;
    const value = error.value;

    // Map technical validation to user-friendly messages
    const friendlyMessages = {
      isNotEmpty: `${field} is required`,
      isInt: `${field} must be a whole number`,
      min: `${field} must be at least ${error.constraints?.min}`,
      max: `${field} cannot exceed ${error.constraints?.max}`,
      isUUID: `Invalid ${field} format`,
      matches: `${field} contains invalid characters`,
    };

    const constraint = Object.keys(error.constraints || {})[0];
    return friendlyMessages[constraint] || `${field} is invalid`;
  }
}
```

### User-Friendly Error Messages

**Warehouse Employee Error Messages:**

```typescript
// Error message mapping for warehouse operations
export const WarehouseErrorMessages = {
  ITEM_NOT_FOUND: {
    title: "Item Not Found",
    message:
      "This item doesn't exist in your warehouse. Check the item name or SKU.",
    actions: ["Try Again", "Browse Items"],
  },

  INSUFFICIENT_INVENTORY: {
    title: "Not Enough Inventory",
    message: (available: number, requested: number) =>
      `Only ${available} units available. You requested ${requested} units.`,
    actions: ["Pick Available Amount", "Find More Locations"],
  },

  PALLET_NOT_FOUND: {
    title: "Pallet Not Found",
    message: "This pallet barcode doesn't exist in your warehouse.",
    actions: ["Scan Again", "Enter Manually"],
  },

  NETWORK_ERROR: {
    title: "Connection Problem",
    message:
      "Can't connect to server. Your changes are saved locally and will sync when connection is restored.",
    actions: ["Retry Now", "Work Offline"],
  },

  SHIPMENT_ALREADY_SHIPPED: {
    title: "Shipment Already Shipped",
    message:
      "This shipment has already been marked as shipped and cannot be modified.",
    actions: ["View Shipment Details", "Create New Shipment"],
  },
};

// Frontend error display component
export const ErrorDisplay: React.FC<{ error: ApiError }> = ({ error }) => {
  const errorConfig = WarehouseErrorMessages[error.code] || {
    title: "Something Went Wrong",
    message: error.message,
    actions: ["Try Again"],
  };

  return (
    <Alert variant="destructive" className="mb-4">
      <AlertCircle className="h-4 w-4" />
      <AlertTitle>{errorConfig.title}</AlertTitle>
      <AlertDescription className="mb-3">
        {typeof errorConfig.message === "function"
          ? errorConfig.message(...error.details)
          : errorConfig.message}
      </AlertDescription>
      <div className="flex gap-2">
        {errorConfig.actions.map((action, index) => (
          <Button
            key={index}
            variant={index === 0 ? "default" : "outline"}
            size="sm"
            onClick={() => handleErrorAction(action, error)}
          >
            {action}
          </Button>
        ))}
      </div>
    </Alert>
  );
};
```

### Recovery Mechanisms

**Failed Shipment Operations:**

```typescript
// Automatic rollback for failed shipment creation
export class ShipmentService {
  async createShipmentWithRollback(
    data: CreateShipmentDto,
    user: EnhancedUserPayload
  ) {
    const rollbackActions: (() => Promise<void>)[] = [];

    try {
      // Step 1: Reserve inventory
      const reservations = await this.reserveInventory(data.items);
      rollbackActions.push(() => this.releaseReservations(reservations));

      // Step 2: Create shipment
      const shipment = await this.createShipmentRecord(data, user);
      rollbackActions.push(() => this.deleteShipmentRecord(shipment.id));

      // Step 3: Create shipment items
      await this.createShipmentItems(shipment.id, data.items);

      return shipment;
    } catch (error) {
      // Execute rollback actions in reverse order
      for (const rollback of rollbackActions.reverse()) {
        try {
          await rollback();
        } catch (rollbackError) {
          this.logger.error("Rollback failed", {
            originalError: error.message,
            rollbackError: rollbackError.message,
          });
        }
      }
      throw error;
    }
  }
}
```

**Inventory Discrepancy Resolution:**

```typescript
// Handle inventory discrepancies during picking
export class InventoryDiscrepancyService {
  async handlePickingDiscrepancy(
    itemId: string,
    palletId: string,
    expectedQuantity: number,
    actualQuantity: number,
    user: EnhancedUserPayload
  ) {
    // Log discrepancy for investigation
    await this.auditLogService.log({
      action: "INVENTORY_DISCREPANCY_DETECTED",
      entity: "ItemLocation",
      entityId: `${itemId}-${palletId}`,
      userId: user.id,
      tenantId: user.tenantId,
      details: {
        itemId,
        palletId,
        expectedQuantity,
        actualQuantity,
        discrepancy: actualQuantity - expectedQuantity,
      },
    });

    // Update inventory to actual count
    await this.prisma.itemLocation.update({
      where: {
        itemId_palletId: { itemId, palletId },
      },
      data: {
        quantity: actualQuantity,
        lastUpdated: new Date(),
      },
    });

    // Notify warehouse managers
    await this.notificationService.sendDiscrepancyAlert(
      itemId,
      palletId,
      expectedQuantity,
      actualQuantity
    );

    return {
      adjustedQuantity: actualQuantity,
      discrepancyLogged: true,
      managersNotified: true,
    };
  }
}
```

---

## Summary

This comprehensive Product Requirements Document provides detailed specifications for implementing Item-Based Picking and Outgoing Shipment Management features in Quildora's warehouse management system. The features are designed to:

**Create Competitive Advantage:**

- Complete pick-pack-ship workflow differentiates from Oracle WMS basic tracking
- Mobile-first design optimized for warehouse floor operations
- Professional packing lists and shipment management for customer satisfaction

**Maintain Quildora Architecture:**

- Full integration with existing warehouse-scoped access control
- Leverages established patterns (@RequireWarehouseAccess, @LogAction, React Query)
- Extends current database schema with backward compatibility
- Preserves mobile optimization and performance targets

**Enable Future Growth:**

- Foundation for advanced picking and kitting features
- Scalable database design for multi-tenant SaaS expansion
- API-first approach for future integrations
- Comprehensive audit logging for compliance and analytics

The 20 tasks outlined in Week 1 of the MVP roadmap provide a focused, achievable implementation plan that transforms Quildora from basic pallet tracking into a complete fulfillment solution capable of competing with enterprise warehouse management systems while maintaining the simplicity and cost-effectiveness that appeals to SMB customers.
