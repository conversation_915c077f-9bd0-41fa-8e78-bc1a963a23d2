# Quildora Picking Workflows Guide

## Overview

Quildora now supports multiple picking workflows to handle different warehouse scenarios efficiently. This guide covers all available picking methods and when to use each one.

## Available Workflows

### 1. **Pick → Release** (Direct Item Release)
**Best for:** Local pickup, stock pulls, emergency releases, contractor supplies

**Use Case:** When items need to be released directly to local crews or contractors without creating a formal shipment.

**Steps:**
1. Navigate to **Picking** from the main menu
2. Search for items using the search bar
3. Select items from different pallets as needed
4. Add items to your cart with desired quantities
5. Click **"Release Items Directly"** (orange button)
6. Enter who the items are being released to
7. Add optional notes (e.g., "Emergency stock pull for production line")
8. Click **"Release Items"**
9. Enjoy the confetti celebration! 🎉

**Example Scenario:**
> Production line needs 50 widgets urgently. Search for "widget", select from available pallets, add 50 to cart, and release directly to "Production Team A" with note "Emergency line repair".

---

### 2. **Pick → Pack → Ship** (Traditional Shipment)
**Best for:** Standard customer orders, freight shipments, individual item fulfillment

**Use Case:** When you need to pick specific items from various pallets to fulfill a customer order.

**Steps:**
1. Navigate to **Picking** from the main menu
2. Search for items using the search bar
3. View available locations for each item
4. Add items to cart from their source pallets
5. Review cart contents and quantities
6. Click **"Ship Items"** (green button)
7. Enter destination name and optional destination code
8. Click **"Create Shipment"**
9. View the generated packing list
10. Update shipment status as you pack
11. Release inventory when ready to ship

**Example Scenario:**
> Customer ABC ordered 25 widgets and 10 gadgets. Search for each item, add quantities from available pallets, create shipment to "Customer ABC" with code "12345", pack items, and ship.

---

### 3. **Pick → Pack → Ship with Pallets** (Enhanced Mixed Shipment)
**Best for:** Large orders combining individual items with complete pallets

**Use Case:** When an order includes both specific item quantities and complete pallets that are already destined for the same customer.

**Steps:**
1. Navigate to **Picking** from the main menu
2. Search and add individual items to cart as needed
3. Click **"Add Pallets"** (blue button) for enhanced shipment creation
4. Switch to the **"Pallets"** tab
5. Select complete pallets that match your destination
6. Switch back to **"Items"** tab to review individual items
7. Enter destination information
8. Click **"Create Shipment"**
9. Process the mixed shipment through packing and shipping

**Example Scenario:**
> Customer XYZ needs 15 specific widgets plus 2 complete pallets already labeled for them. Add individual widgets to cart, then use enhanced shipment to include both the picked items and complete pallets in one shipment.

---

## Navigation and Interface

### Main Picking Screen
- **Search Bar:** Type item names or SKUs to find products
- **Item Results:** Shows available items with stock levels
- **Location Details:** Click any item to see all pallet locations
- **Cart Summary:** Always visible on the right side

### Cart Management
- **Quantity Adjustment:** Use +/- buttons or type directly
- **Remove Items:** Click the trash icon
- **Source Information:** See which pallet each item came from
- **Running Total:** Track total items in cart

### Action Buttons
- **🟠 Release Items Directly:** For local pickup/stock pulls
- **🟢 Ship Items:** For traditional customer shipments  
- **🔵 Add Pallets:** For mixed shipments with complete pallets

## Mobile/Tablet Optimization

All workflows are optimized for warehouse floor use:
- **Large Touch Targets:** 44px+ buttons for easy tapping
- **Clear Visual Hierarchy:** Important information stands out
- **Responsive Design:** Works on phones, tablets, and desktops
- **Offline Resilience:** Cart persists during network issues

## Best Practices

### Item Search
- Use partial names or SKUs for faster results
- Search results are filtered to your current warehouse
- All data loads instantly (no waiting for search results)

### Quantity Management
- Double-check quantities before releasing/shipping
- System validates against available inventory
- Source pallet information helps with traceability

### Destination Management
- Use destination codes for better organization
- Autocomplete suggests previously used destinations
- Codes help with routing and delivery tracking

### Workflow Selection
- **Direct Release:** No paperwork needed, immediate action
- **Traditional Shipment:** Full documentation and tracking
- **Mixed Shipment:** Best for complex orders with multiple components

## Troubleshooting

### Common Issues

**"Item not found"**
- Verify item exists in your warehouse
- Check spelling of item name/SKU
- Ensure you're in the correct warehouse context

**"Insufficient inventory"**
- Check available quantities on each pallet
- Consider splitting picks across multiple pallets
- Verify pallet status (must be "Stored")

**"Destination required"**
- All shipments need a destination name
- Destination codes are optional but recommended
- Use clear, descriptive destination names

### Getting Help
- Contact your warehouse manager for process questions
- Check with IT support for technical issues
- Reference this guide for workflow clarification

## Advanced Features

### Destination Codes
- Numerical codes for routing efficiency
- Auto-populate when selecting known destinations
- Help with carrier integration and tracking

### Audit Trail
- All picks are logged with user and timestamp
- Source pallet information maintained
- Full traceability for compliance and quality

### Performance Features
- Client-side search for instant results
- Smart caching reduces loading times
- Optimistic updates for smooth experience

## Integration with Existing Workflows

### Pallet-Based Picking
- Still available for destination-based workflows
- Use when you know exactly which pallets to ship
- Ideal for pre-staged orders

### Receiving Integration
- Newly received items immediately available for picking
- Put-away process updates item locations
- Real-time inventory updates

### Shipping Integration
- Packing lists generate automatically
- Tracking integration ready for carrier systems
- Status updates flow through to dashboards

---

## Quick Reference Card

| Workflow | Button Color | Best For | Documentation |
|----------|-------------|----------|---------------|
| Direct Release | 🟠 Orange | Local pickup, stock pulls | Minimal |
| Traditional Ship | 🟢 Green | Customer orders | Full packing list |
| Mixed Shipment | 🔵 Blue | Complex orders | Enhanced tracking |

**Remember:** All workflows support mobile/tablet use and maintain full audit trails for compliance and traceability.

---

*For additional training or questions about these workflows, contact your warehouse operations team.*
