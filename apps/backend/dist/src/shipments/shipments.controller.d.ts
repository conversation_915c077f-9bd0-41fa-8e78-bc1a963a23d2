import { RequestWithWarehouseContext } from "../auth/decorators/warehouse-permission.decorator";
import { EnhancedUserPayload } from "../auth/types";
import { ShipmentsService } from "./shipments.service";
import { QueryShipmentDto } from "./dto/query-shipment.dto";
import { UpdateShipmentDto } from "./dto/update-shipment.dto";
export declare class ShipmentsController {
    private readonly shipmentsService;
    constructor(shipmentsService: ShipmentsService);
    findAll(req: RequestWithWarehouseContext<{
        user: EnhancedUserPayload;
    }>, queryDto: QueryShipmentDto, warehouseId?: string): Promise<{
        page: number;
        limit: number;
        totalPages: number;
        data: import("./dto/shipment-list-response.dto").ShipmentListItemDto[];
        count: number;
    }>;
    getSummary(id: string, req: RequestWithWarehouseContext<{
        user: EnhancedUserPayload;
    }>, warehouseId?: string): Promise<{
        shipmentId: string;
        purchaseOrderNumber: string;
        status: string;
        palletsByDestination: Record<string, ({
            id: string;
            status: string;
            label: string;
            description: string | null;
            barcode: string | null;
            shipToDestination: string | null;
            destinationCode: string | null;
            dateCreated: Date;
            lastMovedDate: Date;
            locationId: string | null;
            shipmentId: string | null;
        } & {
            location: import("@prisma/client").Location | null;
            palletItems: ({
                id: string;
                quantity: number;
                dateAdded: Date;
                palletId: string;
                itemId: string;
            } & {
                item: import("@prisma/client").Item;
            })[];
        })[]>;
    }>;
    findByPoNumber(poNumber: string, req: RequestWithWarehouseContext<{
        user: EnhancedUserPayload;
    }>, warehouseId?: string): Promise<{
        id: string;
        status: string;
        createdAt: Date;
        updatedAt: Date;
        tenantId: string;
        referenceNumber: string | null;
        purchaseOrderId: string;
    }>;
    updateShipment(id: string, updateShipmentDto: UpdateShipmentDto, req: RequestWithWarehouseContext<{
        user: EnhancedUserPayload;
    }>, warehouseId?: string): Promise<{
        purchaseOrder: {
            id: string;
            status: string;
            createdAt: Date;
            updatedAt: Date;
            tenantId: string;
            warehouseId: string | null;
            poNumber: string;
            supplier: string | null;
            notes: string | null;
            orderDate: Date;
            expectedDeliveryDate: Date | null;
        };
    } & {
        id: string;
        status: string;
        createdAt: Date;
        updatedAt: Date;
        tenantId: string;
        referenceNumber: string | null;
        purchaseOrderId: string;
    }>;
}
