"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ItemsService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const role_enum_1 = require("../auth/entities/role.enum");
let ItemsService = class ItemsService {
    constructor(prisma) {
        this.prisma = prisma;
    }
    async search(query, currentUser) {
        const { q, warehouseId, limit = 20, offset = 0 } = query;
        const whereClause = {
            tenantId: currentUser.tenantId,
            status: "Active",
            OR: [
                {
                    name: {
                        contains: q,
                        mode: "insensitive",
                    },
                },
                {
                    sku: {
                        contains: q,
                        mode: "insensitive",
                    },
                },
            ],
        };
        if (warehouseId) {
            await this.validateWarehouseAccess(warehouseId, currentUser);
            whereClause.itemLocations = {
                some: {
                    pallet: {
                        location: {
                            warehouseId: warehouseId,
                        },
                    },
                },
            };
        }
        else if (currentUser.role !== role_enum_1.Role.TENANT_ADMIN) {
            const userWarehouseIds = currentUser.warehouseUsers?.map((wu) => wu.warehouseId) || [];
            if (userWarehouseIds.length === 0) {
                return {
                    items: [],
                    pagination: { total: 0, limit, offset, hasMore: false },
                };
            }
            whereClause.itemLocations = {
                some: {
                    pallet: {
                        location: {
                            warehouseId: {
                                in: userWarehouseIds,
                            },
                        },
                    },
                },
            };
        }
        const total = await this.prisma.item.count({ where: whereClause });
        const items = await this.prisma.item.findMany({
            where: whereClause,
            include: {
                itemLocations: {
                    include: {
                        pallet: {
                            include: {
                                location: true,
                            },
                        },
                    },
                    where: warehouseId
                        ? {
                            pallet: {
                                location: {
                                    warehouseId: warehouseId,
                                },
                            },
                        }
                        : currentUser.role !== role_enum_1.Role.TENANT_ADMIN
                            ? {
                                pallet: {
                                    location: {
                                        warehouseId: {
                                            in: currentUser.warehouseUsers?.map((wu) => wu.warehouseId) || [],
                                        },
                                    },
                                },
                            }
                            : undefined,
                },
            },
            orderBy: { name: "asc" },
            skip: offset,
            take: limit,
        });
        const searchResults = items.map((item) => {
            const totalQuantity = item.itemLocations.reduce((sum, location) => sum + location.quantity, 0);
            const locationCount = new Set(item.itemLocations.map((location) => location.pallet.location?.id)).size;
            return {
                id: item.id,
                sku: item.sku,
                name: item.name,
                description: item.description,
                unitOfMeasure: item.unitOfMeasure,
                status: item.status,
                totalQuantity,
                availableQuantity: totalQuantity,
                locationCount,
            };
        });
        return {
            items: searchResults,
            pagination: {
                total,
                limit,
                offset,
                hasMore: offset + limit < total,
            },
        };
    }
    async getItemLocations(itemId, warehouseId, currentUser) {
        await this.validateWarehouseAccess(warehouseId, currentUser);
        const item = await this.prisma.item.findFirst({
            where: {
                id: itemId,
                tenantId: currentUser.tenantId,
            },
        });
        if (!item) {
            throw new common_1.NotFoundException(`Item with ID "${itemId}" not found in your tenant.`);
        }
        const itemLocations = await this.prisma.itemLocation.findMany({
            where: {
                itemId: itemId,
                pallet: {
                    location: {
                        warehouseId: warehouseId,
                    },
                },
            },
            include: {
                pallet: {
                    include: {
                        location: true,
                    },
                },
            },
            orderBy: {
                lastUpdated: "desc",
            },
        });
        const totalQuantity = itemLocations.reduce((sum, location) => sum + location.quantity, 0);
        const locations = itemLocations.map((location) => ({
            palletId: location.palletId,
            palletBarcode: location.pallet.barcode,
            quantity: location.quantity,
            location: {
                id: location.pallet.location.id,
                name: location.pallet.location.name,
                category: location.pallet.location.category,
                type: location.pallet.location.locationType,
            },
            lastUpdated: location.lastUpdated.toISOString(),
        }));
        return {
            itemId: item.id,
            itemName: item.name,
            totalQuantity,
            locations,
        };
    }
    async validateWarehouseAccess(warehouseId, currentUser) {
        if (currentUser.role === role_enum_1.Role.TENANT_ADMIN) {
            const warehouse = await this.prisma.warehouse.findFirst({
                where: {
                    id: warehouseId,
                    tenantId: currentUser.tenantId,
                },
            });
            if (!warehouse) {
                throw new common_1.NotFoundException(`Warehouse with ID "${warehouseId}" not found in your tenant.`);
            }
            return;
        }
        const userWarehouseIds = currentUser.warehouseUsers?.map((wu) => wu.warehouseId) || [];
        if (!userWarehouseIds.includes(warehouseId)) {
            throw new common_1.ForbiddenException(`You do not have access to warehouse "${warehouseId}".`);
        }
        const warehouse = await this.prisma.warehouse.findFirst({
            where: {
                id: warehouseId,
                tenantId: currentUser.tenantId,
            },
        });
        if (!warehouse) {
            throw new common_1.NotFoundException(`Warehouse with ID "${warehouseId}" not found in your tenant.`);
        }
    }
    async create(createItemDto, currentUser) {
        if (createItemDto.sku) {
            const existingSku = await this.prisma.item.findFirst({
                where: {
                    sku: createItemDto.sku,
                },
            });
            if (existingSku) {
                throw new common_1.ForbiddenException(`Item with SKU ${createItemDto.sku} already exists.`);
            }
        }
        return this.prisma.$transaction(async (tx) => {
            const newItem = await tx.item.create({
                data: {
                    name: createItemDto.name,
                    sku: createItemDto.sku,
                    description: createItemDto.description,
                    unitOfMeasure: createItemDto.unitOfMeasure,
                    defaultCost: createItemDto.defaultCost,
                    lowStockThreshold: createItemDto.lowStockThreshold,
                    status: createItemDto.status,
                    tenantId: currentUser.tenantId,
                },
            });
            if (createItemDto.warehouseIds && createItemDto.warehouseIds.length > 0) {
                const validTenantWarehouses = await tx.warehouse.findMany({
                    where: {
                        id: { in: createItemDto.warehouseIds },
                        tenantId: currentUser.tenantId,
                    },
                    select: { id: true },
                });
                const validTenantWarehouseIds = validTenantWarehouses.map((wh) => wh.id);
                if (validTenantWarehouseIds.length !== createItemDto.warehouseIds.length) {
                    const invalidIds = createItemDto.warehouseIds.filter((id) => !validTenantWarehouseIds.includes(id));
                    throw new common_1.ForbiddenException(`Warehouses with IDs [${invalidIds.join(", ")}] do not exist or do not belong to your tenant.`);
                }
                const warehouseItemData = validTenantWarehouseIds.map((warehouseId) => ({
                    itemId: newItem.id,
                    warehouseId: warehouseId,
                    tenantId: currentUser.tenantId,
                }));
                await tx.warehouseItem.createMany({
                    data: warehouseItemData,
                    skipDuplicates: true,
                });
            }
            return newItem;
        });
    }
    async findAll(currentUser) {
        const whereClause = {
            tenantId: currentUser.tenantId,
        };
        if (String(currentUser.role) !== role_enum_1.Role.TENANT_ADMIN) {
            const userWarehouseIds = currentUser.warehouseUsers?.map((wu) => wu.warehouseId) || [];
            if (userWarehouseIds.length === 0) {
                return [];
            }
            whereClause.warehouseItems = {
                some: {
                    warehouseId: {
                        in: userWarehouseIds,
                    },
                    tenantId: currentUser.tenantId,
                },
            };
        }
        const items = await this.prisma.item.findMany({
            where: whereClause,
            orderBy: { name: "asc" },
        });
        if (items.length === 0) {
            return [];
        }
        const itemIds = items.map((item) => item.id);
        const quantityMap = itemIds.reduce((map, itemId) => {
            map[itemId] = 0;
            return map;
        }, {});
        const itemsWithQuantity = items.map((item) => ({
            ...item,
            quantityOnHand: quantityMap[item.id] || 0,
        }));
        return itemsWithQuantity;
    }
    async findOne(id, currentUser) {
        const item = await this.prisma.item.findUnique({
            where: {
                id,
                tenantId: currentUser.tenantId,
            },
        });
        if (!item) {
            throw new common_1.NotFoundException(`Item with ID "${id}" not found in your tenant.`);
        }
        if (String(currentUser.role) !== role_enum_1.Role.TENANT_ADMIN) {
            const userWarehouseIds = currentUser.warehouseUsers?.map((wu) => wu.warehouseId) || [];
            if (userWarehouseIds.length === 0) {
                throw new common_1.NotFoundException(`Item with ID "${id}" not accessible. You are not assigned to any warehouses.`);
            }
            const warehouseItem = await this.prisma.warehouseItem.findFirst({
                where: {
                    itemId: id,
                    warehouseId: { in: userWarehouseIds },
                    tenantId: currentUser.tenantId,
                },
            });
            if (!warehouseItem) {
                throw new common_1.NotFoundException(`Item with ID "${id}" is not available in any of your assigned warehouses.`);
            }
        }
        const palletItems = await this.prisma.palletItem.findMany({
            where: {
                itemId: item.id,
                pallet: {
                    location: {
                        warehouse: {
                            tenantId: currentUser.tenantId,
                        },
                    },
                },
            },
            include: {
                pallet: {
                    include: {
                        location: {
                            include: {
                                warehouse: true,
                            },
                        },
                    },
                },
            },
        });
        let quantityOnHand = 0;
        const stockByLocationAndPallet = [];
        const distinctPalletIds = new Set();
        for (const pi of palletItems) {
            quantityOnHand += pi.quantity;
            if (pi.pallet) {
                distinctPalletIds.add(pi.pallet.id);
                stockByLocationAndPallet.push({
                    palletId: pi.pallet.id,
                    locationName: pi.pallet.location?.name || "N/A",
                    warehouseName: pi.pallet.location?.warehouse?.name || "N/A",
                    quantity: pi.quantity,
                });
            }
        }
        return {
            ...item,
            quantityOnHand,
            numberOfPallets: distinctPalletIds.size,
            stockByLocationAndPallet,
        };
    }
    async update(id, updateItemDto, currentUser) {
        const existingItem = await this.findOne(id, currentUser);
        if (!existingItem) {
            throw new common_1.NotFoundException(`Item with ID "${id}" not found or not accessible.`);
        }
        if (String(currentUser.role) !== role_enum_1.Role.TENANT_ADMIN) {
            throw new common_1.ForbiddenException("You do not have permission to update this item.");
        }
        if ("warehouseId" in updateItemDto &&
            updateItemDto.warehouseId !== undefined) {
            throw new common_1.ForbiddenException("Cannot update warehouseId directly. Manage warehouse associations via WarehouseItem records.");
        }
        if (updateItemDto.sku) {
            const existingSkuItem = await this.prisma.item.findFirst({
                where: {
                    sku: updateItemDto.sku,
                    id: { not: id },
                    tenantId: currentUser.tenantId,
                },
            });
            if (existingSkuItem) {
                throw new common_1.ForbiddenException(`Another item with SKU ${updateItemDto.sku} already exists in your tenant.`);
            }
        }
        return this.prisma.$transaction(async (tx) => {
            const updatedItem = await tx.item.update({
                where: {
                    id,
                    tenantId: currentUser.tenantId,
                },
                data: {
                    name: updateItemDto.name,
                    sku: updateItemDto.sku,
                    description: updateItemDto.description,
                    unitOfMeasure: updateItemDto.unitOfMeasure,
                    defaultCost: updateItemDto.defaultCost,
                    lowStockThreshold: updateItemDto.lowStockThreshold,
                    status: updateItemDto.status,
                },
            });
            if (typeof updateItemDto.warehouseIds !== "undefined") {
                const validTenantWarehouses = await tx.warehouse.findMany({
                    where: {
                        id: { in: updateItemDto.warehouseIds },
                        tenantId: currentUser.tenantId,
                    },
                    select: { id: true },
                });
                const validTenantWarehouseIds = validTenantWarehouses.map((wh) => wh.id);
                if (updateItemDto.warehouseIds.length > 0 &&
                    validTenantWarehouseIds.length !== updateItemDto.warehouseIds.length) {
                    const invalidIds = updateItemDto.warehouseIds.filter((id) => !validTenantWarehouseIds.includes(id));
                    throw new common_1.ForbiddenException(`Warehouses with IDs [${invalidIds.join(", ")}] do not exist or do not belong to your tenant for item association.`);
                }
                const currentWarehouseItems = await tx.warehouseItem.findMany({
                    where: {
                        itemId: id,
                        tenantId: currentUser.tenantId,
                    },
                    select: { warehouseId: true },
                });
                const currentTenantWarehouseIds = currentWarehouseItems.map((wi) => wi.warehouseId);
                const newTenantWarehouseIds = validTenantWarehouseIds;
                const idsToCreate = newTenantWarehouseIds.filter((wid) => !currentTenantWarehouseIds.includes(wid));
                const idsToDelete = currentTenantWarehouseIds.filter((wid) => !newTenantWarehouseIds.includes(wid));
                if (idsToCreate.length > 0) {
                    await tx.warehouseItem.createMany({
                        data: idsToCreate.map((warehouseId) => ({
                            itemId: id,
                            warehouseId: warehouseId,
                            tenantId: currentUser.tenantId,
                        })),
                        skipDuplicates: true,
                    });
                }
                if (idsToDelete.length > 0) {
                    await tx.warehouseItem.deleteMany({
                        where: {
                            itemId: id,
                            warehouseId: { in: idsToDelete },
                            tenantId: currentUser.tenantId,
                        },
                    });
                }
            }
            return updatedItem;
        });
    }
    async remove(id, currentUser) {
        const itemToDelete = await this.findOne(id, currentUser);
        if (String(currentUser.role) !== role_enum_1.Role.TENANT_ADMIN) {
            throw new common_1.ForbiddenException("You do not have permission to delete this item.");
        }
        return this.prisma.$transaction(async (tx) => {
            const palletItems = await tx.palletItem.findMany({
                where: {
                    itemId: id,
                },
                select: { id: true },
            });
            if (palletItems.length > 0) {
                throw new common_1.ForbiddenException("Cannot delete item with associated pallet items. Remove or update pallet items first.");
            }
            await tx.warehouseItem.deleteMany({
                where: {
                    itemId: id,
                    tenantId: currentUser.tenantId,
                },
            });
            const deletedItem = await tx.item.delete({
                where: {
                    id,
                    tenantId: currentUser.tenantId,
                },
            });
            return deletedItem;
        });
    }
};
exports.ItemsService = ItemsService;
exports.ItemsService = ItemsService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], ItemsService);
//# sourceMappingURL=items.service.js.map