import { ItemsService } from "./items.service";
import { CreateItemDto } from "./dto/create-item.dto";
import { UpdateItemDto } from "./dto/update-item.dto";
import { SearchItemsDto } from "./dto/search-items.dto";
import { RequestWithWarehouseContext } from "../auth/decorators/warehouse-permission.decorator";
import { EnhancedUserPayload } from "../auth/types";
export declare class ItemsController {
    private readonly itemsService;
    constructor(itemsService: ItemsService);
    searchItems(searchDto: SearchItemsDto, req: RequestWithWarehouseContext<{
        user: EnhancedUserPayload;
    }>): Promise<import("./items.service").ItemSearchResponse>;
    getItemLocations(itemId: string, warehouseId: string, req: RequestWithWarehouseContext<{
        user: EnhancedUserPayload;
    }>): Promise<import("./items.service").ItemLocationResponse>;
    create(createItemDto: CreateItemDto, req: RequestWithWarehouseContext<{
        user: EnhancedUserPayload;
    }>): Promise<{
        name: string;
        id: string;
        status: string;
        createdAt: Date;
        updatedAt: Date;
        tenantId: string;
        description: string | null;
        sku: string | null;
        unitOfMeasure: string;
        defaultCost: number | null;
        lowStockThreshold: number | null;
    }>;
    findAll(req: RequestWithWarehouseContext<{
        user: EnhancedUserPayload;
    }>): Promise<{
        name: string;
        id: string;
        status: string;
        createdAt: Date;
        updatedAt: Date;
        tenantId: string;
        description: string | null;
        sku: string | null;
        unitOfMeasure: string;
        defaultCost: number | null;
        lowStockThreshold: number | null;
    }[]>;
    findOne(id: string, req: RequestWithWarehouseContext<{
        user: EnhancedUserPayload;
    }>): Promise<import("./items.service").ItemDetailResponse>;
    update(id: string, updateItemDto: UpdateItemDto, req: RequestWithWarehouseContext<{
        user: EnhancedUserPayload;
    }>): Promise<{
        name: string;
        id: string;
        status: string;
        createdAt: Date;
        updatedAt: Date;
        tenantId: string;
        description: string | null;
        sku: string | null;
        unitOfMeasure: string;
        defaultCost: number | null;
        lowStockThreshold: number | null;
    }>;
    remove(id: string, req: RequestWithWarehouseContext<{
        user: EnhancedUserPayload;
    }>): Promise<{
        name: string;
        id: string;
        status: string;
        createdAt: Date;
        updatedAt: Date;
        tenantId: string;
        description: string | null;
        sku: string | null;
        unitOfMeasure: string;
        defaultCost: number | null;
        lowStockThreshold: number | null;
    }>;
}
