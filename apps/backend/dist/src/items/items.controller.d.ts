import { ItemsService } from "./items.service";
import { CreateItemDto } from "./dto/create-item.dto";
import { UpdateItemDto } from "./dto/update-item.dto";
import { SearchItemsDto } from "./dto/search-items.dto";
import { RequestWithWarehouseContext } from "../auth/decorators/warehouse-permission.decorator";
import { EnhancedUserPayload } from "../auth/types";
export declare class ItemsController {
    private readonly itemsService;
    constructor(itemsService: ItemsService);
    searchItems(searchDto: SearchItemsDto, req: RequestWithWarehouseContext<{
        user: EnhancedUserPayload;
    }>): Promise<import("./items.service").ItemSearchResponse>;
    getItemLocations(itemId: string, warehouseId: string, req: RequestWithWarehouseContext<{
        user: EnhancedUserPayload;
    }>): Promise<import("./items.service").ItemLocationResponse>;
    create(createItemDto: CreateItemDto, req: RequestWithWarehouseContext<{
        user: EnhancedUserPayload;
    }>): Promise<{
        name: string;
        sku: string | null;
        description: string | null;
        unitOfMeasure: string;
        defaultCost: number | null;
        lowStockThreshold: number | null;
        status: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        tenantId: string;
    }>;
    findAll(req: RequestWithWarehouseContext<{
        user: EnhancedUserPayload;
    }>): Promise<{
        name: string;
        sku: string | null;
        description: string | null;
        unitOfMeasure: string;
        defaultCost: number | null;
        lowStockThreshold: number | null;
        status: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        tenantId: string;
    }[]>;
    findOne(id: string, req: RequestWithWarehouseContext<{
        user: EnhancedUserPayload;
    }>): Promise<import("./items.service").ItemDetailResponse>;
    update(id: string, updateItemDto: UpdateItemDto, req: RequestWithWarehouseContext<{
        user: EnhancedUserPayload;
    }>): Promise<{
        name: string;
        sku: string | null;
        description: string | null;
        unitOfMeasure: string;
        defaultCost: number | null;
        lowStockThreshold: number | null;
        status: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        tenantId: string;
    }>;
    remove(id: string, req: RequestWithWarehouseContext<{
        user: EnhancedUserPayload;
    }>): Promise<{
        name: string;
        sku: string | null;
        description: string | null;
        unitOfMeasure: string;
        defaultCost: number | null;
        lowStockThreshold: number | null;
        status: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        tenantId: string;
    }>;
}
