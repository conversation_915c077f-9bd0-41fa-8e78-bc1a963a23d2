"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const items_controller_1 = require("./items.controller");
const items_service_1 = require("./items.service");
const common_1 = require("@nestjs/common");
const client_1 = require("@prisma/client");
describe("ItemsController", () => {
    let controller;
    let service;
    const mockItemsService = {
        search: jest.fn(),
        getItemLocations: jest.fn(),
        create: jest.fn(),
        findAll: jest.fn(),
        findOne: jest.fn(),
        update: jest.fn(),
        remove: jest.fn(),
    };
    const mockUser = {
        id: "user-123",
        email: "<EMAIL>",
        role: client_1.Role.WAREHOUSE_MEMBER,
        tenantId: "tenant-123",
        name: "Test User",
        authUserId: "auth-123",
        warehouseUsers: [
            { warehouseId: "warehouse-123", role: client_1.Role.WAREHOUSE_MEMBER },
        ],
    };
    const mockRequest = {
        user: mockUser,
        warehouseContext: {
            warehouseId: "warehouse-123",
            tenantId: "tenant-123",
        },
    };
    beforeEach(async () => {
        const module = await testing_1.Test.createTestingModule({
            controllers: [items_controller_1.ItemsController],
            providers: [
                {
                    provide: items_service_1.ItemsService,
                    useValue: mockItemsService,
                },
            ],
        }).compile();
        controller = module.get(items_controller_1.ItemsController);
        service = module.get(items_service_1.ItemsService);
    });
    afterEach(() => {
        jest.clearAllMocks();
    });
    it("should be defined", () => {
        expect(controller).toBeDefined();
    });
    describe("searchItems", () => {
        const searchDto = {
            q: "widget",
            limit: 20,
            offset: 0,
        };
        const mockSearchResults = [
            {
                id: "item-1",
                name: "Widget A",
                sku: "WID-001",
                description: "Test widget",
                unitOfMeasure: "pcs",
                tenantId: "tenant-123",
            },
            {
                id: "item-2",
                name: "Widget B",
                sku: "WID-002",
                description: "Another widget",
                unitOfMeasure: "pcs",
                tenantId: "tenant-123",
            },
        ];
        it("should search items with warehouse context", async () => {
            mockItemsService.search.mockResolvedValue(mockSearchResults);
            const result = await controller.searchItems(searchDto, mockRequest);
            expect(service.search).toHaveBeenCalledWith({
                q: "widget",
                warehouseId: "warehouse-123",
                limit: 20,
                offset: 0,
            }, mockUser);
            expect(result).toEqual(mockSearchResults);
        });
        it("should use provided warehouseId over context", async () => {
            const searchDtoWithWarehouse = {
                ...searchDto,
                warehouseId: "specific-warehouse",
            };
            mockItemsService.search.mockResolvedValue(mockSearchResults);
            await controller.searchItems(searchDtoWithWarehouse, mockRequest);
            expect(service.search).toHaveBeenCalledWith({
                q: "widget",
                warehouseId: "specific-warehouse",
                limit: 20,
                offset: 0,
            }, mockUser);
        });
        it("should handle empty search results", async () => {
            mockItemsService.search.mockResolvedValue([]);
            const result = await controller.searchItems(searchDto, mockRequest);
            expect(result).toEqual([]);
        });
        it("should handle service errors", async () => {
            mockItemsService.search.mockRejectedValue(new common_1.ForbiddenException("Access denied"));
            await expect(controller.searchItems(searchDto, mockRequest)).rejects.toThrow(common_1.ForbiddenException);
        });
    });
    describe("getItemLocations", () => {
        const itemId = "item-123";
        const mockLocations = [
            {
                id: "location-1",
                palletId: "pallet-1",
                palletBarcode: "PAL-001",
                quantity: 50,
                location: {
                    id: "loc-1",
                    name: "A-01-01",
                    category: "Storage",
                    warehouseId: "warehouse-123",
                },
            },
            {
                id: "location-2",
                palletId: "pallet-2",
                palletBarcode: "PAL-002",
                quantity: 25,
                location: {
                    id: "loc-2",
                    name: "B-02-01",
                    category: "Storage",
                    warehouseId: "warehouse-123",
                },
            },
        ];
        it("should get item locations with warehouse context", async () => {
            mockItemsService.getItemLocations.mockResolvedValue(mockLocations);
            const result = await controller.getItemLocations(itemId, undefined, mockRequest);
            expect(service.getItemLocations).toHaveBeenCalledWith(itemId, "warehouse-123", mockUser);
            expect(result).toEqual(mockLocations);
        });
        it("should use provided warehouseId over context", async () => {
            const specificWarehouseId = "specific-warehouse";
            mockItemsService.getItemLocations.mockResolvedValue(mockLocations);
            await controller.getItemLocations(itemId, specificWarehouseId, mockRequest);
            expect(service.getItemLocations).toHaveBeenCalledWith(itemId, specificWarehouseId, mockUser);
        });
        it("should throw NotFoundException when warehouseId is missing", async () => {
            const requestWithoutContext = {
                ...mockRequest,
                warehouseContext: undefined,
            };
            await expect(controller.getItemLocations(itemId, undefined, requestWithoutContext)).rejects.toThrow(common_1.NotFoundException);
        });
        it("should handle empty location results", async () => {
            mockItemsService.getItemLocations.mockResolvedValue([]);
            const result = await controller.getItemLocations(itemId, undefined, mockRequest);
            expect(result).toEqual([]);
        });
        it("should handle service errors", async () => {
            mockItemsService.getItemLocations.mockRejectedValue(new common_1.NotFoundException("Item not found"));
            await expect(controller.getItemLocations(itemId, undefined, mockRequest)).rejects.toThrow(common_1.NotFoundException);
        });
    });
    describe("warehouse access control", () => {
        it("should enforce warehouse context for search", async () => {
            const requestWithoutWarehouse = {
                ...mockRequest,
                warehouseContext: undefined,
            };
            mockItemsService.search.mockResolvedValue([]);
            await controller.searchItems({ q: "test" }, requestWithoutWarehouse);
            expect(service.search).toHaveBeenCalledWith({
                q: "test",
                warehouseId: undefined,
                limit: undefined,
                offset: undefined,
            }, mockUser);
        });
        it("should validate warehouse access in service layer", async () => {
            mockItemsService.search.mockRejectedValue(new common_1.ForbiddenException("Access denied to warehouse"));
            await expect(controller.searchItems({ q: "test" }, mockRequest)).rejects.toThrow(common_1.ForbiddenException);
        });
    });
});
//# sourceMappingURL=items.controller.spec.js.map