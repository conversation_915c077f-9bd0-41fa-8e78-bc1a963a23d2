import { PrismaService } from "../prisma/prisma.service";
import { CreateItemDto } from "./dto/create-item.dto";
import { UpdateItemDto } from "./dto/update-item.dto";
import { Item } from "@prisma/client";
import { AuthenticatedUser } from "../auth/types/authenticated-user.interface";
export interface ItemSearchQuery {
    q: string;
    warehouseId?: string;
    limit?: number;
    offset?: number;
}
export interface ItemSearchResult {
    id: string;
    sku: string | null;
    name: string;
    description: string | null;
    unitOfMeasure: string;
    status: string;
    totalQuantity: number;
    availableQuantity: number;
    locationCount: number;
}
export interface ItemSearchResponse {
    items: ItemSearchResult[];
    pagination: {
        total: number;
        limit: number;
        offset: number;
        hasMore: boolean;
    };
}
export interface ItemLocationResult {
    palletId: string;
    palletBarcode: string | null;
    quantity: number;
    location: {
        id: string;
        name: string;
        category: string;
        type: string;
    };
    lastUpdated: string;
}
export interface ItemLocationResponse {
    itemId: string;
    itemName: string;
    totalQuantity: number;
    locations: ItemLocationResult[];
}
interface PalletLocationStock {
    palletId: string;
    locationName: string | null;
    warehouseName: string | null;
    quantity: number;
}
export interface ItemDetailResponse extends Item {
    quantityOnHand: number;
    numberOfPallets: number;
    stockByLocationAndPallet: PalletLocationStock[];
}
export declare class ItemsService {
    private prisma;
    constructor(prisma: PrismaService);
    search(query: ItemSearchQuery, currentUser: AuthenticatedUser): Promise<ItemSearchResponse>;
    getItemLocations(itemId: string, warehouseId: string, currentUser: AuthenticatedUser): Promise<ItemLocationResponse>;
    private validateWarehouseAccess;
    create(createItemDto: CreateItemDto, currentUser: AuthenticatedUser): Promise<Item>;
    findAll(currentUser: AuthenticatedUser): Promise<Item[]>;
    findOne(id: string, currentUser: AuthenticatedUser): Promise<ItemDetailResponse>;
    update(id: string, updateItemDto: UpdateItemDto, currentUser: AuthenticatedUser): Promise<Item>;
    remove(id: string, currentUser: AuthenticatedUser): Promise<Item>;
}
export {};
