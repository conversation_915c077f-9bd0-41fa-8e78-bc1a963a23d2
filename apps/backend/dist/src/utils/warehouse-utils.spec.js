"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.sortLocationsByName = exports.categorizeLocation = exports.validateItemSKU = exports.generateShipmentReference = exports.isValidWarehouseCode = exports.calculatePalletCapacity = exports.parseLocationName = exports.formatLocationName = exports.validateDestinationCode = exports.generatePalletBarcode = void 0;
const globals_1 = require("@jest/globals");
const generatePalletBarcode = (warehousePrefix, sequence) => {
    return `${warehousePrefix}-${sequence.toString().padStart(3, "0")}`;
};
exports.generatePalletBarcode = generatePalletBarcode;
const validateDestinationCode = (code) => {
    return /^\d+$/.test(code);
};
exports.validateDestinationCode = validateDestinationCode;
const formatLocationName = (aisle, bay, level) => {
    return `${aisle}-${bay}-${level}`;
};
exports.formatLocationName = formatLocationName;
const parseLocationName = (locationName) => {
    const parts = locationName.split("-");
    if (parts.length !== 3) {
        return null;
    }
    return {
        aisle: parts[0],
        bay: parts[1],
        level: parts[2],
    };
};
exports.parseLocationName = parseLocationName;
const calculatePalletCapacity = (items) => {
    return items.reduce((total, item) => total + item.quantity, 0);
};
exports.calculatePalletCapacity = calculatePalletCapacity;
const isValidWarehouseCode = (code) => {
    return /^[A-Z]{2,4}$/.test(code);
};
exports.isValidWarehouseCode = isValidWarehouseCode;
const generateShipmentReference = (warehouseCode, date, sequence) => {
    const dateStr = date.toISOString().slice(0, 10).replace(/-/g, "");
    return `${warehouseCode}-${dateStr}-${sequence.toString().padStart(4, "0")}`;
};
exports.generateShipmentReference = generateShipmentReference;
const validateItemSKU = (sku) => {
    return /^[A-Z0-9-]{3,20}$/.test(sku);
};
exports.validateItemSKU = validateItemSKU;
const categorizeLocation = (locationName) => {
    const parsed = (0, exports.parseLocationName)(locationName);
    if (!parsed)
        return "Unknown";
    const { aisle } = parsed;
    if (aisle.startsWith("R"))
        return "Receiving";
    if (aisle.startsWith("S"))
        return "Shipping";
    if (aisle.match(/^[A-P]/))
        return "Storage";
    return "Unknown";
};
exports.categorizeLocation = categorizeLocation;
const sortLocationsByName = (locations) => {
    return [...locations].sort((a, b) => a.name.localeCompare(b.name));
};
exports.sortLocationsByName = sortLocationsByName;
(0, globals_1.describe)("Warehouse Utilities", () => {
    (0, globals_1.describe)("generatePalletBarcode", () => {
        (0, globals_1.it)("should generate barcode with warehouse prefix", () => {
            (0, globals_1.expect)((0, exports.generatePalletBarcode)("WH1", 1)).toBe("WH1-001");
            (0, globals_1.expect)((0, exports.generatePalletBarcode)("WH1", 42)).toBe("WH1-042");
            (0, globals_1.expect)((0, exports.generatePalletBarcode)("WH1", 999)).toBe("WH1-999");
        });
        (0, globals_1.it)("should pad sequence numbers correctly", () => {
            (0, globals_1.expect)((0, exports.generatePalletBarcode)("ABC", 1)).toBe("ABC-001");
            (0, globals_1.expect)((0, exports.generatePalletBarcode)("ABC", 10)).toBe("ABC-010");
            (0, globals_1.expect)((0, exports.generatePalletBarcode)("ABC", 100)).toBe("ABC-100");
            (0, globals_1.expect)((0, exports.generatePalletBarcode)("ABC", 1000)).toBe("ABC-1000");
        });
    });
    (0, globals_1.describe)("validateDestinationCode", () => {
        (0, globals_1.it)("should validate numeric codes", () => {
            (0, globals_1.expect)((0, exports.validateDestinationCode)("12345")).toBe(true);
            (0, globals_1.expect)((0, exports.validateDestinationCode)("0")).toBe(true);
            (0, globals_1.expect)((0, exports.validateDestinationCode)("999")).toBe(true);
        });
        (0, globals_1.it)("should reject non-numeric codes", () => {
            (0, globals_1.expect)((0, exports.validateDestinationCode)("ABC123")).toBe(false);
            (0, globals_1.expect)((0, exports.validateDestinationCode)("12-34")).toBe(false);
            (0, globals_1.expect)((0, exports.validateDestinationCode)("")).toBe(false);
        });
    });
    (0, globals_1.describe)("formatLocationName", () => {
        (0, globals_1.it)("should format location names correctly", () => {
            (0, globals_1.expect)((0, exports.formatLocationName)("A", "01", "01")).toBe("A-01-01");
            (0, globals_1.expect)((0, exports.formatLocationName)("B", "02", "03")).toBe("B-02-03");
            (0, globals_1.expect)((0, exports.formatLocationName)("R", "01", "01")).toBe("R-01-01");
        });
    });
    (0, globals_1.describe)("parseLocationName", () => {
        (0, globals_1.it)("should parse valid location names", () => {
            (0, globals_1.expect)((0, exports.parseLocationName)("A-01-01")).toEqual({
                aisle: "A",
                bay: "01",
                level: "01",
            });
            (0, globals_1.expect)((0, exports.parseLocationName)("B-02-03")).toEqual({
                aisle: "B",
                bay: "02",
                level: "03",
            });
        });
        (0, globals_1.it)("should return null for invalid location names", () => {
            (0, globals_1.expect)((0, exports.parseLocationName)("A-01")).toBeNull();
            (0, globals_1.expect)((0, exports.parseLocationName)("A-01-01-02")).toBeNull();
            (0, globals_1.expect)((0, exports.parseLocationName)("")).toBeNull();
            (0, globals_1.expect)((0, exports.parseLocationName)("invalid")).toBeNull();
        });
    });
    (0, globals_1.describe)("calculatePalletCapacity", () => {
        (0, globals_1.it)("should calculate total capacity", () => {
            const items = [
                { quantity: 10, unitOfMeasure: "pcs" },
                { quantity: 5, unitOfMeasure: "pcs" },
                { quantity: 3, unitOfMeasure: "pcs" },
            ];
            (0, globals_1.expect)((0, exports.calculatePalletCapacity)(items)).toBe(18);
        });
        (0, globals_1.it)("should handle empty items array", () => {
            (0, globals_1.expect)((0, exports.calculatePalletCapacity)([])).toBe(0);
        });
        (0, globals_1.it)("should handle zero quantities", () => {
            const items = [
                { quantity: 0, unitOfMeasure: "pcs" },
                { quantity: 5, unitOfMeasure: "pcs" },
            ];
            (0, globals_1.expect)((0, exports.calculatePalletCapacity)(items)).toBe(5);
        });
    });
    (0, globals_1.describe)("isValidWarehouseCode", () => {
        (0, globals_1.it)("should validate warehouse codes", () => {
            (0, globals_1.expect)((0, exports.isValidWarehouseCode)("WH")).toBe(true);
            (0, globals_1.expect)((0, exports.isValidWarehouseCode)("ABC")).toBe(true);
            (0, globals_1.expect)((0, exports.isValidWarehouseCode)("ABCD")).toBe(true);
        });
        (0, globals_1.it)("should reject invalid warehouse codes", () => {
            (0, globals_1.expect)((0, exports.isValidWarehouseCode)("A")).toBe(false);
            (0, globals_1.expect)((0, exports.isValidWarehouseCode)("ABCDE")).toBe(false);
            (0, globals_1.expect)((0, exports.isValidWarehouseCode)("ab")).toBe(false);
            (0, globals_1.expect)((0, exports.isValidWarehouseCode)("A1")).toBe(false);
            (0, globals_1.expect)((0, exports.isValidWarehouseCode)("")).toBe(false);
        });
    });
    (0, globals_1.describe)("generateShipmentReference", () => {
        (0, globals_1.it)("should generate shipment references", () => {
            const date = new Date("2024-01-15");
            (0, globals_1.expect)((0, exports.generateShipmentReference)("WH1", date, 1)).toBe("WH1-20240115-0001");
            (0, globals_1.expect)((0, exports.generateShipmentReference)("ABC", date, 42)).toBe("ABC-20240115-0042");
        });
        (0, globals_1.it)("should pad sequence numbers correctly", () => {
            const date = new Date("2024-01-15");
            (0, globals_1.expect)((0, exports.generateShipmentReference)("WH", date, 1)).toBe("WH-20240115-0001");
            (0, globals_1.expect)((0, exports.generateShipmentReference)("WH", date, 1000)).toBe("WH-20240115-1000");
            (0, globals_1.expect)((0, exports.generateShipmentReference)("WH", date, 10000)).toBe("WH-20240115-10000");
        });
    });
    (0, globals_1.describe)("validateItemSKU", () => {
        (0, globals_1.it)("should validate item SKUs", () => {
            (0, globals_1.expect)((0, exports.validateItemSKU)("ABC-123")).toBe(true);
            (0, globals_1.expect)((0, exports.validateItemSKU)("WIDGET-001")).toBe(true);
            (0, globals_1.expect)((0, exports.validateItemSKU)("SKU123")).toBe(true);
            (0, globals_1.expect)((0, exports.validateItemSKU)("A1B2C3")).toBe(true);
        });
        (0, globals_1.it)("should reject invalid SKUs", () => {
            (0, globals_1.expect)((0, exports.validateItemSKU)("AB")).toBe(false);
            (0, globals_1.expect)((0, exports.validateItemSKU)("A".repeat(21))).toBe(false);
            (0, globals_1.expect)((0, exports.validateItemSKU)("abc-123")).toBe(false);
            (0, globals_1.expect)((0, exports.validateItemSKU)("ABC 123")).toBe(false);
            (0, globals_1.expect)((0, exports.validateItemSKU)("ABC@123")).toBe(false);
            (0, globals_1.expect)((0, exports.validateItemSKU)("")).toBe(false);
        });
    });
    (0, globals_1.describe)("categorizeLocation", () => {
        (0, globals_1.it)("should categorize receiving locations", () => {
            (0, globals_1.expect)((0, exports.categorizeLocation)("R-01-01")).toBe("Receiving");
            (0, globals_1.expect)((0, exports.categorizeLocation)("R-02-01")).toBe("Receiving");
        });
        (0, globals_1.it)("should categorize shipping locations", () => {
            (0, globals_1.expect)((0, exports.categorizeLocation)("S-01-01")).toBe("Shipping");
            (0, globals_1.expect)((0, exports.categorizeLocation)("S-02-01")).toBe("Shipping");
        });
        (0, globals_1.it)("should categorize storage locations", () => {
            (0, globals_1.expect)((0, exports.categorizeLocation)("A-01-01")).toBe("Storage");
            (0, globals_1.expect)((0, exports.categorizeLocation)("B-02-01")).toBe("Storage");
            (0, globals_1.expect)((0, exports.categorizeLocation)("P-01-01")).toBe("Storage");
        });
        (0, globals_1.it)("should handle unknown locations", () => {
            (0, globals_1.expect)((0, exports.categorizeLocation)("Z-01-01")).toBe("Unknown");
            (0, globals_1.expect)((0, exports.categorizeLocation)("invalid")).toBe("Unknown");
            (0, globals_1.expect)((0, exports.categorizeLocation)("")).toBe("Unknown");
        });
    });
    (0, globals_1.describe)("sortLocationsByName", () => {
        (0, globals_1.it)("should sort locations alphabetically", () => {
            const locations = [
                { name: "B-01-01" },
                { name: "A-01-01" },
                { name: "C-01-01" },
            ];
            const sorted = (0, exports.sortLocationsByName)(locations);
            (0, globals_1.expect)(sorted.map((l) => l.name)).toEqual([
                "A-01-01",
                "B-01-01",
                "C-01-01",
            ]);
        });
        (0, globals_1.it)("should not mutate original array", () => {
            const locations = [{ name: "B-01-01" }, { name: "A-01-01" }];
            const sorted = (0, exports.sortLocationsByName)(locations);
            (0, globals_1.expect)(locations[0].name).toBe("B-01-01");
            (0, globals_1.expect)(sorted[0].name).toBe("A-01-01");
        });
        (0, globals_1.it)("should handle empty array", () => {
            (0, globals_1.expect)((0, exports.sortLocationsByName)([])).toEqual([]);
        });
    });
    (0, globals_1.describe)("Integration scenarios", () => {
        (0, globals_1.it)("should handle complete warehouse workflow", () => {
            const warehouseCode = "WH1";
            const sequence = 42;
            const barcode = (0, exports.generatePalletBarcode)(warehouseCode, sequence);
            const location = (0, exports.formatLocationName)("A", "01", "02");
            const category = (0, exports.categorizeLocation)(location);
            (0, globals_1.expect)(barcode).toBe("WH1-042");
            (0, globals_1.expect)(location).toBe("A-01-02");
            (0, globals_1.expect)(category).toBe("Storage");
            (0, globals_1.expect)((0, exports.isValidWarehouseCode)("WHB")).toBe(true);
        });
        (0, globals_1.it)("should validate and format shipment data", () => {
            const destinationCode = "12345";
            const itemSKU = "WIDGET-001";
            const date = new Date("2024-01-15");
            const shipmentRef = (0, exports.generateShipmentReference)("WH1", date, 1);
            (0, globals_1.expect)((0, exports.validateDestinationCode)(destinationCode)).toBe(true);
            (0, globals_1.expect)((0, exports.validateItemSKU)(itemSKU)).toBe(true);
            (0, globals_1.expect)(shipmentRef).toBe("WH1-20240115-0001");
        });
    });
});
//# sourceMappingURL=warehouse-utils.spec.js.map