export declare const generatePalletBarcode: (warehousePrefix: string, sequence: number) => string;
export declare const validateDestinationCode: (code: string) => boolean;
export declare const formatLocationName: (aisle: string, bay: string, level: string) => string;
export declare const parseLocationName: (locationName: string) => {
    aisle: string;
    bay: string;
    level: string;
} | null;
export declare const calculatePalletCapacity: (items: Array<{
    quantity: number;
    unitOfMeasure: string;
}>) => number;
export declare const isValidWarehouseCode: (code: string) => boolean;
export declare const generateShipmentReference: (warehouseCode: string, date: Date, sequence: number) => string;
export declare const validateItemSKU: (sku: string) => boolean;
export declare const categorizeLocation: (locationName: string) => "Receiving" | "Storage" | "Shipping" | "Unknown";
export declare const sortLocationsByName: (locations: Array<{
    name: string;
}>) => Array<{
    name: string;
}>;
