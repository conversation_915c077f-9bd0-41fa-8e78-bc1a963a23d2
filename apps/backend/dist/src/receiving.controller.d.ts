import { ReceivingService } from "./receiving.service";
import { ReceiveItemsDto } from "./receiving/dto/receive-items.dto";
import { RequestWithWarehouseContext } from "./auth/decorators/warehouse-permission.decorator";
import { EnhancedUserPayload } from "./auth/types";
export declare class ReceivingController {
    private readonly receivingService;
    constructor(receivingService: ReceivingService);
    receiveItems(receiveItemsDto: ReceiveItemsDto, req: RequestWithWarehouseContext<{
        user: EnhancedUserPayload;
    }>): Promise<{
        palletItems: {
            id: string;
            quantity: number;
            dateAdded: Date;
            palletId: string;
            itemId: string;
        }[];
    } & {
        id: string;
        status: string;
        label: string;
        description: string | null;
        barcode: string | null;
        shipToDestination: string | null;
        destinationCode: string | null;
        dateCreated: Date;
        lastMovedDate: Date;
        locationId: string | null;
        shipmentId: string | null;
    }>;
}
