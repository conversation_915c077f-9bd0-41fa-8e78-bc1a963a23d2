import { PrismaService } from "../prisma/prisma.service";
import { CreateShipmentDto } from "./dto/create-shipment.dto";
import { SearchShipmentsDto } from "./dto/search-shipments.dto";
import { ReleaseInventoryDto } from "./dto/release-inventory.dto";
import { OutgoingShipment, ShipmentStatus } from "@prisma/client";
import { AuthenticatedUser } from "../auth/types/authenticated-user.interface";
export interface OutgoingShipmentWithDetails extends OutgoingShipment {
    shipmentItems: Array<{
        id: string;
        itemId: string;
        itemName: string;
        quantity: number;
        sourcePalletId: string | null;
    }>;
    shipmentPallets: Array<{
        id: string;
        palletId: string;
        palletBarcode: string | null;
    }>;
}
export interface ShipmentSearchResult {
    id: string;
    shipmentNumber: string;
    status: ShipmentStatus;
    destination: string | null;
    destinationCode: string | null;
    itemCount: number;
    palletCount: number;
    createdAt: Date;
}
export interface ShipmentSearchResponse {
    shipments: ShipmentSearchResult[];
    pagination: {
        total: number;
        limit: number;
        offset: number;
        hasMore: boolean;
    };
    filters: {
        status: ShipmentStatus[];
        destinations: string[];
    };
}
export interface PackingListItem {
    sku: string | null;
    name: string;
    quantity: number;
    unitOfMeasure: string;
    sourcePallet: string | null;
}
export interface PackingListPallet {
    barcode: string | null;
    description: string | null;
    itemCount: number;
    totalQuantity: number;
}
export interface PackingListResponse {
    shipmentId: string;
    shipmentNumber: string;
    destination: string | null;
    destinationCode: string | null;
    generatedAt: string;
    items: PackingListItem[];
    pallets: PackingListPallet[];
    summary: {
        totalItems: number;
        totalQuantity: number;
        totalPallets: number;
    };
}
export interface InventoryReleaseResponse {
    shipmentId: string;
    status: ShipmentStatus;
    shippedAt: string;
    trackingNumber: string | null;
    releasedItems: Array<{
        itemId: string;
        quantity: number;
        releasedFromPallet: string;
    }>;
    releasedPallets: string[];
    inventoryUpdates: {
        itemsReleased: number;
        palletsReleased: number;
        totalQuantityReleased: number;
    };
}
export declare class OutgoingShipmentsService {
    private prisma;
    constructor(prisma: PrismaService);
    create(createShipmentDto: CreateShipmentDto, currentUser: AuthenticatedUser): Promise<OutgoingShipmentWithDetails>;
    private validateInventoryAvailability;
    private validatePalletAccess;
    private determineWarehouse;
    private generateShipmentNumber;
    search(query: SearchShipmentsDto, currentUser: AuthenticatedUser): Promise<ShipmentSearchResponse>;
    private validateWarehouseAccess;
    getPackingList(shipmentId: string, currentUser: AuthenticatedUser): Promise<PackingListResponse>;
    releaseInventory(shipmentId: string, releaseDto: ReleaseInventoryDto, currentUser: AuthenticatedUser): Promise<InventoryReleaseResponse>;
}
