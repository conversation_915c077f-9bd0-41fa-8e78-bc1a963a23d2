import { OutgoingShipmentsService } from "./outgoing-shipments.service";
import { CreateShipmentDto } from "./dto/create-shipment.dto";
import { SearchShipmentsDto } from "./dto/search-shipments.dto";
import { UpdateShipmentStatusDto } from "./dto/update-shipment-status.dto";
import { AddItemsToShipmentDto } from "./dto/add-items-to-shipment.dto";
import { ReleaseInventoryDto } from "./dto/release-inventory.dto";
import { RequestWithWarehouseContext } from "../auth/decorators/warehouse-permission.decorator";
import { EnhancedUserPayload } from "../auth/types";
export declare class OutgoingShipmentsController {
    private readonly outgoingShipmentsService;
    constructor(outgoingShipmentsService: OutgoingShipmentsService);
    createShipment(createShipmentDto: CreateShipmentDto, req: RequestWithWarehouseContext<{
        user: EnhancedUserPayload;
    }>): Promise<import("./outgoing-shipments.service").OutgoingShipmentWithDetails>;
    searchShipments(searchDto: SearchShipmentsDto, req: RequestWithWarehouseContext<{
        user: EnhancedUserPayload;
    }>): Promise<import("./outgoing-shipments.service").ShipmentSearchResponse>;
    updateShipmentStatus(shipmentId: string, updateStatusDto: UpdateShipmentStatusDto, req: RequestWithWarehouseContext<{
        user: EnhancedUserPayload;
    }>): Promise<void>;
    addItemsToShipment(shipmentId: string, addItemsDto: AddItemsToShipmentDto, req: RequestWithWarehouseContext<{
        user: EnhancedUserPayload;
    }>): Promise<void>;
    getPackingList(shipmentId: string, req: RequestWithWarehouseContext<{
        user: EnhancedUserPayload;
    }>): Promise<import("./outgoing-shipments.service").PackingListResponse>;
    releaseInventory(shipmentId: string, releaseDto: ReleaseInventoryDto, req: RequestWithWarehouseContext<{
        user: EnhancedUserPayload;
    }>): Promise<import("./outgoing-shipments.service").InventoryReleaseResponse>;
}
