"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const outgoing_shipments_controller_1 = require("./outgoing-shipments.controller");
const outgoing_shipments_service_1 = require("./outgoing-shipments.service");
const common_1 = require("@nestjs/common");
const client_1 = require("@prisma/client");
describe("OutgoingShipmentsController", () => {
    let controller;
    let service;
    const mockOutgoingShipmentsService = {
        create: jest.fn(),
        search: jest.fn(),
        findOne: jest.fn(),
        updateStatus: jest.fn(),
        addItems: jest.fn(),
        generatePackingList: jest.fn(),
        releaseInventory: jest.fn(),
    };
    const mockUser = {
        id: "user-123",
        email: "<EMAIL>",
        role: client_1.Role.WAREHOUSE_MEMBER,
        tenantId: "tenant-123",
        name: "Test User",
        authUserId: "auth-123",
        warehouseUsers: [
            { warehouseId: "warehouse-123", role: client_1.Role.WAREHOUSE_MEMBER },
        ],
    };
    const mockRequest = {
        user: mockUser,
        warehouseContext: {
            warehouseId: "warehouse-123",
            tenantId: "tenant-123",
        },
    };
    beforeEach(async () => {
        const module = await testing_1.Test.createTestingModule({
            controllers: [outgoing_shipments_controller_1.OutgoingShipmentsController],
            providers: [
                {
                    provide: outgoing_shipments_service_1.OutgoingShipmentsService,
                    useValue: mockOutgoingShipmentsService,
                },
            ],
        }).compile();
        controller = module.get(outgoing_shipments_controller_1.OutgoingShipmentsController);
        service = module.get(outgoing_shipments_service_1.OutgoingShipmentsService);
    });
    afterEach(() => {
        jest.clearAllMocks();
    });
    it("should be defined", () => {
        expect(controller).toBeDefined();
    });
    describe("createShipment", () => {
        const createShipmentDto = {
            destination: "Customer ABC",
            destinationCode: "12345",
            items: [
                {
                    itemId: "item-1",
                    quantity: 10,
                    sourcePalletId: "pallet-1",
                },
            ],
            notes: "Test shipment",
        };
        const mockShipment = {
            id: "shipment-123",
            destination: "Customer ABC",
            destinationCode: "12345",
            status: "PREPARING",
            warehouseId: "warehouse-123",
            tenantId: "tenant-123",
            createdAt: new Date(),
        };
        it("should create a new shipment", async () => {
            mockOutgoingShipmentsService.create.mockResolvedValue(mockShipment);
            const result = await controller.createShipment(createShipmentDto, mockRequest);
            expect(service.create).toHaveBeenCalledWith(createShipmentDto, mockUser);
            expect(result).toEqual(mockShipment);
        });
        it("should handle service errors during creation", async () => {
            mockOutgoingShipmentsService.create.mockRejectedValue(new common_1.ForbiddenException("Insufficient inventory"));
            await expect(controller.createShipment(createShipmentDto, mockRequest)).rejects.toThrow(common_1.ForbiddenException);
        });
    });
    describe("searchShipments", () => {
        const searchDto = {
            status: "PREPARING",
            destination: "Customer",
            limit: 20,
            offset: 0,
        };
        const mockSearchResults = [
            {
                id: "shipment-1",
                destination: "Customer ABC",
                status: "PREPARING",
                warehouseId: "warehouse-123",
                tenantId: "tenant-123",
            },
            {
                id: "shipment-2",
                destination: "Customer XYZ",
                status: "PREPARING",
                warehouseId: "warehouse-123",
                tenantId: "tenant-123",
            },
        ];
        it("should search shipments with warehouse context", async () => {
            mockOutgoingShipmentsService.search.mockResolvedValue(mockSearchResults);
            const result = await controller.searchShipments(searchDto, mockRequest);
            expect(service.search).toHaveBeenCalledWith({
                ...searchDto,
                warehouseId: "warehouse-123",
            }, mockUser);
            expect(result).toEqual(mockSearchResults);
        });
        it("should use provided warehouseId over context", async () => {
            const searchDtoWithWarehouse = {
                ...searchDto,
                warehouseId: "specific-warehouse",
            };
            mockOutgoingShipmentsService.search.mockResolvedValue(mockSearchResults);
            await controller.searchShipments(searchDtoWithWarehouse, mockRequest);
            expect(service.search).toHaveBeenCalledWith({
                ...searchDto,
                warehouseId: "specific-warehouse",
            }, mockUser);
        });
        it("should handle empty search results", async () => {
            mockOutgoingShipmentsService.search.mockResolvedValue([]);
            const result = await controller.searchShipments(searchDto, mockRequest);
            expect(result).toEqual([]);
        });
    });
    describe("updateShipmentStatus", () => {
        const shipmentId = "shipment-123";
        const updateDto = {
            status: "PACKED",
            notes: "Shipment packed and ready",
        };
        const mockUpdatedShipment = {
            id: shipmentId,
            status: "PACKED",
            destination: "Customer ABC",
            warehouseId: "warehouse-123",
        };
        it("should update shipment status", async () => {
            mockOutgoingShipmentsService.updateStatus.mockResolvedValue(mockUpdatedShipment);
            const result = await controller.updateShipmentStatus(shipmentId, updateDto, mockRequest);
            expect(service.updateStatus).toHaveBeenCalledWith(shipmentId, updateDto, mockUser);
            expect(result).toEqual(mockUpdatedShipment);
        });
        it("should handle not found errors", async () => {
            mockOutgoingShipmentsService.updateStatus.mockRejectedValue(new common_1.NotFoundException("Shipment not found"));
            await expect(controller.updateShipmentStatus(shipmentId, updateDto, mockRequest)).rejects.toThrow(common_1.NotFoundException);
        });
    });
    describe("releaseInventory", () => {
        const shipmentId = "shipment-123";
        const releaseDto = {
            trackingNumber: "TRACK-123",
            releaseNotes: "Released for shipping",
        };
        const mockReleasedShipment = {
            id: shipmentId,
            status: "SHIPPED",
            trackingNumber: "TRACK-123",
            destination: "Customer ABC",
        };
        it("should release inventory for shipment", async () => {
            mockOutgoingShipmentsService.releaseInventory.mockResolvedValue(mockReleasedShipment);
            const result = await controller.releaseInventory(shipmentId, releaseDto, mockRequest);
            expect(service.releaseInventory).toHaveBeenCalledWith(shipmentId, releaseDto, mockUser);
            expect(result).toEqual(mockReleasedShipment);
        });
        it("should handle release errors", async () => {
            mockOutgoingShipmentsService.releaseInventory.mockRejectedValue(new common_1.ForbiddenException("Cannot release unpacked shipment"));
            await expect(controller.releaseInventory(shipmentId, releaseDto, mockRequest)).rejects.toThrow(common_1.ForbiddenException);
        });
    });
    describe("warehouse access control", () => {
        it("should enforce warehouse context for search", async () => {
            const requestWithoutWarehouse = {
                ...mockRequest,
                warehouseContext: undefined,
            };
            mockOutgoingShipmentsService.search.mockResolvedValue([]);
            await controller.searchShipments({ status: "PREPARING" }, requestWithoutWarehouse);
            expect(service.search).toHaveBeenCalledWith({
                status: "PREPARING",
                warehouseId: undefined,
            }, mockUser);
        });
        it("should validate warehouse access in service layer", async () => {
            mockOutgoingShipmentsService.search.mockRejectedValue(new common_1.ForbiddenException("Access denied to warehouse"));
            await expect(controller.searchShipments({ status: "PREPARING" }, mockRequest)).rejects.toThrow(common_1.ForbiddenException);
        });
    });
});
//# sourceMappingURL=outgoing-shipments.controller.spec.js.map