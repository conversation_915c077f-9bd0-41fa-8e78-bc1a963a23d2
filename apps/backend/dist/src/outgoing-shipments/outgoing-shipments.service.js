"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OutgoingShipmentsService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const client_1 = require("@prisma/client");
const role_enum_1 = require("../auth/entities/role.enum");
let OutgoingShipmentsService = class OutgoingShipmentsService {
    constructor(prisma) {
        this.prisma = prisma;
    }
    async create(createShipmentDto, currentUser) {
        if ((!createShipmentDto.items || createShipmentDto.items.length === 0) &&
            (!createShipmentDto.pallets || createShipmentDto.pallets.length === 0)) {
            throw new common_1.BadRequestException("At least one item or pallet must be included in the shipment");
        }
        return this.prisma.$transaction(async (tx) => {
            if (createShipmentDto.items && createShipmentDto.items.length > 0) {
                await this.validateInventoryAvailability(createShipmentDto.items, currentUser, tx);
            }
            if (createShipmentDto.pallets && createShipmentDto.pallets.length > 0) {
                await this.validatePalletAccess(createShipmentDto.pallets, currentUser, tx);
            }
            const warehouseId = await this.determineWarehouse(createShipmentDto, currentUser, tx);
            const shipmentNumber = await this.generateShipmentNumber(tx);
            const shipment = await tx.outgoingShipment.create({
                data: {
                    shipmentNumber,
                    destination: createShipmentDto.destination,
                    destinationCode: createShipmentDto.destinationCode,
                    notes: createShipmentDto.notes,
                    tenantId: currentUser.tenantId,
                    warehouseId,
                    status: client_1.ShipmentStatus.PREPARING,
                },
            });
            const shipmentItems = [];
            if (createShipmentDto.items && createShipmentDto.items.length > 0) {
                for (const itemDto of createShipmentDto.items) {
                    const shipmentItem = await tx.outgoingShipmentItem.create({
                        data: {
                            shipmentId: shipment.id,
                            itemId: itemDto.itemId,
                            quantity: itemDto.quantity,
                            sourcePalletId: itemDto.sourcePalletId,
                        },
                        include: {
                            item: { select: { name: true } },
                        },
                    });
                    shipmentItems.push({
                        id: shipmentItem.id,
                        itemId: shipmentItem.itemId,
                        itemName: shipmentItem.item.name,
                        quantity: shipmentItem.quantity,
                        sourcePalletId: shipmentItem.sourcePalletId,
                    });
                }
            }
            const shipmentPallets = [];
            if (createShipmentDto.pallets && createShipmentDto.pallets.length > 0) {
                for (const palletId of createShipmentDto.pallets) {
                    const shipmentPallet = await tx.outgoingShipmentPallet.create({
                        data: {
                            shipmentId: shipment.id,
                            palletId,
                        },
                        include: {
                            pallet: { select: { barcode: true } },
                        },
                    });
                    shipmentPallets.push({
                        id: shipmentPallet.id,
                        palletId: shipmentPallet.palletId,
                        palletBarcode: shipmentPallet.pallet.barcode,
                    });
                }
            }
            return {
                ...shipment,
                shipmentItems,
                shipmentPallets,
            };
        });
    }
    async validateInventoryAvailability(items, currentUser, tx) {
        for (const item of items) {
            const itemLocations = await tx.itemLocation.findMany({
                where: {
                    itemId: item.itemId,
                    pallet: {
                        location: {
                            warehouse: {
                                tenantId: currentUser.tenantId,
                            },
                        },
                    },
                    ...(item.sourcePalletId && { palletId: item.sourcePalletId }),
                },
            });
            const availableQuantity = itemLocations.reduce((sum, location) => sum + location.quantity, 0);
            if (availableQuantity < item.quantity) {
                const itemRecord = await tx.item.findUnique({
                    where: { id: item.itemId },
                    select: { name: true },
                });
                throw new common_1.BadRequestException(`Insufficient inventory for item "${itemRecord?.name || item.itemId}". ` + `Requested: ${item.quantity}, Available: ${availableQuantity}`);
            }
        }
    }
    async validatePalletAccess(palletIds, currentUser, tx) {
        const pallets = await tx.pallet.findMany({
            where: {
                id: { in: palletIds },
                location: {
                    warehouse: {
                        tenantId: currentUser.tenantId,
                    },
                },
            },
            include: {
                location: {
                    include: {
                        warehouse: true,
                    },
                },
            },
        });
        if (pallets.length !== palletIds.length) {
            const foundIds = pallets.map((p) => p.id);
            const missingIds = palletIds.filter((id) => !foundIds.includes(id));
            throw new common_1.NotFoundException(`Pallets not found or not accessible: ${missingIds.join(", ")}`);
        }
        if (currentUser.role !== role_enum_1.Role.TENANT_ADMIN) {
            const userWarehouseIds = currentUser.warehouseUsers?.map((wu) => wu.warehouseId) || [];
            for (const pallet of pallets) {
                if (!userWarehouseIds.includes(pallet.location.warehouseId)) {
                    throw new common_1.ForbiddenException(`You do not have access to pallet "${pallet.label}" in warehouse "${pallet.location.warehouse.name}"`);
                }
            }
        }
    }
    async determineWarehouse(createShipmentDto, currentUser, tx) {
        const warehouseIds = new Set();
        if (createShipmentDto.items && createShipmentDto.items.length > 0) {
            const itemLocations = await tx.itemLocation.findMany({
                where: {
                    itemId: { in: createShipmentDto.items.map((i) => i.itemId) },
                    pallet: {
                        location: {
                            warehouse: {
                                tenantId: currentUser.tenantId,
                            },
                        },
                    },
                },
                include: {
                    pallet: {
                        include: {
                            location: true,
                        },
                    },
                },
            });
            itemLocations.forEach((location) => {
                if (location.pallet.location) {
                    warehouseIds.add(location.pallet.location.warehouseId);
                }
            });
        }
        if (createShipmentDto.pallets && createShipmentDto.pallets.length > 0) {
            const pallets = await tx.pallet.findMany({
                where: {
                    id: { in: createShipmentDto.pallets },
                    location: {
                        warehouse: {
                            tenantId: currentUser.tenantId,
                        },
                    },
                },
                include: {
                    location: true,
                },
            });
            pallets.forEach((pallet) => {
                if (pallet.location) {
                    warehouseIds.add(pallet.location.warehouseId);
                }
            });
        }
        return warehouseIds.size > 0 ? Array.from(warehouseIds)[0] : null;
    }
    async generateShipmentNumber(tx) {
        const today = new Date();
        const dateStr = today.toISOString().slice(0, 10).replace(/-/g, "");
        const prefix = `OS-${dateStr}-`;
        const lastShipment = await tx.outgoingShipment.findFirst({
            where: {
                shipmentNumber: {
                    startsWith: prefix,
                },
            },
            orderBy: {
                shipmentNumber: "desc",
            },
        });
        let sequence = 1;
        if (lastShipment) {
            const lastSequence = parseInt(lastShipment.shipmentNumber.split("-")[2]);
            sequence = lastSequence + 1;
        }
        return `${prefix}${sequence.toString().padStart(4, "0")}`;
    }
    async search(query, currentUser) {
        const { status, destination, warehouseId, limit = 20, offset = 0 } = query;
        const whereClause = {
            tenantId: currentUser.tenantId,
        };
        if (status) {
            whereClause.status = status;
        }
        if (destination) {
            whereClause.OR = [
                {
                    destination: {
                        contains: destination,
                        mode: "insensitive",
                    },
                },
                {
                    destinationCode: {
                        contains: destination,
                        mode: "insensitive",
                    },
                },
            ];
        }
        if (warehouseId) {
            await this.validateWarehouseAccess(warehouseId, currentUser);
            whereClause.warehouseId = warehouseId;
        }
        else if (currentUser.role !== role_enum_1.Role.TENANT_ADMIN) {
            const userWarehouseIds = currentUser.warehouseUsers?.map((wu) => wu.warehouseId) || [];
            if (userWarehouseIds.length === 0) {
                return {
                    shipments: [],
                    pagination: { total: 0, limit, offset, hasMore: false },
                    filters: { status: [], destinations: [] },
                };
            }
            whereClause.warehouseId = {
                in: userWarehouseIds,
            };
        }
        const total = await this.prisma.outgoingShipment.count({
            where: whereClause,
        });
        const shipments = await this.prisma.outgoingShipment.findMany({
            where: whereClause,
            include: {
                shipmentItems: true,
                shipmentPallets: true,
            },
            orderBy: { createdAt: "desc" },
            skip: offset,
            take: limit,
        });
        const searchResults = shipments.map((shipment) => ({
            id: shipment.id,
            shipmentNumber: shipment.shipmentNumber,
            status: shipment.status,
            destination: shipment.destination,
            destinationCode: shipment.destinationCode,
            itemCount: shipment.shipmentItems.length,
            palletCount: shipment.shipmentPallets.length,
            createdAt: shipment.createdAt,
        }));
        const allShipments = await this.prisma.outgoingShipment.findMany({
            where: {
                tenantId: currentUser.tenantId,
                ...(currentUser.role !== role_enum_1.Role.TENANT_ADMIN && {
                    warehouseId: {
                        in: currentUser.warehouseUsers?.map((wu) => wu.warehouseId) || [],
                    },
                }),
            },
            select: {
                status: true,
                destination: true,
            },
        });
        const statusOptions = [...new Set(allShipments.map((s) => s.status))];
        const destinationOptions = [
            ...new Set(allShipments.map((s) => s.destination).filter(Boolean)),
        ];
        return {
            shipments: searchResults,
            pagination: {
                total,
                limit,
                offset,
                hasMore: offset + limit < total,
            },
            filters: {
                status: statusOptions,
                destinations: destinationOptions,
            },
        };
    }
    async validateWarehouseAccess(warehouseId, currentUser) {
        if (currentUser.role === role_enum_1.Role.TENANT_ADMIN) {
            const warehouse = await this.prisma.warehouse.findFirst({
                where: {
                    id: warehouseId,
                    tenantId: currentUser.tenantId,
                },
            });
            if (!warehouse) {
                throw new common_1.NotFoundException(`Warehouse with ID "${warehouseId}" not found in your tenant.`);
            }
            return;
        }
        const userWarehouseIds = currentUser.warehouseUsers?.map((wu) => wu.warehouseId) || [];
        if (!userWarehouseIds.includes(warehouseId)) {
            throw new common_1.ForbiddenException(`You do not have access to warehouse "${warehouseId}".`);
        }
        const warehouse = await this.prisma.warehouse.findFirst({
            where: {
                id: warehouseId,
                tenantId: currentUser.tenantId,
            },
        });
        if (!warehouse) {
            throw new common_1.NotFoundException(`Warehouse with ID "${warehouseId}" not found in your tenant.`);
        }
    }
    async getPackingList(shipmentId, currentUser) {
        const shipment = await this.prisma.outgoingShipment.findFirst({
            where: {
                id: shipmentId,
                tenantId: currentUser.tenantId,
            },
            include: {
                shipmentItems: {
                    include: {
                        item: true,
                        sourcePallet: {
                            select: {
                                barcode: true,
                                label: true,
                            },
                        },
                    },
                },
                shipmentPallets: {
                    include: {
                        pallet: {
                            include: {
                                palletItems: {
                                    include: {
                                        item: true,
                                    },
                                },
                            },
                        },
                    },
                },
            },
        });
        if (!shipment) {
            throw new common_1.NotFoundException(`Shipment with ID "${shipmentId}" not found in your tenant.`);
        }
        if (shipment.warehouseId) {
            await this.validateWarehouseAccess(shipment.warehouseId, currentUser);
        }
        const packingListItems = shipment.shipmentItems.map((shipmentItem) => ({
            sku: shipmentItem.item.sku,
            name: shipmentItem.item.name,
            quantity: shipmentItem.quantity,
            unitOfMeasure: shipmentItem.item.unitOfMeasure,
            sourcePallet: shipmentItem.sourcePallet?.barcode ||
                shipmentItem.sourcePallet?.label ||
                null,
        }));
        const packingListPallets = shipment.shipmentPallets.map((shipmentPallet) => {
            const pallet = shipmentPallet.pallet;
            const totalQuantity = pallet.palletItems.reduce((sum, item) => sum + item.quantity, 0);
            return {
                barcode: pallet.barcode,
                description: pallet.description,
                itemCount: pallet.palletItems.length,
                totalQuantity,
            };
        });
        const totalItems = packingListItems.length;
        const totalQuantity = packingListItems.reduce((sum, item) => sum + item.quantity, 0);
        const totalPallets = packingListPallets.length;
        return {
            shipmentId: shipment.id,
            shipmentNumber: shipment.shipmentNumber,
            destination: shipment.destination,
            destinationCode: shipment.destinationCode,
            generatedAt: new Date().toISOString(),
            items: packingListItems,
            pallets: packingListPallets,
            summary: {
                totalItems,
                totalQuantity,
                totalPallets,
            },
        };
    }
    async releaseInventory(shipmentId, releaseDto, currentUser) {
        return this.prisma.$transaction(async (tx) => {
            const shipment = await tx.outgoingShipment.findFirst({
                where: {
                    id: shipmentId,
                    tenantId: currentUser.tenantId,
                },
                include: {
                    shipmentItems: {
                        include: {
                            item: true,
                            sourcePallet: true,
                        },
                    },
                    shipmentPallets: {
                        include: {
                            pallet: true,
                        },
                    },
                },
            });
            if (!shipment) {
                throw new common_1.NotFoundException(`Shipment with ID "${shipmentId}" not found in your tenant.`);
            }
            if (shipment.warehouseId) {
                await this.validateWarehouseAccess(shipment.warehouseId, currentUser);
            }
            if (shipment.status === client_1.ShipmentStatus.SHIPPED) {
                throw new common_1.BadRequestException("Shipment has already been shipped");
            }
            if (shipment.status === client_1.ShipmentStatus.CANCELLED) {
                throw new common_1.BadRequestException("Cannot ship a cancelled shipment");
            }
            const updatedShipment = await tx.outgoingShipment.update({
                where: { id: shipmentId },
                data: {
                    status: client_1.ShipmentStatus.SHIPPED,
                    shippedAt: new Date(),
                    notes: releaseDto.releaseNotes
                        ? `${shipment.notes || ""}\n\nShipping Notes: ${releaseDto.releaseNotes}`.trim()
                        : shipment.notes,
                },
            });
            const releasedItems = [];
            let totalQuantityReleased = 0;
            for (const shipmentItem of shipment.shipmentItems) {
                if (shipmentItem.sourcePalletId) {
                    const itemLocation = await tx.itemLocation.findFirst({
                        where: {
                            itemId: shipmentItem.itemId,
                            palletId: shipmentItem.sourcePalletId,
                        },
                    });
                    if (itemLocation && itemLocation.quantity >= shipmentItem.quantity) {
                        await tx.itemLocation.update({
                            where: { id: itemLocation.id },
                            data: {
                                quantity: itemLocation.quantity - shipmentItem.quantity,
                                lastUpdated: new Date(),
                            },
                        });
                        if (itemLocation.quantity === shipmentItem.quantity) {
                            await tx.itemLocation.delete({
                                where: { id: itemLocation.id },
                            });
                        }
                    }
                    releasedItems.push({
                        itemId: shipmentItem.itemId,
                        quantity: shipmentItem.quantity,
                        releasedFromPallet: shipmentItem.sourcePallet?.barcode ||
                            shipmentItem.sourcePallet?.label ||
                            shipmentItem.sourcePalletId,
                    });
                }
                else {
                    const itemLocations = await tx.itemLocation.findMany({
                        where: {
                            itemId: shipmentItem.itemId,
                            pallet: {
                                location: {
                                    warehouseId: shipment.warehouseId,
                                },
                            },
                        },
                        include: {
                            pallet: true,
                        },
                        orderBy: {
                            createdAt: "asc",
                        },
                    });
                    let remainingToRelease = shipmentItem.quantity;
                    for (const location of itemLocations) {
                        if (remainingToRelease <= 0)
                            break;
                        const releaseFromThisLocation = Math.min(location.quantity, remainingToRelease);
                        await tx.itemLocation.update({
                            where: { id: location.id },
                            data: {
                                quantity: location.quantity - releaseFromThisLocation,
                                lastUpdated: new Date(),
                            },
                        });
                        if (location.quantity === releaseFromThisLocation) {
                            await tx.itemLocation.delete({
                                where: { id: location.id },
                            });
                        }
                        releasedItems.push({
                            itemId: shipmentItem.itemId,
                            quantity: releaseFromThisLocation,
                            releasedFromPallet: location.pallet.barcode ||
                                location.pallet.label ||
                                location.palletId,
                        });
                        remainingToRelease -= releaseFromThisLocation;
                    }
                }
                totalQuantityReleased += shipmentItem.quantity;
            }
            const releasedPallets = [];
            for (const shipmentPallet of shipment.shipmentPallets) {
                releasedPallets.push(shipmentPallet.pallet.barcode ||
                    shipmentPallet.pallet.label ||
                    shipmentPallet.palletId);
            }
            return {
                shipmentId: updatedShipment.id,
                status: updatedShipment.status,
                shippedAt: updatedShipment.shippedAt.toISOString(),
                trackingNumber: releaseDto.trackingNumber || null,
                releasedItems,
                releasedPallets,
                inventoryUpdates: {
                    itemsReleased: releasedItems.length,
                    palletsReleased: releasedPallets.length,
                    totalQuantityReleased,
                },
            };
        });
    }
};
exports.OutgoingShipmentsService = OutgoingShipmentsService;
exports.OutgoingShipmentsService = OutgoingShipmentsService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], OutgoingShipmentsService);
//# sourceMappingURL=outgoing-shipments.service.js.map