"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OutgoingShipmentsController = void 0;
const common_1 = require("@nestjs/common");
const outgoing_shipments_service_1 = require("./outgoing-shipments.service");
const create_shipment_dto_1 = require("./dto/create-shipment.dto");
const search_shipments_dto_1 = require("./dto/search-shipments.dto");
const update_shipment_status_dto_1 = require("./dto/update-shipment-status.dto");
const add_items_to_shipment_dto_1 = require("./dto/add-items-to-shipment.dto");
const release_inventory_dto_1 = require("./dto/release-inventory.dto");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const warehouse_permission_guard_1 = require("../auth/guards/warehouse-permission.guard");
const warehouse_permission_decorator_1 = require("../auth/decorators/warehouse-permission.decorator");
const log_action_decorator_1 = require("../audit-log/decorators/log-action.decorator");
const audit_log_interceptor_1 = require("../audit-log/interceptors/audit-log.interceptor");
let OutgoingShipmentsController = class OutgoingShipmentsController {
    constructor(outgoingShipmentsService) {
        this.outgoingShipmentsService = outgoingShipmentsService;
    }
    async createShipment(createShipmentDto, req) {
        return this.outgoingShipmentsService.create(createShipmentDto, req.user);
    }
    async searchShipments(searchDto, req) {
        return this.outgoingShipmentsService.search({
            ...searchDto,
            warehouseId: searchDto.warehouseId || req.warehouseContext?.warehouseId,
        }, req.user);
    }
    async updateShipmentStatus(shipmentId, updateStatusDto, req) {
        throw new Error("Method not implemented yet");
    }
    async addItemsToShipment(shipmentId, addItemsDto, req) {
        throw new Error("Method not implemented yet");
    }
    async getPackingList(shipmentId, req) {
        return this.outgoingShipmentsService.getPackingList(shipmentId, req.user);
    }
    async releaseInventory(shipmentId, releaseDto, req) {
        return this.outgoingShipmentsService.releaseInventory(shipmentId, releaseDto, req.user);
    }
};
exports.OutgoingShipmentsController = OutgoingShipmentsController;
__decorate([
    (0, common_1.Post)(),
    (0, warehouse_permission_decorator_1.RequireWarehouseAccess)(),
    (0, common_1.UsePipes)(new common_1.ValidationPipe({ whitelist: true, forbidNonWhitelisted: true })),
    (0, log_action_decorator_1.LogAction)({
        action: "CREATE_SHIPMENT",
        entity: "OutgoingShipment",
        getEntityId: (context, result) => result?.id,
        getDetails: (context, result) => {
            const request = context.switchToHttp().getRequest();
            return {
                destination: result?.destination,
                destinationCode: result?.destinationCode,
                itemCount: result?.shipmentItems?.length || 0,
                palletCount: result?.shipmentPallets?.length || 0,
                warehouseId: request.warehouseContext?.warehouseId,
                userRole: request.warehouseContext?.userRole,
            };
        },
    }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_shipment_dto_1.CreateShipmentDto, Object]),
    __metadata("design:returntype", Promise)
], OutgoingShipmentsController.prototype, "createShipment", null);
__decorate([
    (0, common_1.Get)("search"),
    (0, warehouse_permission_decorator_1.RequireWarehouseAccess)(),
    (0, common_1.UsePipes)(new common_1.ValidationPipe({
        whitelist: true,
        forbidNonWhitelisted: true,
        transform: true,
    })),
    (0, log_action_decorator_1.LogAction)({
        action: "SEARCH_SHIPMENTS",
        entity: "OutgoingShipment",
    }),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [search_shipments_dto_1.SearchShipmentsDto, Object]),
    __metadata("design:returntype", Promise)
], OutgoingShipmentsController.prototype, "searchShipments", null);
__decorate([
    (0, common_1.Put)(":id/status"),
    (0, warehouse_permission_decorator_1.RequireWarehouseAccess)(),
    (0, common_1.UsePipes)(new common_1.ValidationPipe({ whitelist: true, forbidNonWhitelisted: true })),
    (0, log_action_decorator_1.LogAction)({
        action: "UPDATE_SHIPMENT_STATUS",
        entity: "OutgoingShipment",
        getEntityId: (context) => context.switchToHttp().getRequest().params.id,
        getDetails: (context, result) => ({
            oldStatus: context.switchToHttp().getRequest().body.oldStatus,
            newStatus: result?.status,
            notes: result?.notes,
        }),
    }),
    __param(0, (0, common_1.Param)("id")),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_shipment_status_dto_1.UpdateShipmentStatusDto, Object]),
    __metadata("design:returntype", Promise)
], OutgoingShipmentsController.prototype, "updateShipmentStatus", null);
__decorate([
    (0, common_1.Post)(":id/items"),
    (0, warehouse_permission_decorator_1.RequireWarehouseAccess)(),
    (0, common_1.UsePipes)(new common_1.ValidationPipe({ whitelist: true, forbidNonWhitelisted: true })),
    (0, log_action_decorator_1.LogAction)({
        action: "ADD_ITEMS_TO_SHIPMENT",
        entity: "OutgoingShipment",
        getEntityId: (context) => context.switchToHttp().getRequest().params.id,
        getDetails: (context, result) => ({
            itemsAdded: result?.addedItems?.length || 0,
            totalItems: result?.updatedTotals?.totalItems || 0,
        }),
    }),
    __param(0, (0, common_1.Param)("id")),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, add_items_to_shipment_dto_1.AddItemsToShipmentDto, Object]),
    __metadata("design:returntype", Promise)
], OutgoingShipmentsController.prototype, "addItemsToShipment", null);
__decorate([
    (0, common_1.Get)(":id/packing-list"),
    (0, warehouse_permission_decorator_1.RequireWarehouseAccess)(),
    (0, log_action_decorator_1.LogAction)({
        action: "GENERATE_PACKING_LIST",
        entity: "OutgoingShipment",
        getEntityId: (context) => context.switchToHttp().getRequest().params.id,
    }),
    __param(0, (0, common_1.Param)("id")),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], OutgoingShipmentsController.prototype, "getPackingList", null);
__decorate([
    (0, common_1.Post)(":id/release"),
    (0, warehouse_permission_decorator_1.RequireWarehouseAccess)(),
    (0, common_1.UsePipes)(new common_1.ValidationPipe({ whitelist: true, forbidNonWhitelisted: true })),
    (0, log_action_decorator_1.LogAction)({
        action: "RELEASE_INVENTORY",
        entity: "OutgoingShipment",
        getEntityId: (context) => context.switchToHttp().getRequest().params.id,
        getDetails: (context, result) => ({
            itemsReleased: result?.inventoryUpdates?.itemsReleased || 0,
            palletsReleased: result?.inventoryUpdates?.palletsReleased || 0,
            totalQuantityReleased: result?.inventoryUpdates?.totalQuantityReleased || 0,
            trackingNumber: result?.trackingNumber,
        }),
    }),
    __param(0, (0, common_1.Param)("id")),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, release_inventory_dto_1.ReleaseInventoryDto, Object]),
    __metadata("design:returntype", Promise)
], OutgoingShipmentsController.prototype, "releaseInventory", null);
exports.OutgoingShipmentsController = OutgoingShipmentsController = __decorate([
    (0, common_1.Controller)("shipments"),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, warehouse_permission_guard_1.WarehousePermissionGuard),
    (0, common_1.UseInterceptors)(audit_log_interceptor_1.AuditLogInterceptor),
    __metadata("design:paramtypes", [outgoing_shipments_service_1.OutgoingShipmentsService])
], OutgoingShipmentsController);
//# sourceMappingURL=outgoing-shipments.controller.js.map