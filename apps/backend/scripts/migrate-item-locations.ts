import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

/**
 * Data migration script to populate ItemLocation table from existing PalletItem data
 * This ensures backward compatibility when introducing the new ItemLocation entity
 */
async function migrateItemLocations() {
  console.log("Starting ItemLocation data migration...");

  try {
    // Get all existing PalletItems with their relationships
    const palletItems = await prisma.palletItem.findMany({
      include: {
        item: true,
        pallet: {
          include: {
            location: {
              include: {
                warehouse: true,
              },
            },
          },
        },
      },
      where: {
        quantity: {
          gt: 0, // Only migrate items with positive quantities
        },
      },
    });

    console.log(`Found ${palletItems.length} PalletItems to migrate`);

    // Track migration statistics
    let migratedCount = 0;
    let skippedCount = 0;
    let errorCount = 0;

    // Process in batches to avoid memory issues
    const batchSize = 100;
    for (let i = 0; i < palletItems.length; i += batchSize) {
      const batch = palletItems.slice(i, i + batchSize);

      console.log(
        `Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(
          palletItems.length / batchSize
        )}`
      );

      // Prepare batch data for ItemLocation creation
      const itemLocationData = batch
        .filter((palletItem) => {
          // Skip if pallet doesn't have a location (shouldn't happen in normal operations)
          if (!palletItem.pallet.location) {
            console.warn(
              `Skipping PalletItem ${palletItem.id}: Pallet ${palletItem.palletId} has no location`
            );
            skippedCount++;
            return false;
          }
          return true;
        })
        .map((palletItem) => ({
          itemId: palletItem.itemId,
          palletId: palletItem.palletId,
          quantity: palletItem.quantity,
          createdAt: palletItem.dateAdded,
          lastUpdated: new Date(),
        }));

      if (itemLocationData.length === 0) {
        continue;
      }

      try {
        // Use createMany with skipDuplicates to handle potential conflicts
        const result = await prisma.itemLocation.createMany({
          data: itemLocationData,
          skipDuplicates: true,
        });

        migratedCount += result.count;
        console.log(`Batch completed: ${result.count} ItemLocations created`);
      } catch (error) {
        console.error(`Error processing batch starting at index ${i}:`, error);
        errorCount += batch.length;
      }
    }

    // Verify migration results
    const totalItemLocations = await prisma.itemLocation.count();

    console.log("\n=== Migration Summary ===");
    console.log(`Total PalletItems processed: ${palletItems.length}`);
    console.log(`ItemLocations created: ${migratedCount}`);
    console.log(`Items skipped: ${skippedCount}`);
    console.log(`Errors encountered: ${errorCount}`);
    console.log(`Total ItemLocations in database: ${totalItemLocations}`);

    // Validate data integrity
    await validateMigration();

    console.log("\nItemLocation data migration completed successfully!");
  } catch (error) {
    console.error("Migration failed:", error);
    throw error;
  }
}

/**
 * Validate that the migration was successful by comparing data
 */
async function validateMigration() {
  console.log("\nValidating migration data integrity...");

  // Check that all PalletItems with positive quantities have corresponding ItemLocations
  const palletItemsWithoutLocations = await prisma.palletItem.findMany({
    where: {
      quantity: { gt: 0 },
      pallet: {
        location: { isNot: null },
      },
      NOT: {
        itemId: {
          in: await prisma.itemLocation
            .findMany({
              select: { itemId: true },
            })
            .then((locations) => locations.map((l) => l.itemId)),
        },
      },
    },
    include: {
      item: { select: { name: true } },
      pallet: { select: { label: true } },
    },
  });

  if (palletItemsWithoutLocations.length > 0) {
    console.warn(
      `Warning: ${palletItemsWithoutLocations.length} PalletItems don't have corresponding ItemLocations:`
    );
    palletItemsWithoutLocations.forEach((pi) => {
      console.warn(
        `- Item: ${pi.item.name}, Pallet: ${pi.pallet.label}, Quantity: ${pi.quantity}`
      );
    });
  }

  // Check for quantity mismatches
  const quantityMismatches = await prisma.$queryRaw`
    SELECT
      pi."itemId",
      pi."palletId",
      pi.quantity as pallet_item_quantity,
      il.quantity as item_location_quantity
    FROM "PalletItem" pi
    JOIN "ItemLocation" il ON pi."itemId" = il."itemId" AND pi."palletId" = il."palletId"
    WHERE pi.quantity != il.quantity
  `;

  if (Array.isArray(quantityMismatches) && quantityMismatches.length > 0) {
    console.warn(
      `Warning: ${quantityMismatches.length} quantity mismatches found between PalletItem and ItemLocation`
    );
  }

  console.log("Data integrity validation completed.");
}

/**
 * Rollback function to remove all ItemLocation records (for testing purposes)
 */
async function rollbackMigration() {
  console.log("Rolling back ItemLocation migration...");

  const deleteResult = await prisma.itemLocation.deleteMany({});
  console.log(`Deleted ${deleteResult.count} ItemLocation records`);

  console.log("Rollback completed.");
}

// Main execution
async function main() {
  const args = process.argv.slice(2);

  if (args.includes("--rollback")) {
    await rollbackMigration();
  } else {
    await migrateItemLocations();
  }
}

// Execute if run directly
if (require.main === module) {
  main()
    .catch((error) => {
      console.error("Migration script failed:", error);
      process.exit(1);
    })
    .finally(async () => {
      await prisma.$disconnect();
    });
}

export { migrateItemLocations, rollbackMigration, validateMigration };
