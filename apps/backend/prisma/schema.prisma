// Target Prisma Schema for Warehouse-Scoped Access Control
// This shows the final state after removing redundant tenantId columns

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Tenant {
  id        String   @id @default(cuid())
  name      String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Direct tenant relationships (keep these)
  warehouses         Warehouse[]
  users              User[]
  items              Item[]
  purchaseOrders     PurchaseOrder[]
  auditLogs          AuditLog[]
  warehouseItems     WarehouseItem[]
  shipments          Shipment[]
  outgoingShipments  OutgoingShipment[]
  onboardingSessions OnboardingSession[]
  tenantInvitations  TenantInvitation[]
}

model User {
  id         String   @id @default(cuid())
  email      String   @unique
  name       String?
  password   String?
  authUserId String?  @unique // Supabase auth user ID
  role       Role     @default(WAREHOUSE_MEMBER)
  status     String   @default("Active")
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  // User belongs to tenant (keep this)
  tenantId String?
  tenant   Tenant? @relation(fields: [tenantId], references: [id])

  // Warehouse assignments
  warehouseUsers      WarehouseUser[]
  auditLogs           AuditLog[]
  onboardingSessions  OnboardingSession[]
  invitationsSent     TenantInvitation[]  @relation("InvitedBy")
  invitationsAccepted TenantInvitation[]  @relation("AcceptedBy")
}

model Warehouse {
  id        String   @id @default(cuid())
  name      String
  address   String?
  status    String   @default("Active")
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Warehouse belongs to tenant (keep this)
  tenantId String
  tenant   Tenant @relation(fields: [tenantId], references: [id])

  // Warehouse relationships
  locations         Location[]
  warehouseUsers    WarehouseUser[]
  warehouseItems    WarehouseItem[]
  purchaseOrders    PurchaseOrder[]
  outgoingShipments OutgoingShipment[]

  // Security validation index
  @@index([id, tenantId])
}

enum LocationCategory {
  Receiving
  Storage
  Picking
  Shipping
  Returns
}

enum LocationType {
  FLOOR
  RACK
  SHELF
  BIN
  DOCK
  STAGING
  OFFICE
  ZONE
  OTHER
}

model Location {
  id           String           @id @default(cuid())
  name         String
  category     LocationCategory @default(Storage)
  locationType LocationType
  status       String           @default("Active")
  createdAt    DateTime         @default(now())
  updatedAt    DateTime         @updatedAt

  // Location belongs to warehouse (tenant derived through warehouse)
  warehouseId String
  warehouse   Warehouse @relation(fields: [warehouseId], references: [id])

  // Location relationships
  pallets Pallet[]

  // Optimized indexes for warehouse-scoped queries
  @@index([warehouseId, category])
  @@index([warehouseId, status])
  @@index([warehouseId, name])
}

model Item {
  id                String   @id @default(cuid())
  sku               String?  @unique
  name              String
  description       String?
  unitOfMeasure     String   @default("Each")
  defaultCost       Float?
  lowStockThreshold Int?
  status            String   @default("Active")
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  // Item belongs to tenant (keep this - items are tenant-wide)
  tenantId String
  tenant   Tenant @relation(fields: [tenantId], references: [id])

  // Item relationships
  warehouseItems        WarehouseItem[]
  palletItems           PalletItem[]
  purchaseOrderItems    PurchaseOrderItem[]
  itemLocations         ItemLocation[]
  outgoingShipmentItems OutgoingShipmentItem[]
}

model ItemLocation {
  id          String   @id @default(cuid())
  quantity    Int
  lastUpdated DateTime @updatedAt
  createdAt   DateTime @default(now())

  // Relationships
  itemId String
  item   Item   @relation(fields: [itemId], references: [id])

  palletId String
  pallet   Pallet @relation(fields: [palletId], references: [id])

  // Warehouse scope derived through pallet.location.warehouse
  @@unique([itemId, palletId], name: "item_pallet_location_unique")
  @@index([itemId]) // Fast item location lookup
  @@index([palletId]) // Fast pallet contents lookup
}

model Pallet {
  id                String   @id @default(cuid())
  label             String
  description       String?
  barcode           String?
  shipToDestination String?
  destinationCode   String? // NEW: Optional numerical identifier for destinations
  status            String   @default("Empty")
  dateCreated       DateTime @default(now())
  lastMovedDate     DateTime @updatedAt

  // Pallet belongs to location (tenant derived through location.warehouse)
  locationId String?
  location   Location? @relation(fields: [locationId], references: [id])

  // Pallet relationships
  palletItems             PalletItem[]
  itemLocations           ItemLocation[]
  outgoingShipmentItems   OutgoingShipmentItem[]   @relation("SourcePallet")
  outgoingShipmentPallets OutgoingShipmentPallet[]
  shipmentId              String?
  shipment                Shipment?                @relation(fields: [shipmentId], references: [id])

  // Optimized indexes for warehouse-scoped queries
  @@unique([locationId, barcode], name: "warehouse_pallet_barcode_unique")
  @@index([locationId, status])
  @@index([locationId, shipToDestination])
  @@index([locationId, dateCreated])
  @@index([shipToDestination])
  @@index([destinationCode]) // NEW: Index for destination code lookups
  @@index([shipToDestination, destinationCode]) // NEW: Composite index for combined searches
}

model PalletItem {
  id        String   @id @default(cuid())
  quantity  Int
  dateAdded DateTime @default(now())

  // PalletItem belongs to pallet (tenant derived through pallet.location.warehouse)
  palletId String
  pallet   Pallet @relation(fields: [palletId], references: [id])

  itemId String
  item   Item   @relation(fields: [itemId], references: [id])

  // Simplified unique constraint (no tenant needed)
  @@unique([palletId, itemId], name: "pallet_item_unique")
}

model PurchaseOrder {
  id                   String    @id @default(cuid())
  poNumber             String
  status               String    @default("Pending")
  supplier             String?
  notes                String?
  orderDate            DateTime  @default(now())
  expectedDeliveryDate DateTime?
  createdAt            DateTime  @default(now())
  updatedAt            DateTime  @updatedAt

  // PO belongs to tenant (keep this - POs are tenant-level)
  tenantId String
  tenant   Tenant @relation(fields: [tenantId], references: [id])

  // Optional warehouse assignment
  warehouseId String?
  warehouse   Warehouse? @relation(fields: [warehouseId], references: [id])

  // PO relationships
  items     PurchaseOrderItem[]
  shipments Shipment[]

  @@unique([tenantId, poNumber], name: "tenantId_poNumber")
}

model PurchaseOrderItem {
  id               String        @id @default(cuid())
  quantity         Int
  receivedQuantity Int           @default(0)
  unitCost         Float?
  purchaseOrderId  String
  purchaseOrder    PurchaseOrder @relation(fields: [purchaseOrderId], references: [id])
  itemId           String
  item             Item          @relation(fields: [itemId], references: [id])
}

model AuditLog {
  id        String   @id @default(cuid())
  timestamp DateTime @default(now())
  userId    String?
  userEmail String?
  action    String
  entity    String
  entityId  String
  details   Json?

  // AuditLog belongs to tenant (keep this - auditing is tenant-level)
  tenantId String
  tenant   Tenant @relation(fields: [tenantId], references: [id])

  user User? @relation(fields: [userId], references: [id], onDelete: SetNull)

  @@index([tenantId, timestamp])
  @@index([tenantId, entity, entityId])
  @@index([tenantId, userId])
}

enum Role {
  TENANT_ADMIN
  WAREHOUSE_MANAGER
  WAREHOUSE_MEMBER
}

model WarehouseUser {
  id         String   @id @default(cuid())
  role       Role
  assignedAt DateTime @default(now())

  userId String
  user   User   @relation(fields: [userId], references: [id])

  warehouseId String
  warehouse   Warehouse @relation(fields: [warehouseId], references: [id])

  @@unique([userId, warehouseId])
  @@index([warehouseId, userId])
}

model WarehouseItem {
  id           String @id @default(cuid())
  currentStock Int    @default(0)
  minStock     Int?
  maxStock     Int?

  // WarehouseItem belongs to both warehouse and tenant
  warehouseId String
  warehouse   Warehouse @relation(fields: [warehouseId], references: [id])

  itemId String
  item   Item   @relation(fields: [itemId], references: [id])

  tenantId String
  tenant   Tenant @relation(fields: [tenantId], references: [id])

  @@unique([tenantId, warehouseId, itemId], name: "tenant_warehouse_item_unique")
}

model Shipment {
  id              String   @id @default(cuid())
  referenceNumber String?
  status          String   @default("Pending")
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  purchaseOrderId String        @unique
  purchaseOrder   PurchaseOrder @relation(fields: [purchaseOrderId], references: [id])

  pallets Pallet[]

  // Shipment belongs to tenant (keep this)
  tenantId String
  tenant   Tenant @relation(fields: [tenantId], references: [id])

  @@unique([tenantId, referenceNumber])
  @@index([purchaseOrderId])
}

model OnboardingSession {
  id          String    @id @default(cuid())
  userId      String?   @map("user_id")
  tenantId    String?   @map("tenant_id")
  currentStep String    @default("business_info") @map("current_step")
  data        Json      @default("{}")
  completedAt DateTime? @map("completed_at")
  expiresAt   DateTime  @map("expires_at")
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")

  user   User?   @relation(fields: [userId], references: [id], onDelete: Cascade)
  tenant Tenant? @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@map("onboarding_sessions")
}

model TenantInvitation {
  id               String    @id @default(cuid())
  tenantId         String    @map("tenant_id")
  invitedByUserId  String    @map("invited_by_user_id")
  email            String
  role             Role      @default(WAREHOUSE_MEMBER)
  warehouseIds     String[]  @default([]) @map("warehouse_ids")
  invitationCode   String    @unique @map("invitation_code")
  expiresAt        DateTime  @map("expires_at")
  acceptedAt       DateTime? @map("accepted_at")
  acceptedByUserId String?   @map("accepted_by_user_id")
  createdAt        DateTime  @default(now()) @map("created_at")
  updatedAt        DateTime  @updatedAt @map("updated_at")

  tenant         Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  invitedBy      User   @relation("InvitedBy", fields: [invitedByUserId], references: [id], onDelete: Cascade)
  acceptedByUser User?  @relation("AcceptedBy", fields: [acceptedByUserId], references: [id], onDelete: SetNull)

  @@map("tenant_invitations")
}

enum ShipmentStatus {
  PREPARING
  PACKED
  SHIPPED
  DELIVERED
  CANCELLED
}

model OutgoingShipment {
  id              String         @id @default(cuid())
  shipmentNumber  String // Auto-generated: OS-YYYYMMDD-XXXX
  status          ShipmentStatus @default(PREPARING)
  destination     String?
  destinationCode String?
  notes           String?
  packedAt        DateTime?
  shippedAt       DateTime?
  createdAt       DateTime       @default(now())
  updatedAt       DateTime       @updatedAt

  // Tenant relationship (required for multi-tenant isolation)
  tenantId String
  tenant   Tenant @relation(fields: [tenantId], references: [id])

  // Warehouse relationship (derived through items/pallets)
  warehouseId String?
  warehouse   Warehouse? @relation(fields: [warehouseId], references: [id])

  // Relationships
  shipmentItems   OutgoingShipmentItem[]
  shipmentPallets OutgoingShipmentPallet[]

  @@unique([tenantId, shipmentNumber])
  @@index([tenantId, status])
  @@index([tenantId, warehouseId, status])
  @@index([destination])
  @@index([destinationCode])
}

model OutgoingShipmentItem {
  id       String   @id @default(cuid())
  quantity Int
  addedAt  DateTime @default(now())

  // Relationships
  shipmentId String
  shipment   OutgoingShipment @relation(fields: [shipmentId], references: [id])

  itemId String
  item   Item   @relation(fields: [itemId], references: [id])

  // Optional: Track which pallet the item came from
  sourcePalletId String?
  sourcePallet   Pallet? @relation("SourcePallet", fields: [sourcePalletId], references: [id])

  @@unique([shipmentId, itemId], name: "shipment_item_unique")
}

model OutgoingShipmentPallet {
  id      String   @id @default(cuid())
  addedAt DateTime @default(now())

  // Relationships
  shipmentId String
  shipment   OutgoingShipment @relation(fields: [shipmentId], references: [id])

  palletId String
  pallet   Pallet @relation(fields: [palletId], references: [id])

  @@unique([shipmentId, palletId], name: "shipment_pallet_unique")
}
