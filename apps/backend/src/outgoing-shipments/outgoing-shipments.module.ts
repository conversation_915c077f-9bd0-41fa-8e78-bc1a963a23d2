import { Module } from "@nestjs/common";
import { OutgoingShipmentsController } from "./outgoing-shipments.controller";
import { OutgoingShipmentsService } from "./outgoing-shipments.service";
import { PrismaModule } from "../prisma/prisma.module";
import { WarehousesModule } from "../warehouses/warehouses.module";

@Module({
  imports: [PrismaModule, WarehousesModule],
  controllers: [OutgoingShipmentsController],
  providers: [OutgoingShipmentsService],
  exports: [OutgoingShipmentsService],
})
export class OutgoingShipmentsModule {}
