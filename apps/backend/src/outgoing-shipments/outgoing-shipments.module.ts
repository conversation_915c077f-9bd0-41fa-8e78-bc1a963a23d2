import { Module } from '@nestjs/common';
import { OutgoingShipmentsController } from './outgoing-shipments.controller';
import { OutgoingShipmentsService } from './outgoing-shipments.service';
import { PrismaModule } from '../prisma/prisma.module';

@Module({
  imports: [PrismaModule],
  controllers: [OutgoingShipmentsController],
  providers: [OutgoingShipmentsService],
  exports: [OutgoingShipmentsService],
})
export class OutgoingShipmentsModule {}
