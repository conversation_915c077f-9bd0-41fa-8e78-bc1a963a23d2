import {
  Controller,
  Get,
  Post,
  Put,
  Body,
  Param,
  Query,
  UsePipes,
  ValidationPipe,
  UseGuards,
  UseInterceptors,
  Req,
} from "@nestjs/common";
import { OutgoingShipmentsService } from "./outgoing-shipments.service";
import { CreateShipmentDto } from "./dto/create-shipment.dto";
import { SearchShipmentsDto } from "./dto/search-shipments.dto";
import { UpdateShipmentStatusDto } from "./dto/update-shipment-status.dto";
import { AddItemsToShipmentDto } from "./dto/add-items-to-shipment.dto";
import { ReleaseInventoryDto } from "./dto/release-inventory.dto";
import { JwtAuthGuard } from "../auth/guards/jwt-auth.guard";
import { WarehousePermissionGuard } from "../auth/guards/warehouse-permission.guard";
import {
  RequireWarehouseAccess,
  RequestWithWarehouseContext,
} from "../auth/decorators/warehouse-permission.decorator";
import { LogAction } from "../audit-log/decorators/log-action.decorator";
import { AuditLogInterceptor } from "../audit-log/interceptors/audit-log.interceptor";
import { EnhancedUserPayload } from "../auth/types";

@Controller("shipments")
@UseGuards(JwtAuthGuard, WarehousePermissionGuard)
@UseInterceptors(AuditLogInterceptor)
export class OutgoingShipmentsController {
  constructor(
    private readonly outgoingShipmentsService: OutgoingShipmentsService
  ) {}

  @Post()
  @RequireWarehouseAccess()
  @UsePipes(new ValidationPipe({ whitelist: true, forbidNonWhitelisted: true }))
  @LogAction({
    action: "CREATE_SHIPMENT",
    entity: "OutgoingShipment",
    getEntityId: (context, result) => result?.id,
    getDetails: (context, result) => {
      const request = context.switchToHttp().getRequest();
      return {
        destination: result?.destination,
        destinationCode: result?.destinationCode,
        itemCount: result?.shipmentItems?.length || 0,
        palletCount: result?.shipmentPallets?.length || 0,
        warehouseId: request.warehouseContext?.warehouseId,
        userRole: request.warehouseContext?.userRole,
      };
    },
  })
  async createShipment(
    @Body() createShipmentDto: CreateShipmentDto,
    @Req() req: RequestWithWarehouseContext<{ user: EnhancedUserPayload }>
  ) {
    return this.outgoingShipmentsService.create(createShipmentDto, req.user);
  }

  @Get("search")
  @RequireWarehouseAccess()
  @UsePipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
    })
  )
  @LogAction({
    action: "SEARCH_SHIPMENTS",
    entity: "OutgoingShipment",
  })
  async searchShipments(
    @Query() searchDto: SearchShipmentsDto,
    @Req() req: RequestWithWarehouseContext<{ user: EnhancedUserPayload }>
  ) {
    return this.outgoingShipmentsService.search(
      {
        ...searchDto,
        warehouseId: searchDto.warehouseId || req.warehouseContext?.warehouseId,
      },
      req.user
    );
  }

  @Put(":id/status")
  @RequireWarehouseAccess()
  @UsePipes(new ValidationPipe({ whitelist: true, forbidNonWhitelisted: true }))
  @LogAction({
    action: "UPDATE_SHIPMENT_STATUS",
    entity: "OutgoingShipment",
    getEntityId: (context) => context.switchToHttp().getRequest().params.id,
    getDetails: (context, result) => ({
      oldStatus: context.switchToHttp().getRequest().body.oldStatus,
      newStatus: result?.status,
      notes: result?.notes,
    }),
  })
  async updateShipmentStatus(
    @Param("id") shipmentId: string,
    @Body() updateStatusDto: UpdateShipmentStatusDto,
    @Req() req: RequestWithWarehouseContext<{ user: EnhancedUserPayload }>
  ) {
    // TODO: Implement updateStatus method in service
    throw new Error("Method not implemented yet");
  }

  @Post(":id/items")
  @RequireWarehouseAccess()
  @UsePipes(new ValidationPipe({ whitelist: true, forbidNonWhitelisted: true }))
  @LogAction({
    action: "ADD_ITEMS_TO_SHIPMENT",
    entity: "OutgoingShipment",
    getEntityId: (context) => context.switchToHttp().getRequest().params.id,
    getDetails: (context, result) => ({
      itemsAdded: result?.addedItems?.length || 0,
      totalItems: result?.updatedTotals?.totalItems || 0,
    }),
  })
  async addItemsToShipment(
    @Param("id") shipmentId: string,
    @Body() addItemsDto: AddItemsToShipmentDto,
    @Req() req: RequestWithWarehouseContext<{ user: EnhancedUserPayload }>
  ) {
    // TODO: Implement addItems method in service
    throw new Error("Method not implemented yet");
  }

  @Get(":id/packing-list")
  @RequireWarehouseAccess()
  @LogAction({
    action: "GENERATE_PACKING_LIST",
    entity: "OutgoingShipment",
    getEntityId: (context) => context.switchToHttp().getRequest().params.id,
  })
  async getPackingList(
    @Param("id") shipmentId: string,
    @Req() req: RequestWithWarehouseContext<{ user: EnhancedUserPayload }>
  ) {
    return this.outgoingShipmentsService.getPackingList(shipmentId, req.user);
  }

  @Post(":id/release")
  @RequireWarehouseAccess()
  @UsePipes(new ValidationPipe({ whitelist: true, forbidNonWhitelisted: true }))
  @LogAction({
    action: "RELEASE_INVENTORY",
    entity: "OutgoingShipment",
    getEntityId: (context) => context.switchToHttp().getRequest().params.id,
    getDetails: (context, result) => ({
      itemsReleased: result?.inventoryUpdates?.itemsReleased || 0,
      palletsReleased: result?.inventoryUpdates?.palletsReleased || 0,
      totalQuantityReleased:
        result?.inventoryUpdates?.totalQuantityReleased || 0,
      trackingNumber: result?.trackingNumber,
    }),
  })
  async releaseInventory(
    @Param("id") shipmentId: string,
    @Body() releaseDto: ReleaseInventoryDto,
    @Req() req: RequestWithWarehouseContext<{ user: EnhancedUserPayload }>
  ) {
    return this.outgoingShipmentsService.releaseInventory(
      shipmentId,
      releaseDto,
      req.user
    );
  }
}
