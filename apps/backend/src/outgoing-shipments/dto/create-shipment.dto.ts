import { 
  IsString, 
  IsOptional, 
  Is<PERSON>rray, 
  ValidateNested, 
  <PERSON>Int, 
  Min, 
  Max, 
  Length, 
  Matches,
  IsUUID 
} from 'class-validator';
import { Type } from 'class-transformer';

export class ShipmentItemDto {
  @IsString()
  @IsUUID()
  itemId: string;

  @IsInt()
  @Min(1)
  @Max(10000)
  quantity: number;

  @IsOptional()
  @IsString()
  @IsUUID()
  sourcePalletId?: string;
}

export class CreateShipmentDto {
  @IsString()
  @Length(1, 255)
  destination: string;

  @IsOptional()
  @IsString()
  @Matches(/^\d+$/, { message: 'Destination code must contain only numbers' })
  destinationCode?: string;

  @IsOptional()
  @IsString()
  @Length(0, 1000)
  notes?: string;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ShipmentItemDto)
  items: ShipmentItemDto[];

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @IsUUID(undefined, { each: true })
  pallets?: string[];
}
