import { IsString, IsOptional, <PERSON>Int, <PERSON>, <PERSON>, <PERSON>E<PERSON> } from 'class-validator';
import { Transform } from 'class-transformer';
import { ShipmentStatus } from '@prisma/client';

export class SearchShipmentsDto {
  @IsOptional()
  @IsEnum(ShipmentStatus)
  status?: ShipmentStatus;

  @IsOptional()
  @IsString()
  destination?: string;

  @IsOptional()
  @IsString()
  warehouseId?: string;

  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number = 20;

  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsInt()
  @Min(0)
  offset?: number = 0;
}
