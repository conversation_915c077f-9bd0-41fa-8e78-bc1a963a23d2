import "reflect-metadata";
import { Test, TestingModule } from "@nestjs/testing";
import { PrismaService } from "../prisma/prisma.service";
import { OutgoingShipmentsService } from "./outgoing-shipments.service";
import { OutgoingShipmentsModule } from "./outgoing-shipments.module";
import { PrismaModule } from "../prisma/prisma.module";
import { cleanupDatabase } from "../../test/utils/db-cleanup";
import { CreateShipmentDto } from "./dto/create-shipment.dto";
import { UpdateShipmentStatusDto } from "./dto/update-shipment-status.dto";
import { ReleaseInventoryDto } from "./dto/release-inventory.dto";
import {
  NotFoundException,
  BadRequestException,
  ForbiddenException,
} from "@nestjs/common";
import { AuthenticatedUser } from "../auth/types/authenticated-user.interface";
import {
  Prisma,
  Tenant,
  Warehouse,
  Item,
  OutgoingShipment,
  Role as PrismaRole,
} from "@prisma/client";

describe("OutgoingShipmentsService Integration", () => {
  let service: OutgoingShipmentsService;
  let prisma: PrismaService;
  let module: TestingModule;

  let testTenant: Tenant;
  let testWarehouse1: Warehouse;
  let testWarehouse2: Warehouse;
  let testItem: Item;

  let mockAdminUser: AuthenticatedUser;
  let mockWarehouseMemberUser: AuthenticatedUser;

  beforeAll(async () => {
    // Module setup handled in beforeEach
  });

  afterAll(async () => {
    await module?.close();
  });

  beforeEach(async () => {
    module = await Test.createTestingModule({
      imports: [PrismaModule, OutgoingShipmentsModule],
    }).compile();

    prisma = module.get<PrismaService>(PrismaService);
    service = module.get<OutgoingShipmentsService>(OutgoingShipmentsService);

    await cleanupDatabase(prisma);

    testTenant = await prisma.tenant.create({
      data: { name: "Test Tenant for Shipments" },
    });

    testWarehouse1 = await prisma.warehouse.create({
      data: { name: "Test Warehouse 1", tenantId: testTenant.id },
    });
    testWarehouse2 = await prisma.warehouse.create({
      data: { name: "Test Warehouse 2", tenantId: testTenant.id },
    });

    testItem = await prisma.item.create({
      data: {
        name: "Test Item",
        sku: "TEST-001",
        unitOfMeasure: "pcs",
        tenantId: testTenant.id,
      },
    });

    // Associate item with warehouse
    await prisma.warehouseItem.create({
      data: {
        itemId: testItem.id,
        warehouseId: testWarehouse1.id,
        tenantId: testTenant.id,
      },
    });

    // Mock Users
    mockAdminUser = {
      id: "admin-user-id",
      email: "<EMAIL>",
      role: PrismaRole.TENANT_ADMIN,
      tenantId: testTenant.id,
      name: "Admin User",
      authUserId: "auth-admin-user-id",
      warehouseUsers: [
        { warehouseId: testWarehouse1.id, role: PrismaRole.WAREHOUSE_MEMBER },
        { warehouseId: testWarehouse2.id, role: PrismaRole.WAREHOUSE_MEMBER },
      ],
    };

    mockWarehouseMemberUser = {
      id: "whmember-user-id",
      email: "<EMAIL>",
      role: PrismaRole.WAREHOUSE_MEMBER,
      tenantId: testTenant.id,
      name: "Warehouse Member User",
      authUserId: "auth-whmember-user-id",
      warehouseUsers: [
        { warehouseId: testWarehouse1.id, role: PrismaRole.WAREHOUSE_MEMBER },
      ],
    };
  });

  it("should be defined", () => {
    expect(service).toBeDefined();
  });

  describe("create()", () => {
    const createDto: CreateShipmentDto = {
      destination: "Customer ABC",
      destinationCode: "12345",
      items: [
        {
          itemId: "item-1",
          quantity: 10,
          sourcePalletId: "pallet-1",
        },
      ],
      notes: "Test shipment",
    };

    it("should create a new shipment with items", async () => {
      // Create test pallet and item location first
      const testLocation = await prisma.location.create({
        data: {
          name: "A-01-01",
          category: "Storage",
          warehouseId: testWarehouse1.id,
        },
      });

      const testPallet = await prisma.pallet.create({
        data: {
          barcode: "PAL-001",
          status: "Stored",
          locationId: testLocation.id,
          warehouseId: testWarehouse1.id,
          tenantId: testTenant.id,
        },
      });

      await prisma.itemLocation.create({
        data: {
          itemId: testItem.id,
          palletId: testPallet.id,
          quantity: 50,
          warehouseId: testWarehouse1.id,
          tenantId: testTenant.id,
        },
      });

      const shipmentDto: CreateShipmentDto = {
        destination: "Customer ABC",
        destinationCode: "12345",
        items: [
          {
            itemId: testItem.id,
            quantity: 10,
            sourcePalletId: testPallet.id,
          },
        ],
        notes: "Test shipment",
      };

      const shipment = await service.create(shipmentDto, mockWarehouseMemberUser);

      expect(shipment).toBeDefined();
      expect(shipment.destination).toBe("Customer ABC");
      expect(shipment.destinationCode).toBe("12345");
      expect(shipment.status).toBe("PREPARING");
      expect(shipment.warehouseId).toBe(testWarehouse1.id);
      expect(shipment.tenantId).toBe(testTenant.id);

      // Verify shipment items were created
      const shipmentItems = await prisma.outgoingShipmentItem.findMany({
        where: { shipmentId: shipment.id },
      });
      expect(shipmentItems.length).toBe(1);
      expect(shipmentItems[0].quantity).toBe(10);
    });

    it("should throw BadRequestException for insufficient inventory", async () => {
      const testLocation = await prisma.location.create({
        data: {
          name: "A-01-01",
          category: "Storage", 
          warehouseId: testWarehouse1.id,
        },
      });

      const testPallet = await prisma.pallet.create({
        data: {
          barcode: "PAL-001",
          status: "Stored",
          locationId: testLocation.id,
          warehouseId: testWarehouse1.id,
          tenantId: testTenant.id,
        },
      });

      await prisma.itemLocation.create({
        data: {
          itemId: testItem.id,
          palletId: testPallet.id,
          quantity: 5, // Only 5 available
          warehouseId: testWarehouse1.id,
          tenantId: testTenant.id,
        },
      });

      const shipmentDto: CreateShipmentDto = {
        destination: "Customer ABC",
        items: [
          {
            itemId: testItem.id,
            quantity: 10, // Requesting more than available
            sourcePalletId: testPallet.id,
          },
        ],
      };

      await expect(
        service.create(shipmentDto, mockWarehouseMemberUser)
      ).rejects.toThrow(BadRequestException);
    });
  });

  describe("search()", () => {
    let testShipment1: OutgoingShipment;
    let testShipment2: OutgoingShipment;

    beforeEach(async () => {
      testShipment1 = await prisma.outgoingShipment.create({
        data: {
          destination: "Customer ABC",
          status: "PREPARING",
          warehouseId: testWarehouse1.id,
          tenantId: testTenant.id,
          createdBy: mockWarehouseMemberUser.id,
        },
      });

      testShipment2 = await prisma.outgoingShipment.create({
        data: {
          destination: "Customer XYZ",
          status: "PACKED",
          warehouseId: testWarehouse1.id,
          tenantId: testTenant.id,
          createdBy: mockWarehouseMemberUser.id,
        },
      });
    });

    it("should search shipments by status", async () => {
      const results = await service.search(
        { status: "PREPARING", warehouseId: testWarehouse1.id },
        mockWarehouseMemberUser
      );

      expect(results.length).toBe(1);
      expect(results[0].id).toBe(testShipment1.id);
      expect(results[0].status).toBe("PREPARING");
    });

    it("should search shipments by destination", async () => {
      const results = await service.search(
        { destination: "ABC", warehouseId: testWarehouse1.id },
        mockWarehouseMemberUser
      );

      expect(results.length).toBe(1);
      expect(results[0].destination).toBe("Customer ABC");
    });

    it("should respect warehouse scoping", async () => {
      const results = await service.search(
        { warehouseId: testWarehouse2.id },
        mockWarehouseMemberUser
      );

      expect(results.length).toBe(0); // No shipments in warehouse2
    });

    it("should handle pagination", async () => {
      const results = await service.search(
        { warehouseId: testWarehouse1.id, limit: 1, offset: 0 },
        mockWarehouseMemberUser
      );

      expect(results.length).toBe(1);
    });
  });

  describe("updateStatus()", () => {
    let testShipment: OutgoingShipment;

    beforeEach(async () => {
      testShipment = await prisma.outgoingShipment.create({
        data: {
          destination: "Customer ABC",
          status: "PREPARING",
          warehouseId: testWarehouse1.id,
          tenantId: testTenant.id,
          createdBy: mockWarehouseMemberUser.id,
        },
      });
    });

    it("should update shipment status", async () => {
      const updateDto: UpdateShipmentStatusDto = {
        status: "PACKED",
        notes: "Shipment packed and ready",
      };

      const updated = await service.updateStatus(
        testShipment.id,
        updateDto,
        mockWarehouseMemberUser
      );

      expect(updated.status).toBe("PACKED");
      expect(updated.notes).toBe("Shipment packed and ready");
    });

    it("should throw NotFoundException for non-existent shipment", async () => {
      await expect(
        service.updateStatus(
          "non-existent-id",
          { status: "PACKED" },
          mockWarehouseMemberUser
        )
      ).rejects.toThrow(NotFoundException);
    });
  });

  describe("releaseInventory()", () => {
    let testShipment: OutgoingShipment;

    beforeEach(async () => {
      testShipment = await prisma.outgoingShipment.create({
        data: {
          destination: "Customer ABC",
          status: "PACKED",
          warehouseId: testWarehouse1.id,
          tenantId: testTenant.id,
          createdBy: mockWarehouseMemberUser.id,
        },
      });
    });

    it("should release inventory and update status to SHIPPED", async () => {
      const releaseDto: ReleaseInventoryDto = {
        trackingNumber: "TRACK-123",
        releaseNotes: "Released for shipping",
      };

      const released = await service.releaseInventory(
        testShipment.id,
        releaseDto,
        mockWarehouseMemberUser
      );

      expect(released.status).toBe("SHIPPED");
      expect(released.trackingNumber).toBe("TRACK-123");
      expect(released.releaseNotes).toBe("Released for shipping");
    });

    it("should throw BadRequestException for unpacked shipment", async () => {
      const unpackedShipment = await prisma.outgoingShipment.create({
        data: {
          destination: "Customer DEF",
          status: "PREPARING",
          warehouseId: testWarehouse1.id,
          tenantId: testTenant.id,
          createdBy: mockWarehouseMemberUser.id,
        },
      });

      await expect(
        service.releaseInventory(
          unpackedShipment.id,
          { trackingNumber: "TRACK-456" },
          mockWarehouseMemberUser
        )
      ).rejects.toThrow(BadRequestException);
    });
  });
});
