import {
  Injectable,
  NotFoundException,
  ForbiddenException,
  BadRequestException,
} from "@nestjs/common";
import { PrismaService } from "../prisma/prisma.service";
import { CreateShipmentDto, ShipmentItemDto } from "./dto/create-shipment.dto";
import { SearchShipmentsDto } from "./dto/search-shipments.dto";
import { UpdateShipmentStatusDto } from "./dto/update-shipment-status.dto";
import { AddItemsToShipmentDto } from "./dto/add-items-to-shipment.dto";
import { ReleaseInventoryDto } from "./dto/release-inventory.dto";
import { OutgoingShipment, ShipmentStatus, Prisma } from "@prisma/client";
import { AuthenticatedUser } from "../auth/types/authenticated-user.interface";
import { Role } from "../auth/entities/role.enum";

export interface OutgoingShipmentWithDetails extends OutgoingShipment {
  shipmentItems: Array<{
    id: string;
    itemId: string;
    itemName: string;
    quantity: number;
    sourcePalletId: string | null;
  }>;
  shipmentPallets: Array<{
    id: string;
    palletId: string;
    palletBarcode: string | null;
  }>;
}

export interface ShipmentSearchResult {
  id: string;
  shipmentNumber: string;
  status: ShipmentStatus;
  destination: string | null;
  destinationCode: string | null;
  itemCount: number;
  palletCount: number;
  createdAt: Date;
}

export interface ShipmentSearchResponse {
  shipments: ShipmentSearchResult[];
  pagination: {
    total: number;
    limit: number;
    offset: number;
    hasMore: boolean;
  };
  filters: {
    status: ShipmentStatus[];
    destinations: string[];
  };
}

export interface PackingListItem {
  sku: string | null;
  name: string;
  quantity: number;
  unitOfMeasure: string;
  sourcePallet: string | null;
}

export interface PackingListPallet {
  barcode: string | null;
  description: string | null;
  itemCount: number;
  totalQuantity: number;
}

export interface PackingListResponse {
  shipmentId: string;
  shipmentNumber: string;
  destination: string | null;
  destinationCode: string | null;
  generatedAt: string;
  items: PackingListItem[];
  pallets: PackingListPallet[];
  summary: {
    totalItems: number;
    totalQuantity: number;
    totalPallets: number;
  };
}

export interface InventoryReleaseResponse {
  shipmentId: string;
  status: ShipmentStatus;
  shippedAt: string;
  trackingNumber: string | null;
  releasedItems: Array<{
    itemId: string;
    quantity: number;
    releasedFromPallet: string;
  }>;
  releasedPallets: string[];
  inventoryUpdates: {
    itemsReleased: number;
    palletsReleased: number;
    totalQuantityReleased: number;
  };
}

@Injectable()
export class OutgoingShipmentsService {
  constructor(private prisma: PrismaService) {}

  /**
   * Create a new outgoing shipment with items and pallets
   */
  async create(
    createShipmentDto: CreateShipmentDto,
    currentUser: AuthenticatedUser
  ): Promise<OutgoingShipmentWithDetails> {
    // Validate that at least one item or pallet is provided
    if (
      (!createShipmentDto.items || createShipmentDto.items.length === 0) &&
      (!createShipmentDto.pallets || createShipmentDto.pallets.length === 0)
    ) {
      throw new BadRequestException(
        "At least one item or pallet must be included in the shipment"
      );
    }

    return this.prisma.$transaction(async (tx) => {
      // 1. Validate inventory availability
      if (createShipmentDto.items && createShipmentDto.items.length > 0) {
        await this.validateInventoryAvailability(
          createShipmentDto.items,
          currentUser,
          tx
        );
      }

      // 2. Validate pallet access
      if (createShipmentDto.pallets && createShipmentDto.pallets.length > 0) {
        await this.validatePalletAccess(
          createShipmentDto.pallets,
          currentUser,
          tx
        );
      }

      // 3. Determine warehouse from items/pallets
      const warehouseId = await this.determineWarehouse(
        createShipmentDto,
        currentUser,
        tx
      );

      // 4. Generate shipment number
      const shipmentNumber = await this.generateShipmentNumber(tx);

      // 5. Create shipment record
      const shipment = await tx.outgoingShipment.create({
        data: {
          shipmentNumber,
          destination: createShipmentDto.destination,
          destinationCode: createShipmentDto.destinationCode,
          notes: createShipmentDto.notes,
          tenantId: currentUser.tenantId,
          warehouseId,
          status: ShipmentStatus.PREPARING,
        },
      });

      // 6. Create shipment items
      const shipmentItems = [];
      if (createShipmentDto.items && createShipmentDto.items.length > 0) {
        for (const itemDto of createShipmentDto.items) {
          const shipmentItem = await tx.outgoingShipmentItem.create({
            data: {
              shipmentId: shipment.id,
              itemId: itemDto.itemId,
              quantity: itemDto.quantity,
              sourcePalletId: itemDto.sourcePalletId,
            },
            include: {
              item: { select: { name: true } },
            },
          });

          shipmentItems.push({
            id: shipmentItem.id,
            itemId: shipmentItem.itemId,
            itemName: shipmentItem.item.name,
            quantity: shipmentItem.quantity,
            sourcePalletId: shipmentItem.sourcePalletId,
          });
        }
      }

      // 7. Create shipment pallets
      const shipmentPallets = [];
      if (createShipmentDto.pallets && createShipmentDto.pallets.length > 0) {
        for (const palletId of createShipmentDto.pallets) {
          const shipmentPallet = await tx.outgoingShipmentPallet.create({
            data: {
              shipmentId: shipment.id,
              palletId,
            },
            include: {
              pallet: { select: { barcode: true } },
            },
          });

          shipmentPallets.push({
            id: shipmentPallet.id,
            palletId: shipmentPallet.palletId,
            palletBarcode: shipmentPallet.pallet.barcode,
          });
        }
      }

      return {
        ...shipment,
        shipmentItems,
        shipmentPallets,
      };
    });
  }

  /**
   * Validate that requested items are available in sufficient quantities
   */
  private async validateInventoryAvailability(
    items: ShipmentItemDto[],
    currentUser: AuthenticatedUser,
    tx: Prisma.TransactionClient
  ): Promise<void> {
    for (const item of items) {
      // Get available quantity for this item
      const itemLocations = await tx.itemLocation.findMany({
        where: {
          itemId: item.itemId,
          pallet: {
            location: {
              warehouse: {
                tenantId: currentUser.tenantId,
              },
            },
          },
          ...(item.sourcePalletId && { palletId: item.sourcePalletId }),
        },
      });

      const availableQuantity = itemLocations.reduce(
        (sum, location) => sum + location.quantity,
        0
      );

      if (availableQuantity < item.quantity) {
        const itemRecord = await tx.item.findUnique({
          where: { id: item.itemId },
          select: { name: true },
        });

        throw new BadRequestException(
          `Insufficient inventory for item "${
            itemRecord?.name || item.itemId
          }". ` + `Requested: ${item.quantity}, Available: ${availableQuantity}`
        );
      }
    }
  }

  /**
   * Validate that user has access to specified pallets
   */
  private async validatePalletAccess(
    palletIds: string[],
    currentUser: AuthenticatedUser,
    tx: Prisma.TransactionClient
  ): Promise<void> {
    const pallets = await tx.pallet.findMany({
      where: {
        id: { in: palletIds },
        location: {
          warehouse: {
            tenantId: currentUser.tenantId,
          },
        },
      },
      include: {
        location: {
          include: {
            warehouse: true,
          },
        },
      },
    });

    if (pallets.length !== palletIds.length) {
      const foundIds = pallets.map((p) => p.id);
      const missingIds = palletIds.filter((id) => !foundIds.includes(id));
      throw new NotFoundException(
        `Pallets not found or not accessible: ${missingIds.join(", ")}`
      );
    }

    // For non-admin users, validate warehouse access
    if (currentUser.role !== Role.TENANT_ADMIN) {
      const userWarehouseIds =
        currentUser.warehouseUsers?.map((wu) => wu.warehouseId) || [];

      for (const pallet of pallets) {
        if (!userWarehouseIds.includes(pallet.location!.warehouseId)) {
          throw new ForbiddenException(
            `You do not have access to pallet "${pallet.label}" in warehouse "${
              pallet.location!.warehouse.name
            }"`
          );
        }
      }
    }
  }

  /**
   * Determine warehouse from items and pallets
   */
  private async determineWarehouse(
    createShipmentDto: CreateShipmentDto,
    currentUser: AuthenticatedUser,
    tx: Prisma.TransactionClient
  ): Promise<string | null> {
    const warehouseIds = new Set<string>();

    // Get warehouses from items
    if (createShipmentDto.items && createShipmentDto.items.length > 0) {
      const itemLocations = await tx.itemLocation.findMany({
        where: {
          itemId: { in: createShipmentDto.items.map((i) => i.itemId) },
          pallet: {
            location: {
              warehouse: {
                tenantId: currentUser.tenantId,
              },
            },
          },
        },
        include: {
          pallet: {
            include: {
              location: true,
            },
          },
        },
      });

      itemLocations.forEach((location) => {
        if (location.pallet.location) {
          warehouseIds.add(location.pallet.location.warehouseId);
        }
      });
    }

    // Get warehouses from pallets
    if (createShipmentDto.pallets && createShipmentDto.pallets.length > 0) {
      const pallets = await tx.pallet.findMany({
        where: {
          id: { in: createShipmentDto.pallets },
          location: {
            warehouse: {
              tenantId: currentUser.tenantId,
            },
          },
        },
        include: {
          location: true,
        },
      });

      pallets.forEach((pallet) => {
        if (pallet.location) {
          warehouseIds.add(pallet.location.warehouseId);
        }
      });
    }

    // For now, return the first warehouse found
    // In a more complex scenario, we might require all items to be from the same warehouse
    return warehouseIds.size > 0 ? Array.from(warehouseIds)[0] : null;
  }

  /**
   * Generate unique shipment number: OS-YYYYMMDD-XXXX
   */
  private async generateShipmentNumber(
    tx: Prisma.TransactionClient
  ): Promise<string> {
    const today = new Date();
    const dateStr = today.toISOString().slice(0, 10).replace(/-/g, "");
    const prefix = `OS-${dateStr}-`;

    // Find the highest sequence number for today
    const lastShipment = await tx.outgoingShipment.findFirst({
      where: {
        shipmentNumber: {
          startsWith: prefix,
        },
      },
      orderBy: {
        shipmentNumber: "desc",
      },
    });

    let sequence = 1;
    if (lastShipment) {
      const lastSequence = parseInt(lastShipment.shipmentNumber.split("-")[2]);
      sequence = lastSequence + 1;
    }

    return `${prefix}${sequence.toString().padStart(4, "0")}`;
  }

  /**
   * Search and filter shipments
   */
  async search(
    query: SearchShipmentsDto,
    currentUser: AuthenticatedUser
  ): Promise<ShipmentSearchResponse> {
    const { status, destination, warehouseId, limit = 20, offset = 0 } = query;

    // Build base where clause for tenant scoping
    const whereClause: Prisma.OutgoingShipmentWhereInput = {
      tenantId: currentUser.tenantId,
    };

    // Apply status filter
    if (status) {
      whereClause.status = status;
    }

    // Apply destination filter
    if (destination) {
      whereClause.OR = [
        {
          destination: {
            contains: destination,
            mode: "insensitive",
          },
        },
        {
          destinationCode: {
            contains: destination,
            mode: "insensitive",
          },
        },
      ];
    }

    // Apply warehouse filtering
    if (warehouseId) {
      // Validate user has access to the specified warehouse
      await this.validateWarehouseAccess(warehouseId, currentUser);
      whereClause.warehouseId = warehouseId;
    } else if (currentUser.role !== Role.TENANT_ADMIN) {
      // For non-admin users, filter by their assigned warehouses
      const userWarehouseIds =
        currentUser.warehouseUsers?.map((wu) => wu.warehouseId) || [];

      if (userWarehouseIds.length === 0) {
        return {
          shipments: [],
          pagination: { total: 0, limit, offset, hasMore: false },
          filters: { status: [], destinations: [] },
        };
      }

      whereClause.warehouseId = {
        in: userWarehouseIds,
      };
    }

    // Get total count for pagination
    const total = await this.prisma.outgoingShipment.count({
      where: whereClause,
    });

    // Fetch shipments with pagination
    const shipments = await this.prisma.outgoingShipment.findMany({
      where: whereClause,
      include: {
        shipmentItems: true,
        shipmentPallets: true,
      },
      orderBy: { createdAt: "desc" },
      skip: offset,
      take: limit,
    });

    // Transform to search results
    const searchResults: ShipmentSearchResult[] = shipments.map((shipment) => ({
      id: shipment.id,
      shipmentNumber: shipment.shipmentNumber,
      status: shipment.status,
      destination: shipment.destination,
      destinationCode: shipment.destinationCode,
      itemCount: shipment.shipmentItems.length,
      palletCount: shipment.shipmentPallets.length,
      createdAt: shipment.createdAt,
    }));

    // Get filter options for the response
    const allShipments = await this.prisma.outgoingShipment.findMany({
      where: {
        tenantId: currentUser.tenantId,
        ...(currentUser.role !== Role.TENANT_ADMIN && {
          warehouseId: {
            in: currentUser.warehouseUsers?.map((wu) => wu.warehouseId) || [],
          },
        }),
      },
      select: {
        status: true,
        destination: true,
      },
    });

    const statusOptions = [...new Set(allShipments.map((s) => s.status))];
    const destinationOptions = [
      ...new Set(allShipments.map((s) => s.destination).filter(Boolean)),
    ] as string[];

    return {
      shipments: searchResults,
      pagination: {
        total,
        limit,
        offset,
        hasMore: offset + limit < total,
      },
      filters: {
        status: statusOptions,
        destinations: destinationOptions,
      },
    };
  }

  /**
   * Validate that the user has access to the specified warehouse
   */
  private async validateWarehouseAccess(
    warehouseId: string,
    currentUser: AuthenticatedUser
  ): Promise<void> {
    // TENANT_ADMIN has access to all warehouses in their tenant
    if (currentUser.role === Role.TENANT_ADMIN) {
      const warehouse = await this.prisma.warehouse.findFirst({
        where: {
          id: warehouseId,
          tenantId: currentUser.tenantId,
        },
      });

      if (!warehouse) {
        throw new NotFoundException(
          `Warehouse with ID "${warehouseId}" not found in your tenant.`
        );
      }
      return;
    }

    // Other roles must be explicitly assigned to the warehouse
    const userWarehouseIds =
      currentUser.warehouseUsers?.map((wu) => wu.warehouseId) || [];

    if (!userWarehouseIds.includes(warehouseId)) {
      throw new ForbiddenException(
        `You do not have access to warehouse "${warehouseId}".`
      );
    }

    // Verify warehouse exists and belongs to user's tenant
    const warehouse = await this.prisma.warehouse.findFirst({
      where: {
        id: warehouseId,
        tenantId: currentUser.tenantId,
      },
    });

    if (!warehouse) {
      throw new NotFoundException(
        `Warehouse with ID "${warehouseId}" not found in your tenant.`
      );
    }
  }

  /**
   * Generate packing list for a shipment
   */
  async getPackingList(
    shipmentId: string,
    currentUser: AuthenticatedUser
  ): Promise<PackingListResponse> {
    // Get shipment with all details
    const shipment = await this.prisma.outgoingShipment.findFirst({
      where: {
        id: shipmentId,
        tenantId: currentUser.tenantId,
      },
      include: {
        shipmentItems: {
          include: {
            item: true,
            sourcePallet: {
              select: {
                barcode: true,
                label: true,
              },
            },
          },
        },
        shipmentPallets: {
          include: {
            pallet: {
              include: {
                palletItems: {
                  include: {
                    item: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    if (!shipment) {
      throw new NotFoundException(
        `Shipment with ID "${shipmentId}" not found in your tenant.`
      );
    }

    // Validate warehouse access
    if (shipment.warehouseId) {
      await this.validateWarehouseAccess(shipment.warehouseId, currentUser);
    }

    // Process items for packing list
    const packingListItems: PackingListItem[] = shipment.shipmentItems.map(
      (shipmentItem) => ({
        sku: shipmentItem.item.sku,
        name: shipmentItem.item.name,
        quantity: shipmentItem.quantity,
        unitOfMeasure: shipmentItem.item.unitOfMeasure,
        sourcePallet:
          shipmentItem.sourcePallet?.barcode ||
          shipmentItem.sourcePallet?.label ||
          null,
      })
    );

    // Process pallets for packing list
    const packingListPallets: PackingListPallet[] =
      shipment.shipmentPallets.map((shipmentPallet) => {
        const pallet = shipmentPallet.pallet;
        const totalQuantity = pallet.palletItems.reduce(
          (sum, item) => sum + item.quantity,
          0
        );

        return {
          barcode: pallet.barcode,
          description: pallet.description,
          itemCount: pallet.palletItems.length,
          totalQuantity,
        };
      });

    // Calculate summary
    const totalItems = packingListItems.length;
    const totalQuantity = packingListItems.reduce(
      (sum, item) => sum + item.quantity,
      0
    );
    const totalPallets = packingListPallets.length;

    return {
      shipmentId: shipment.id,
      shipmentNumber: shipment.shipmentNumber,
      destination: shipment.destination,
      destinationCode: shipment.destinationCode,
      generatedAt: new Date().toISOString(),
      items: packingListItems,
      pallets: packingListPallets,
      summary: {
        totalItems,
        totalQuantity,
        totalPallets,
      },
    };
  }

  /**
   * Release inventory for a shipment (mark as shipped and update inventory)
   */
  async releaseInventory(
    shipmentId: string,
    releaseDto: ReleaseInventoryDto,
    currentUser: AuthenticatedUser
  ): Promise<InventoryReleaseResponse> {
    return this.prisma.$transaction(async (tx) => {
      // Get shipment with all details
      const shipment = await tx.outgoingShipment.findFirst({
        where: {
          id: shipmentId,
          tenantId: currentUser.tenantId,
        },
        include: {
          shipmentItems: {
            include: {
              item: true,
              sourcePallet: true,
            },
          },
          shipmentPallets: {
            include: {
              pallet: true,
            },
          },
        },
      });

      if (!shipment) {
        throw new NotFoundException(
          `Shipment with ID "${shipmentId}" not found in your tenant.`
        );
      }

      // Validate warehouse access
      if (shipment.warehouseId) {
        await this.validateWarehouseAccess(shipment.warehouseId, currentUser);
      }

      // Validate shipment can be released
      if (shipment.status === ShipmentStatus.SHIPPED) {
        throw new BadRequestException("Shipment has already been shipped");
      }

      if (shipment.status === ShipmentStatus.CANCELLED) {
        throw new BadRequestException("Cannot ship a cancelled shipment");
      }

      // Update shipment status
      const updatedShipment = await tx.outgoingShipment.update({
        where: { id: shipmentId },
        data: {
          status: ShipmentStatus.SHIPPED,
          shippedAt: new Date(),
          notes: releaseDto.releaseNotes
            ? `${shipment.notes || ""}\n\nShipping Notes: ${
                releaseDto.releaseNotes
              }`.trim()
            : shipment.notes,
        },
      });

      // Process inventory releases for items
      const releasedItems = [];
      let totalQuantityReleased = 0;

      for (const shipmentItem of shipment.shipmentItems) {
        // Reduce inventory from ItemLocation
        if (shipmentItem.sourcePalletId) {
          // Release from specific pallet
          const itemLocation = await tx.itemLocation.findFirst({
            where: {
              itemId: shipmentItem.itemId,
              palletId: shipmentItem.sourcePalletId,
            },
          });

          if (itemLocation && itemLocation.quantity >= shipmentItem.quantity) {
            await tx.itemLocation.update({
              where: { id: itemLocation.id },
              data: {
                quantity: itemLocation.quantity - shipmentItem.quantity,
                lastUpdated: new Date(),
              },
            });

            // Remove ItemLocation if quantity reaches zero
            if (itemLocation.quantity === shipmentItem.quantity) {
              await tx.itemLocation.delete({
                where: { id: itemLocation.id },
              });
            }
          }

          releasedItems.push({
            itemId: shipmentItem.itemId,
            quantity: shipmentItem.quantity,
            releasedFromPallet:
              shipmentItem.sourcePallet?.barcode ||
              shipmentItem.sourcePallet?.label ||
              shipmentItem.sourcePalletId,
          });
        } else {
          // Release from any available location (FIFO)
          const itemLocations = await tx.itemLocation.findMany({
            where: {
              itemId: shipmentItem.itemId,
              pallet: {
                location: {
                  warehouseId: shipment.warehouseId,
                },
              },
            },
            include: {
              pallet: true,
            },
            orderBy: {
              createdAt: "asc", // FIFO
            },
          });

          let remainingToRelease = shipmentItem.quantity;
          for (const location of itemLocations) {
            if (remainingToRelease <= 0) break;

            const releaseFromThisLocation = Math.min(
              location.quantity,
              remainingToRelease
            );

            await tx.itemLocation.update({
              where: { id: location.id },
              data: {
                quantity: location.quantity - releaseFromThisLocation,
                lastUpdated: new Date(),
              },
            });

            // Remove ItemLocation if quantity reaches zero
            if (location.quantity === releaseFromThisLocation) {
              await tx.itemLocation.delete({
                where: { id: location.id },
              });
            }

            releasedItems.push({
              itemId: shipmentItem.itemId,
              quantity: releaseFromThisLocation,
              releasedFromPallet:
                location.pallet.barcode ||
                location.pallet.label ||
                location.palletId,
            });

            remainingToRelease -= releaseFromThisLocation;
          }
        }

        totalQuantityReleased += shipmentItem.quantity;
      }

      // Process pallet releases
      const releasedPallets = [];
      for (const shipmentPallet of shipment.shipmentPallets) {
        // Update pallet status or handle pallet release logic here
        // For now, we'll just track the pallet IDs
        releasedPallets.push(
          shipmentPallet.pallet.barcode ||
            shipmentPallet.pallet.label ||
            shipmentPallet.palletId
        );
      }

      return {
        shipmentId: updatedShipment.id,
        status: updatedShipment.status,
        shippedAt: updatedShipment.shippedAt!.toISOString(),
        trackingNumber: releaseDto.trackingNumber || null,
        releasedItems,
        releasedPallets,
        inventoryUpdates: {
          itemsReleased: releasedItems.length,
          palletsReleased: releasedPallets.length,
          totalQuantityReleased,
        },
      };
    });
  }
}
