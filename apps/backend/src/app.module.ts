// apps/backend/src/app.module.ts

import { Module } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";
import { AppController } from "./app.controller";
import { AppService } from "./app.service";
import { PrismaModule } from "./prisma/prisma.module";
import { ItemsModule } from "./items/items.module";
import { LocationsModule } from "./locations/locations.module";
import { PalletsModule } from "./pallets/pallets.module";
import { WarehousesModule } from "./warehouses/warehouses.module";
import { AuthModule } from "./auth/auth.module";
import { PalletItemsModule } from "./pallet-items/pallet-items.module";
import { ReceivingModule } from "./receiving.module";
import { ShipmentsModule } from "./shipments/shipments.module";
import { ReceivingService } from "./receiving.service";
import { ReceivingController } from "./receiving.controller";
import { OnboardingModule } from "./onboarding/onboarding.module";
import { PurchaseOrdersModule } from "./purchase-orders/purchase-orders.module";
import { UsersModule } from "./users/users.module";
import { AuditLogModule } from "./audit-log/audit-log.module";
import { OutgoingShipmentsModule } from "./outgoing-shipments/outgoing-shipments.module";

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ".env",
    }),
    PrismaModule,
    ItemsModule,
    LocationsModule,
    PalletsModule,
    WarehousesModule,
    AuthModule,
    PalletItemsModule,
    ReceivingModule,
    OnboardingModule,
    PurchaseOrdersModule,
    UsersModule,
    AuditLogModule,
    ShipmentsModule,
    OutgoingShipmentsModule,
  ],
  controllers: [AppController, ReceivingController],
  providers: [AppService, ReceivingService],
})
export class AppModule {}
