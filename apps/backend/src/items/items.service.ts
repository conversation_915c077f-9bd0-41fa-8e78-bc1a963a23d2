import {
  Injectable,
  NotFoundException,
  ForbiddenException,
} from "@nestjs/common";
import { PrismaService } from "../prisma/prisma.service";
import { CreateItemDto } from "./dto/create-item.dto";
import { UpdateItemDto } from "./dto/update-item.dto";
import { Item, User as PrismaUser, Prisma } from "@prisma/client";
import { AuthenticatedUser } from "../auth/types/authenticated-user.interface";
import { Role } from "../auth/entities/role.enum";

// Search-related interfaces
export interface ItemSearchQuery {
  q: string;
  warehouseId?: string;
  limit?: number;
  offset?: number;
}

export interface ItemSearchResult {
  id: string;
  sku: string | null;
  name: string;
  description: string | null;
  unitOfMeasure: string;
  status: string;
  totalQuantity: number;
  availableQuantity: number;
  locationCount: number;
}

export interface ItemSearchResponse {
  items: ItemSearchResult[];
  pagination: {
    total: number;
    limit: number;
    offset: number;
    hasMore: boolean;
  };
}

export interface ItemLocationResult {
  palletId: string;
  palletBarcode: string | null;
  quantity: number;
  location: {
    id: string;
    name: string;
    category: string;
    type: string;
  };
  lastUpdated: string;
}

export interface ItemLocationResponse {
  itemId: string;
  itemName: string;
  totalQuantity: number;
  locations: ItemLocationResult[];
}

// Define an extended User type that includes warehouseUsers from the strategy
// This should ideally be a shared type if used in multiple services.
interface ExtendedUser extends PrismaUser {
  warehouseUsers?: { warehouseId: string }[];
}

// Define interfaces for the detailed response
interface PalletLocationStock {
  palletId: string;
  locationName: string | null;
  warehouseName: string | null;
  quantity: number;
}

export interface ItemDetailResponse extends Item {
  quantityOnHand: number;
  numberOfPallets: number;
  stockByLocationAndPallet: PalletLocationStock[];
}

@Injectable()
export class ItemsService {
  constructor(private prisma: PrismaService) {}

  /**
   * Search items by name or SKU with warehouse context
   * Supports pagination and warehouse-scoped filtering
   */
  async search(
    query: ItemSearchQuery,
    currentUser: AuthenticatedUser
  ): Promise<ItemSearchResponse> {
    const { q, warehouseId, limit = 20, offset = 0 } = query;

    // Build base where clause for tenant scoping
    const whereClause: Prisma.ItemWhereInput = {
      tenantId: currentUser.tenantId,
      status: "Active", // Only show active items in search
      OR: [
        {
          name: {
            contains: q,
            mode: "insensitive",
          },
        },
        {
          sku: {
            contains: q,
            mode: "insensitive",
          },
        },
      ],
    };

    // Apply warehouse filtering if specified
    if (warehouseId) {
      // Validate user has access to the specified warehouse
      await this.validateWarehouseAccess(warehouseId, currentUser);

      // Filter items that have locations in the specified warehouse
      whereClause.itemLocations = {
        some: {
          pallet: {
            location: {
              warehouseId: warehouseId,
            },
          },
        },
      };
    } else if (currentUser.role !== Role.TENANT_ADMIN) {
      // For non-admin users, filter by their assigned warehouses
      const userWarehouseIds =
        currentUser.warehouseUsers?.map((wu) => wu.warehouseId) || [];

      if (userWarehouseIds.length === 0) {
        return {
          items: [],
          pagination: { total: 0, limit, offset, hasMore: false },
        };
      }

      whereClause.itemLocations = {
        some: {
          pallet: {
            location: {
              warehouseId: {
                in: userWarehouseIds,
              },
            },
          },
        },
      };
    }

    // Get total count for pagination
    const total = await this.prisma.item.count({ where: whereClause });

    // Fetch items with pagination
    const items = await this.prisma.item.findMany({
      where: whereClause,
      include: {
        itemLocations: {
          include: {
            pallet: {
              include: {
                location: true,
              },
            },
          },
          where: warehouseId
            ? {
                pallet: {
                  location: {
                    warehouseId: warehouseId,
                  },
                },
              }
            : currentUser.role !== Role.TENANT_ADMIN
            ? {
                pallet: {
                  location: {
                    warehouseId: {
                      in:
                        currentUser.warehouseUsers?.map(
                          (wu) => wu.warehouseId
                        ) || [],
                    },
                  },
                },
              }
            : undefined,
        },
      },
      orderBy: { name: "asc" },
      skip: offset,
      take: limit,
    });

    // Transform to search results with calculated quantities
    const searchResults: ItemSearchResult[] = items.map((item) => {
      const totalQuantity = item.itemLocations.reduce(
        (sum, location) => sum + location.quantity,
        0
      );

      const locationCount = new Set(
        item.itemLocations.map((location) => location.pallet.location?.id)
      ).size;

      return {
        id: item.id,
        sku: item.sku,
        name: item.name,
        description: item.description,
        unitOfMeasure: item.unitOfMeasure,
        status: item.status,
        totalQuantity,
        availableQuantity: totalQuantity, // For now, assume all quantity is available
        locationCount,
      };
    });

    return {
      items: searchResults,
      pagination: {
        total,
        limit,
        offset,
        hasMore: offset + limit < total,
      },
    };
  }

  /**
   * Get all pallet locations for a specific item within warehouse context
   */
  async getItemLocations(
    itemId: string,
    warehouseId: string,
    currentUser: AuthenticatedUser
  ): Promise<ItemLocationResponse> {
    // Validate user has access to the specified warehouse
    await this.validateWarehouseAccess(warehouseId, currentUser);

    // Verify item exists and user has access to it
    const item = await this.prisma.item.findFirst({
      where: {
        id: itemId,
        tenantId: currentUser.tenantId,
      },
    });

    if (!item) {
      throw new NotFoundException(
        `Item with ID "${itemId}" not found in your tenant.`
      );
    }

    // Get all item locations for this item in the specified warehouse
    const itemLocations = await this.prisma.itemLocation.findMany({
      where: {
        itemId: itemId,
        pallet: {
          location: {
            warehouseId: warehouseId,
          },
        },
      },
      include: {
        pallet: {
          include: {
            location: true,
          },
        },
      },
      orderBy: {
        lastUpdated: "desc",
      },
    });

    const totalQuantity = itemLocations.reduce(
      (sum, location) => sum + location.quantity,
      0
    );

    const locations: ItemLocationResult[] = itemLocations.map((location) => ({
      palletId: location.palletId,
      palletBarcode: location.pallet.barcode,
      quantity: location.quantity,
      location: {
        id: location.pallet.location!.id,
        name: location.pallet.location!.name,
        category: location.pallet.location!.category,
        type: location.pallet.location!.locationType,
      },
      lastUpdated: location.lastUpdated.toISOString(),
    }));

    return {
      itemId: item.id,
      itemName: item.name,
      totalQuantity,
      locations,
    };
  }

  /**
   * Validate that the user has access to the specified warehouse
   */
  private async validateWarehouseAccess(
    warehouseId: string,
    currentUser: AuthenticatedUser
  ): Promise<void> {
    // TENANT_ADMIN has access to all warehouses in their tenant
    if (currentUser.role === Role.TENANT_ADMIN) {
      const warehouse = await this.prisma.warehouse.findFirst({
        where: {
          id: warehouseId,
          tenantId: currentUser.tenantId,
        },
      });

      if (!warehouse) {
        throw new NotFoundException(
          `Warehouse with ID "${warehouseId}" not found in your tenant.`
        );
      }
      return;
    }

    // Other roles must be explicitly assigned to the warehouse
    const userWarehouseIds =
      currentUser.warehouseUsers?.map((wu) => wu.warehouseId) || [];

    if (!userWarehouseIds.includes(warehouseId)) {
      throw new ForbiddenException(
        `You do not have access to warehouse "${warehouseId}".`
      );
    }

    // Verify warehouse exists and belongs to user's tenant
    const warehouse = await this.prisma.warehouse.findFirst({
      where: {
        id: warehouseId,
        tenantId: currentUser.tenantId,
      },
    });

    if (!warehouse) {
      throw new NotFoundException(
        `Warehouse with ID "${warehouseId}" not found in your tenant.`
      );
    }
  }

  async create(
    createItemDto: CreateItemDto,
    currentUser: AuthenticatedUser
  ): Promise<Item> {
    // Check if SKU is provided and already exists (if applicable)
    // This check is global for SKU uniqueness, not tenant-specific.
    if (createItemDto.sku) {
      const existingSku = await this.prisma.item.findFirst({
        where: {
          sku: createItemDto.sku,
          // Assuming SKU uniqueness is global, not per tenant.
          // If SKU should be unique per tenant, add: tenantId: currentUser.tenantId
        },
      });
      if (existingSku) {
        throw new ForbiddenException(
          `Item with SKU ${createItemDto.sku} already exists.`
        );
      }
    }

    return this.prisma.$transaction(async (tx) => {
      // Master Item creation - Assuming Items are global, not tenant-specific in the Item table itself.
      // If Items were tenant-specific, we'd add tenantId: currentUser.tenantId here.
      const newItem = await tx.item.create({
        data: {
          name: createItemDto.name,
          sku: createItemDto.sku,
          description: createItemDto.description,
          unitOfMeasure: createItemDto.unitOfMeasure,
          defaultCost: createItemDto.defaultCost,
          lowStockThreshold: createItemDto.lowStockThreshold,
          status: createItemDto.status,
          tenantId: currentUser.tenantId, // Item is tenant-scoped
        },
      });

      if (createItemDto.warehouseIds && createItemDto.warehouseIds.length > 0) {
        // Validate that provided warehouseIds belong to the current user's tenant
        const validTenantWarehouses = await tx.warehouse.findMany({
          where: {
            id: { in: createItemDto.warehouseIds },
            tenantId: currentUser.tenantId,
          },
          select: { id: true },
        });

        const validTenantWarehouseIds = validTenantWarehouses.map(
          (wh) => wh.id
        );

        if (
          validTenantWarehouseIds.length !== createItemDto.warehouseIds.length
        ) {
          const invalidIds = createItemDto.warehouseIds.filter(
            (id) => !validTenantWarehouseIds.includes(id)
          );
          throw new ForbiddenException(
            `Warehouses with IDs [${invalidIds.join(
              ", "
            )}] do not exist or do not belong to your tenant.`
          );
        }

        const warehouseItemData = validTenantWarehouseIds.map(
          (warehouseId) => ({
            itemId: newItem.id,
            warehouseId: warehouseId,
            tenantId: currentUser.tenantId, // WarehouseItem is explicitly tenant-scoped
          })
        );
        await tx.warehouseItem.createMany({
          data: warehouseItemData,
          skipDuplicates: true,
        });
      }
      return newItem;
    });
  }

  async findAll(currentUser: AuthenticatedUser): Promise<Item[]> {
    const whereClause: Prisma.ItemWhereInput = {
      tenantId: currentUser.tenantId,
    };

    if (String(currentUser.role) !== Role.TENANT_ADMIN) {
      const userWarehouseIds =
        currentUser.warehouseUsers?.map((wu) => wu.warehouseId) || [];

      // If a non-TENANT_ADMIN has no warehouse assignments, they see no items.
      if (userWarehouseIds.length === 0) {
        return [];
      }

      whereClause.warehouseItems = {
        some: {
          warehouseId: {
            in: userWarehouseIds,
          },
          tenantId: currentUser.tenantId, // Ensures WarehouseItem is within tenant
        },
      };
    }

    const items = await this.prisma.item.findMany({
      where: whereClause,
      orderBy: { name: "asc" },
    });

    if (items.length === 0) {
      return [];
    }

    const itemIds = items.map((item) => item.id);

    // TODO: Fix aggregation query for warehouse-scoped access
    // This needs to be rewritten to work with the new schema structure
    // where PalletItem access is controlled through pallet->location->warehouse->tenant

    // For now, set all quantities to 0 to allow compilation
    const quantityMap = itemIds.reduce((map, itemId) => {
      map[itemId] = 0;
      return map;
    }, {} as Record<string, number>);

    // Add quantityOnHand to each item
    const itemsWithQuantity = items.map((item) => ({
      ...item,
      quantityOnHand: quantityMap[item.id] || 0, // Default to 0 if no pallet items
    }));

    return itemsWithQuantity;
  }

  async findOne(
    id: string,
    currentUser: AuthenticatedUser
  ): Promise<ItemDetailResponse> {
    const item = await this.prisma.item.findUnique({
      where: {
        id,
        tenantId: currentUser.tenantId, // Ensures item belongs to the user's tenant
      },
    });

    if (!item) {
      throw new NotFoundException(
        `Item with ID "${id}" not found in your tenant.`
      );
    }

    // TENANT_ADMIN can view any item in their tenant.
    // Other roles must have the item associated with one of their warehouses.
    if (String(currentUser.role) !== Role.TENANT_ADMIN) {
      const userWarehouseIds =
        currentUser.warehouseUsers?.map((wu) => wu.warehouseId) || [];

      if (userWarehouseIds.length === 0) {
        // Non-admin user with no warehouse assignments cannot access specific items.
        throw new NotFoundException(
          `Item with ID "${id}" not accessible. You are not assigned to any warehouses.`
        );
      }

      const warehouseItem = await this.prisma.warehouseItem.findFirst({
        where: {
          itemId: id,
          warehouseId: { in: userWarehouseIds },
          tenantId: currentUser.tenantId, // Ensures WarehouseItem record is also within the tenant
        },
      });

      if (!warehouseItem) {
        throw new NotFoundException(
          `Item with ID "${id}" is not available in any of your assigned warehouses.`
        );
      }
    }

    // Fetch related pallet items to calculate quantityOnHand and stock details
    const palletItems = await this.prisma.palletItem.findMany({
      where: {
        itemId: item.id,
        pallet: {
          location: {
            warehouse: {
              tenantId: currentUser.tenantId, // Security: ensure tenant access
            },
          },
        },
      },
      include: {
        pallet: {
          include: {
            location: {
              include: {
                warehouse: true,
              },
            },
          },
        },
      },
    });

    let quantityOnHand = 0;
    const stockByLocationAndPallet: PalletLocationStock[] = [];
    const distinctPalletIds = new Set<string>();

    for (const pi of palletItems) {
      quantityOnHand += pi.quantity;
      if (pi.pallet) {
        // Corrected: pi.Pallet -> pi.pallet
        distinctPalletIds.add(pi.pallet.id);
        stockByLocationAndPallet.push({
          palletId: pi.pallet.id,
          locationName: pi.pallet.location?.name || "N/A", // Corrected
          warehouseName: pi.pallet.location?.warehouse?.name || "N/A", // Corrected
          quantity: pi.quantity,
        });
      }
    }

    return {
      ...item,
      quantityOnHand,
      numberOfPallets: distinctPalletIds.size,
      stockByLocationAndPallet,
    };
  }

  async update(
    id: string,
    updateItemDto: UpdateItemDto,
    currentUser: AuthenticatedUser
  ): Promise<Item> {
    // Check if the item exists and belongs to a warehouse accessible by the user
    const existingItem = await this.findOne(id, currentUser);
    if (!existingItem) {
      // findOne already throws NotFoundException or handles scoping
      throw new NotFoundException(
        `Item with ID "${id}" not found or not accessible.`
      );
    }

    // Only TENANT_ADMIN can update any item. Other roles cannot update items directly (they manage pallet items).
    if (String(currentUser.role) !== Role.TENANT_ADMIN) {
      throw new ForbiddenException(
        "You do not have permission to update this item."
      );
    }

    // Prevent updating warehouseId directly through this general update method
    // Warehouse association should be managed via WarehouseItem records if needed.
    if (
      "warehouseId" in updateItemDto &&
      updateItemDto.warehouseId !== undefined
    ) {
      throw new ForbiddenException(
        "Cannot update warehouseId directly. Manage warehouse associations via WarehouseItem records."
      );
    }

    if (updateItemDto.sku) {
      const existingSkuItem = await this.prisma.item.findFirst({
        where: {
          sku: updateItemDto.sku,
          id: { not: id },
          tenantId: currentUser.tenantId, // Check for SKU uniqueness within the same tenant
        },
      });
      if (existingSkuItem) {
        throw new ForbiddenException(
          `Another item with SKU ${updateItemDto.sku} already exists in your tenant.`
        );
      }
    }

    return this.prisma.$transaction(async (tx) => {
      const updatedItem = await tx.item.update({
        where: {
          id,
          tenantId: currentUser.tenantId,
        },
        data: {
          name: updateItemDto.name,
          sku: updateItemDto.sku,
          description: updateItemDto.description,
          unitOfMeasure: updateItemDto.unitOfMeasure,
          defaultCost: updateItemDto.defaultCost,
          lowStockThreshold: updateItemDto.lowStockThreshold,
          status: updateItemDto.status,
        },
      });

      if (typeof updateItemDto.warehouseIds !== "undefined") {
        const validTenantWarehouses = await tx.warehouse.findMany({
          where: {
            id: { in: updateItemDto.warehouseIds },
            tenantId: currentUser.tenantId,
          },
          select: { id: true },
        });
        const validTenantWarehouseIds = validTenantWarehouses.map(
          (wh) => wh.id
        );

        if (
          updateItemDto.warehouseIds.length > 0 &&
          validTenantWarehouseIds.length !== updateItemDto.warehouseIds.length
        ) {
          const invalidIds = updateItemDto.warehouseIds.filter(
            (id) => !validTenantWarehouseIds.includes(id)
          );
          throw new ForbiddenException(
            `Warehouses with IDs [${invalidIds.join(
              ", "
            )}] do not exist or do not belong to your tenant for item association.`
          );
        }

        const currentWarehouseItems = await tx.warehouseItem.findMany({
          where: {
            itemId: id,
            tenantId: currentUser.tenantId, // Operate only on WarehouseItems of the ADMIN's tenant
          },
          select: { warehouseId: true },
        });
        const currentTenantWarehouseIds = currentWarehouseItems.map(
          (wi) => wi.warehouseId
        );

        const newTenantWarehouseIds = validTenantWarehouseIds;

        const idsToCreate = newTenantWarehouseIds.filter(
          (wid) => !currentTenantWarehouseIds.includes(wid)
        );
        const idsToDelete = currentTenantWarehouseIds.filter(
          (wid) => !newTenantWarehouseIds.includes(wid)
        );

        if (idsToCreate.length > 0) {
          await tx.warehouseItem.createMany({
            data: idsToCreate.map((warehouseId) => ({
              itemId: id,
              warehouseId: warehouseId,
              tenantId: currentUser.tenantId, // WarehouseItem is explicitly tenant-scoped
            })),
            skipDuplicates: true,
          });
        }

        if (idsToDelete.length > 0) {
          await tx.warehouseItem.deleteMany({
            where: {
              itemId: id,
              warehouseId: { in: idsToDelete },
              tenantId: currentUser.tenantId, // Operate only on WarehouseItems of the ADMIN's tenant
            },
          });
        }
      }
      return updatedItem;
    });
  }

  async remove(id: string, currentUser: AuthenticatedUser): Promise<Item> {
    // findOne will throw if item not found or not accessible by currentUser (respecting TENANT_ADMIN logic)
    const itemToDelete = await this.findOne(id, currentUser);

    // Only TENANT_ADMIN can delete master items.
    if (String(currentUser.role) !== Role.TENANT_ADMIN) {
      throw new ForbiddenException(
        "You do not have permission to delete this item."
      );
    }

    return this.prisma.$transaction(async (tx) => {
      // Check for related PalletItem records before deleting
      // This check needs to be inside the transaction to ensure consistency if many requests happen.
      const palletItems = await tx.palletItem.findMany({
        where: {
          itemId: id,
          // Assuming PalletItems are implicitly tenant-scoped via their item association.
          // If PalletItem has a tenantId, it should be added here:
          // tenantId: currentUser.tenantId,
        },
        select: { id: true }, // Only need to check for existence
      });

      if (palletItems.length > 0) {
        throw new ForbiddenException(
          "Cannot delete item with associated pallet items. Remove or update pallet items first."
        );
      }

      // Delete associated WarehouseItem records for this item within the tenant
      await tx.warehouseItem.deleteMany({
        where: {
          itemId: id,
          tenantId: currentUser.tenantId,
        },
      });

      // Delete the item itself, scoped by id and tenantId
      const deletedItem = await tx.item.delete({
        where: {
          id,
          tenantId: currentUser.tenantId,
        },
      });
      return deletedItem;
    });
  }
}
