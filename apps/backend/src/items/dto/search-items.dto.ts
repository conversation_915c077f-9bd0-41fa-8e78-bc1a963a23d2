import { IsString, IsOptional, IsInt, Min, Max, Length, Matches } from 'class-validator';
import { Transform } from 'class-transformer';

export class SearchItemsDto {
  @IsString()
  @Length(1, 100)
  @Matches(/^[a-zA-Z0-9\s\-_]+$/, {
    message: 'Search query can only contain letters, numbers, spaces, hyphens, and underscores'
  })
  q: string;

  @IsOptional()
  @IsString()
  warehouseId?: string;

  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number = 20;

  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsInt()
  @Min(0)
  offset?: number = 0;
}
