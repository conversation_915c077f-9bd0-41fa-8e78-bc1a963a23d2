import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UsePipes,
  ValidationPipe,
  NotFoundException,
  UseGuards,
  UseInterceptors,
  Req,
} from "@nestjs/common";
import { ItemsService } from "./items.service";
import { CreateItemDto } from "./dto/create-item.dto";
import { UpdateItemDto } from "./dto/update-item.dto";
import { SearchItemsDto } from "./dto/search-items.dto";
import { JwtAuthGuard } from "../auth/guards/jwt-auth.guard";
import { WarehousePermissionGuard } from "../auth/guards/warehouse-permission.guard";

import {
  RequireWarehouseAccess,
  RequestWithWarehouseContext,
} from "../auth/decorators/warehouse-permission.decorator";
import { LogAction } from "../audit-log/decorators/log-action.decorator";
import { AuditLogInterceptor } from "../audit-log/interceptors/audit-log.interceptor";
import { EnhancedUserPayload } from "../auth/types";

@Controller("items")
@UseGuards(JwtAuthGuard, WarehousePermissionGuard)
@UseInterceptors(AuditLogInterceptor)
export class ItemsController {
  constructor(private readonly itemsService: ItemsService) {}

  @Get("search")
  @RequireWarehouseAccess()
  @UsePipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
    })
  )
  @LogAction({
    action: "SEARCH_ITEMS",
    entity: "Item",
  })
  async searchItems(
    @Query() searchDto: SearchItemsDto,
    @Req() req: RequestWithWarehouseContext<{ user: EnhancedUserPayload }>
  ) {
    return this.itemsService.search(
      {
        q: searchDto.q,
        warehouseId: searchDto.warehouseId || req.warehouseContext?.warehouseId,
        limit: searchDto.limit,
        offset: searchDto.offset,
      },
      req.user
    );
  }

  @Get(":id/locations")
  @RequireWarehouseAccess()
  @LogAction({
    action: "VIEW_ITEM_LOCATIONS",
    entity: "Item",
    getEntityId: (context) => context.switchToHttp().getRequest().params.id,
  })
  async getItemLocations(
    @Param("id") itemId: string,
    @Query("warehouseId") warehouseId: string,
    @Req() req: RequestWithWarehouseContext<{ user: EnhancedUserPayload }>
  ) {
    const targetWarehouseId = warehouseId || req.warehouseContext?.warehouseId;

    if (!targetWarehouseId) {
      throw new NotFoundException("Warehouse ID is required");
    }

    return this.itemsService.getItemLocations(
      itemId,
      targetWarehouseId,
      req.user
    );
  }

  @Post()
  @UsePipes(new ValidationPipe({ whitelist: true, forbidNonWhitelisted: true }))
  create(
    @Body() createItemDto: CreateItemDto,
    @Req() req: RequestWithWarehouseContext<{ user: EnhancedUserPayload }>
  ) {
    return this.itemsService.create(createItemDto, req.user);
  }

  @Get()
  findAll(
    @Req() req: RequestWithWarehouseContext<{ user: EnhancedUserPayload }>
  ) {
    return this.itemsService.findAll(req.user);
  }

  @Get(":id")
  async findOne(
    @Param("id") id: string,
    @Req() req: RequestWithWarehouseContext<{ user: EnhancedUserPayload }>
  ) {
    const item = await this.itemsService.findOne(id, req.user);
    return item;
  }

  @Patch(":id")
  @UsePipes(new ValidationPipe({ whitelist: true, forbidNonWhitelisted: true }))
  update(
    @Param("id") id: string,
    @Body() updateItemDto: UpdateItemDto,
    @Req() req: RequestWithWarehouseContext<{ user: EnhancedUserPayload }>
  ) {
    return this.itemsService.update(id, updateItemDto, req.user);
  }

  @Delete(":id")
  remove(
    @Param("id") id: string,
    @Req() req: RequestWithWarehouseContext<{ user: EnhancedUserPayload }>
  ) {
    return this.itemsService.remove(id, req.user);
  }
}
