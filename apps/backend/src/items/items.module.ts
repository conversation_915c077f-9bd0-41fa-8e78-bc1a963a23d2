import { Module } from "@nestjs/common";
import { ItemsService } from "./items.service";
import { ItemsController } from "./items.controller";
import { WarehousesModule } from "../warehouses/warehouses.module";
import { AuditLogModule } from "../audit-log/audit-log.module";

@Module({
  imports: [WarehousesModule, AuditLogModule],
  providers: [ItemsService],
  controllers: [ItemsController],
})
export class ItemsModule {}
