{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "test": "vitest"}, "dependencies": {"@clerk/nextjs": "^6.23.0", "@hookform/resolvers": "^5.0.1", "@quildora/types": "workspace:*", "@radix-ui/react-alert-dialog": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-dialog": "^1.1.10", "@radix-ui/react-dropdown-menu": "^2.1.12", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-popover": "^1.1.10", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.8", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@supabase/auth-ui-react": "^0.4.7", "@supabase/auth-ui-shared": "^0.1.8", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.4", "@tanstack/react-query": "^5.74.4", "axios": "^1.8.4", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.23.6", "lucide-react": "^0.501.0", "next": "15.3.5", "next-themes": "^0.4.6", "react": "^19.0.0", "react-barcode": "^1.6.1", "react-confetti": "^6.4.0", "react-day-picker": "8.10.1", "react-dom": "^19.0.0", "react-hook-form": "^7.56.0", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "zod": "^3.24.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@radix-ui/react-toast": "^1.2.14", "@svgr/webpack": "^8.1.0", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/postcss": "^4.1.4", "@tailwindcss/typography": "^0.5.16", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/canvas-confetti": "^1.9.0", "@types/node": "^20.17.30", "@types/react": "^19", "@types/react-dom": "^19", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "cssnano": "^7.0.7", "eslint": "^9", "eslint-config-next": "15.3.1", "jsdom": "^26.1.0", "msw": "^2.0.0", "postcss": "^8.5.3", "postcss-import": "^16.1.0", "postcss-nesting": "^13.0.1", "postcss-preset-env": "^10.1.6", "tailwindcss": "^4.1.7", "tailwindcss-animate": "^1.0.7", "vite": "^6.3.5", "vite-tsconfig-paths": "^4.0.0", "vitest": "^3.1.2"}}