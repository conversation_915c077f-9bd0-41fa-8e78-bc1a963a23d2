import { useState, useCallback } from "react";
import {
  useSuspenseQuery,
  useMutation,
  useQueryClient,
} from "@tanstack/react-query";
import { toast } from "sonner";
import { fetchWithAuth } from "@/lib/api";
import { useAuth } from "@/components/providers/auth-provider";
import { useWarehouse } from "@/components/providers/warehouse-provider";
import type { PackingListResponse } from "@quildora/types";

// API functions for packing list operations
const fetchPackingList = async (
  shipmentId: string,
  token: string | null
): Promise<PackingListResponse> => {
  return fetchWithAuth(`/api/shipments/${shipmentId}/packing-list`, {
    token,
  });
};

const generatePackingListPdf = async (
  shipmentId: string,
  token: string | null
): Promise<Blob> => {
  const response = await fetch(
    `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/shipments/${shipmentId}/packing-list/pdf`,
    {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    }
  );

  if (!response.ok) {
    const errorData = await response
      .json()
      .catch(() => ({ message: "Failed to generate packing list PDF." }));
    throw new Error(errorData.message);
  }

  const blob = await response.blob();

  if (blob.size === 0) {
    throw new Error("Received empty PDF file");
  }

  return blob;
};

const updatePackingListNotes = async (
  shipmentId: string,
  notes: string,
  token: string | null
): Promise<PackingListResponse> => {
  return fetchWithAuth(`/api/shipments/${shipmentId}/packing-list`, {
    method: "PATCH",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({ notes }),
    token,
  });
};

interface UsePackingListOperationsOptions {
  shipmentId: string;
  enableAutoRefresh?: boolean;
  refreshInterval?: number;
}

export function usePackingListOperations({
  shipmentId,
  enableAutoRefresh = false,
  refreshInterval = 60000, // 1 minute
}: UsePackingListOperationsOptions) {
  const { appToken } = useAuth();
  const { currentWarehouse } = useWarehouse();
  const queryClient = useQueryClient();

  const [printStatus, setPrintStatus] = useState<
    "idle" | "preparing" | "printing" | "success" | "error"
  >("idle");

  // Fetch packing list data
  const { data: packingList, error } = useSuspenseQuery<
    PackingListResponse,
    Error
  >({
    queryKey: ["packing-list", shipmentId],
    queryFn: () => fetchPackingList(shipmentId, appToken),
    staleTime: enableAutoRefresh ? refreshInterval : 300000, // 5 minutes default
    refetchInterval: enableAutoRefresh ? refreshInterval : false,
    retry: (failureCount, error) => {
      // Don't retry on not found or permission errors
      if (
        error.message.includes("not found") ||
        error.message.includes("forbidden")
      ) {
        return false;
      }
      return failureCount < 2;
    },
  });

  // PDF generation mutation
  const pdfMutation = useMutation({
    mutationFn: () => generatePackingListPdf(shipmentId, appToken),
    onSuccess: (blob) => {
      // Create download link
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `packing-list-${
        packingList?.shipmentNumber || shipmentId
      }.pdf`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      toast.success("PDF downloaded successfully", {
        description: `Packing list for ${packingList?.shipmentNumber}`,
      });
    },
    onError: (error) => {
      toast.error("Failed to generate PDF", {
        description:
          error instanceof Error ? error.message : "Please try again",
      });
    },
  });

  // Notes update mutation
  const notesMutation = useMutation({
    mutationFn: (notes: string) =>
      updatePackingListNotes(shipmentId, notes, appToken),
    onSuccess: (updatedPackingList) => {
      // Update cache
      queryClient.setQueryData(
        ["packing-list", shipmentId],
        updatedPackingList
      );

      toast.success("Notes updated", {
        description: "Packing list notes have been saved",
      });
    },
    onError: (error) => {
      toast.error("Failed to update notes", {
        description:
          error instanceof Error ? error.message : "Please try again",
      });
    },
  });

  // Print functionality with popup blocker workaround
  const handlePrint = useCallback(async () => {
    setPrintStatus("preparing");

    try {
      // Open print window immediately to avoid popup blockers
      const printWindow = window.open("", "_blank");

      if (!printWindow) {
        throw new Error("Popup blocked. Please allow popups for this site.");
      }

      setPrintStatus("printing");

      // Generate print-friendly HTML content
      const printContent = generatePrintableHTML(packingList);

      printWindow.document.write(printContent);
      printWindow.document.close();

      // Wait for content to load, then print
      printWindow.onload = () => {
        printWindow.print();

        // Close window after printing (with delay for print dialog)
        setTimeout(() => {
          printWindow.close();
          setPrintStatus("success");

          toast.success("Print dialog opened", {
            description: "Packing list ready for printing",
          });

          // Reset status after success message
          setTimeout(() => setPrintStatus("idle"), 2000);
        }, 1000);
      };
    } catch (error) {
      setPrintStatus("error");
      toast.error("Print failed", {
        description:
          error instanceof Error ? error.message : "Please try again",
      });

      // Reset status after error
      setTimeout(() => setPrintStatus("idle"), 3000);
    }
  }, [packingList]);

  // Download PDF
  const downloadPdf = useCallback(() => {
    pdfMutation.mutate();
  }, [pdfMutation]);

  // Update notes
  const updateNotes = useCallback(
    (notes: string) => {
      notesMutation.mutate(notes);
    },
    [notesMutation]
  );

  // Generate text version for download
  const downloadText = useCallback(() => {
    if (!packingList) {
      toast.error("No packing list data available");
      return;
    }

    const textContent = generateTextPackingList(packingList);
    const blob = new Blob([textContent], { type: "text/plain" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `packing-list-${packingList.shipmentNumber}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    toast.success("Text file downloaded", {
      description: `Packing list for ${packingList.shipmentNumber}`,
    });
  }, [packingList]);

  // Refresh packing list data
  const refresh = useCallback(() => {
    queryClient.invalidateQueries({ queryKey: ["packing-list", shipmentId] });
    toast.success("Packing list refreshed");
  }, [queryClient, shipmentId]);

  return {
    // Data
    packingList,
    error,

    // Operations
    handlePrint,
    downloadPdf,
    downloadText,
    updateNotes,
    refresh,

    // Loading states
    isPrintingPdf: pdfMutation.isPending,
    isUpdatingNotes: notesMutation.isPending,
    printStatus,

    // Status checks
    canPrint: !!packingList && printStatus === "idle",
    canDownload: !!packingList,
    hasItems: (packingList?.items?.length || 0) > 0,
    hasPallets: (packingList?.pallets?.length || 0) > 0,
  };
}

// Helper function to generate printable HTML
function generatePrintableHTML(
  packingList: PackingListResponse | undefined
): string {
  if (!packingList) {
    return "<html><body><h1>No packing list data available</h1></body></html>";
  }

  return `
    <!DOCTYPE html>
    <html>
    <head>
      <title>Packing List - ${packingList.shipmentNumber}</title>
      <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { text-align: center; border-bottom: 2px solid #000; padding-bottom: 10px; margin-bottom: 20px; }
        .section { margin-bottom: 20px; }
        .section h3 { border-bottom: 1px solid #ccc; padding-bottom: 5px; }
        table { width: 100%; border-collapse: collapse; margin-top: 10px; }
        th, td { border: 1px solid #000; padding: 8px; text-align: left; }
        th { background-color: #f0f0f0; }
        .summary { display: flex; justify-content: space-between; }
        .summary div { flex: 1; }
        @media print {
          body { margin: 0; }
          .no-print { display: none; }
        }
      </style>
    </head>
    <body>
      <div class="header">
        <h1>PACKING LIST</h1>
        <p><strong>Shipment Number:</strong> ${packingList.shipmentNumber}</p>
        <p><strong>Generated:</strong> ${new Date(
          packingList.generatedAt
        ).toLocaleString()}</p>
      </div>

      <div class="section">
        <h3>Destination</h3>
        <p><strong>${packingList.destination}</strong></p>
        ${
          packingList.destinationCode
            ? `<p>Code: ${packingList.destinationCode}</p>`
            : ""
        }
      </div>

      <div class="section">
        <h3>Summary</h3>
        <div class="summary">
          <div>Total Items: <strong>${
            packingList.summary.totalItems
          }</strong></div>
          <div>Total Quantity: <strong>${
            packingList.summary.totalQuantity
          }</strong></div>
          <div>Total Pallets: <strong>${
            packingList.summary.totalPallets
          }</strong></div>
        </div>
      </div>

      ${
        packingList.items.length > 0
          ? `
        <div class="section">
          <h3>Items (${packingList.items.length})</h3>
          <table>
            <thead>
              <tr>
                <th>SKU</th>
                <th>Item Name</th>
                <th>Quantity</th>
                <th>Unit</th>
                <th>Source Pallet</th>
              </tr>
            </thead>
            <tbody>
              ${packingList.items
                .map(
                  (item) => `
                <tr>
                  <td>${item.sku || "-"}</td>
                  <td>${item.name}</td>
                  <td style="text-align: center;">${item.quantity}</td>
                  <td>${item.unitOfMeasure}</td>
                  <td>${item.sourcePallet || "-"}</td>
                </tr>
              `
                )
                .join("")}
            </tbody>
          </table>
        </div>
      `
          : ""
      }

      ${
        packingList.pallets.length > 0
          ? `
        <div class="section">
          <h3>Pallets (${packingList.pallets.length})</h3>
          ${packingList.pallets
            .map(
              (pallet, index) => `
            <div style="margin-bottom: 10px; padding: 10px; border: 1px solid #ccc;">
              <strong>${pallet.barcode || `Pallet ${index + 1}`}</strong>
              ${pallet.description ? `<br><em>${pallet.description}</em>` : ""}
              <br>Items: ${pallet.itemCount} | Total Quantity: ${
                pallet.totalQuantity
              }
            </div>
          `
            )
            .join("")}
        </div>
      `
          : ""
      }
    </body>
    </html>
  `;
}

// Helper function to generate text version
function generateTextPackingList(packingList: PackingListResponse): string {
  const lines = [
    "PACKING LIST",
    "=".repeat(50),
    "",
    `Shipment Number: ${packingList.shipmentNumber}`,
    `Generated: ${new Date(packingList.generatedAt).toLocaleString()}`,
    "",
    "DESTINATION:",
    packingList.destination,
    packingList.destinationCode ? `Code: ${packingList.destinationCode}` : "",
    "",
    "SUMMARY:",
    `Total Items: ${packingList.summary.totalItems}`,
    `Total Quantity: ${packingList.summary.totalQuantity}`,
    `Total Pallets: ${packingList.summary.totalPallets}`,
    "",
  ];

  if (packingList.items.length > 0) {
    lines.push("ITEMS:");
    lines.push("-".repeat(50));
    packingList.items.forEach((item, index) => {
      lines.push(
        `${index + 1}. ${item.name} (${item.sku || "No SKU"})`,
        `   Quantity: ${item.quantity} ${item.unitOfMeasure}`,
        `   Source: ${item.sourcePallet || "Not specified"}`,
        ""
      );
    });
  }

  if (packingList.pallets.length > 0) {
    lines.push("PALLETS:");
    lines.push("-".repeat(50));
    packingList.pallets.forEach((pallet, index) => {
      lines.push(
        `${index + 1}. ${pallet.barcode || `Pallet ${index + 1}`}`,
        pallet.description ? `   Description: ${pallet.description}` : "",
        `   Items: ${pallet.itemCount} | Total Quantity: ${pallet.totalQuantity}`,
        ""
      );
    });
  }

  return lines.filter((line) => line !== null).join("\n");
}
