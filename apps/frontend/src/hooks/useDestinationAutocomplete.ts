import { useState, useCallback, useMemo } from "react";
import type { DestinationResponse } from "@quildora/types";

export function useDestinationAutocomplete(destinations: DestinationResponse[]) {
  const [query, setQuery] = useState("");
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);

  // Filter destinations based on query
  const suggestions = useMemo(() => {
    if (!query.trim() || query.length < 2) {
      return [];
    }

    const searchTerm = query.toLowerCase();
    return destinations.filter(dest => 
      dest.name.toLowerCase().includes(searchTerm) ||
      (dest.code && dest.code.toLowerCase().includes(searchTerm))
    ).slice(0, 10); // Limit to 10 suggestions
  }, [destinations, query]);

  const handleInputChange = useCallback((
    value: string,
    onSelect?: (suggestion: DestinationResponse) => void
  ) => {
    setQuery(value);
    setShowSuggestions(value.length >= 2);
    setSelectedIndex(-1);

    // Auto-select if exact match found
    if (onSelect && value.length >= 2) {
      const exactMatch = destinations.find(dest => 
        dest.name.toLowerCase() === value.toLowerCase() ||
        (dest.code && dest.code.toLowerCase() === value.toLowerCase())
      );
      
      if (exactMatch) {
        onSelect(exactMatch);
      }
    }
  }, [destinations]);

  const handleKeyDown = useCallback((
    e: React.KeyboardEvent,
    onSelect?: (suggestion: DestinationResponse) => void
  ) => {
    if (!showSuggestions || suggestions.length === 0) return;

    switch (e.key) {
      case "ArrowDown":
        e.preventDefault();
        setSelectedIndex(prev => 
          prev < suggestions.length - 1 ? prev + 1 : 0
        );
        break;
      case "ArrowUp":
        e.preventDefault();
        setSelectedIndex(prev => 
          prev > 0 ? prev - 1 : suggestions.length - 1
        );
        break;
      case "Enter":
        e.preventDefault();
        if (selectedIndex >= 0 && selectedIndex < suggestions.length && onSelect) {
          onSelect(suggestions[selectedIndex]);
          setShowSuggestions(false);
          setSelectedIndex(-1);
        }
        break;
      case "Escape":
        setShowSuggestions(false);
        setSelectedIndex(-1);
        break;
    }
  }, [showSuggestions, suggestions, selectedIndex]);

  const handleSuggestionSelect = useCallback((suggestion: DestinationResponse) => {
    setQuery(suggestion.name);
    setShowSuggestions(false);
    setSelectedIndex(-1);
  }, []);

  const clearSuggestions = useCallback(() => {
    setQuery("");
    setShowSuggestions(false);
    setSelectedIndex(-1);
  }, []);

  return {
    query,
    suggestions,
    showSuggestions,
    selectedIndex,
    handleInputChange,
    handleKeyDown,
    handleSuggestionSelect,
    clearSuggestions,
  };
}
