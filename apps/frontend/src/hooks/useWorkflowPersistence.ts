"use client";

import { useState, useEffect, useCallback } from "react";
import { useWarehouse } from "@/components/providers/warehouse-provider";

export interface WorkflowPreferences {
  pickingMode: "pallet" | "item" | "picklist";
  lastUsedDestination?: string;
  defaultItemFilters?: {
    includeOutOfStock: boolean;
    sortBy: "name" | "sku" | "lastUpdated";
    sortOrder: "asc" | "desc";
  };
  cartSettings?: {
    autoSave: boolean;
    showQuantityWarnings: boolean;
    defaultDestination?: string;
  };
  uiPreferences?: {
    compactView: boolean;
    showAdvancedFilters: boolean;
    preferredPageSize: number;
  };
}

const DEFAULT_PREFERENCES: WorkflowPreferences = {
  pickingMode: "pallet",
  defaultItemFilters: {
    includeOutOfStock: false,
    sortBy: "name",
    sortOrder: "asc",
  },
  cartSettings: {
    autoSave: true,
    showQuantityWarnings: true,
  },
  uiPreferences: {
    compactView: false,
    showAdvancedFilters: false,
    preferredPageSize: 25,
  },
};

/**
 * Hook for managing workflow state persistence across sessions
 * Provides warehouse-scoped preferences and automatic synchronization
 */
export function useWorkflowPersistence() {
  const { currentWarehouse } = useWarehouse();
  const [preferences, setPreferences] =
    useState<WorkflowPreferences>(DEFAULT_PREFERENCES);
  const [isLoading, setIsLoading] = useState(true);

  // Generate storage key for current warehouse
  const getStorageKey = useCallback(
    (key: string) => {
      return currentWarehouse?.id
        ? `workflow-${key}-${currentWarehouse.id}`
        : null;
    },
    [currentWarehouse?.id]
  );

  // Load preferences from localStorage
  const loadPreferences = useCallback(() => {
    if (!currentWarehouse?.id) {
      setPreferences(DEFAULT_PREFERENCES);
      setIsLoading(false);
      return;
    }

    try {
      const storageKey = getStorageKey("preferences");
      if (!storageKey) return;

      const saved = localStorage.getItem(storageKey);
      if (saved) {
        const parsed = JSON.parse(saved);
        // Merge with defaults to handle new preference keys
        setPreferences({ ...DEFAULT_PREFERENCES, ...parsed });
      } else {
        setPreferences(DEFAULT_PREFERENCES);
      }
    } catch (error) {
      console.warn("Failed to load workflow preferences:", error);
      setPreferences(DEFAULT_PREFERENCES);
    } finally {
      setIsLoading(false);
    }
  }, [currentWarehouse?.id, getStorageKey]);

  // Save preferences to localStorage
  const savePreferences = useCallback(
    (newPreferences: WorkflowPreferences) => {
      if (!currentWarehouse?.id) return;

      try {
        const storageKey = getStorageKey("preferences");
        if (!storageKey) return;

        localStorage.setItem(storageKey, JSON.stringify(newPreferences));
        setPreferences(newPreferences);
      } catch (error) {
        console.warn("Failed to save workflow preferences:", error);
      }
    },
    [currentWarehouse?.id, getStorageKey]
  );

  // Update specific preference sections
  const updatePickingMode = useCallback(
    (mode: WorkflowPreferences["pickingMode"]) => {
      const updated = { ...preferences, pickingMode: mode };
      savePreferences(updated);
    },
    [preferences, savePreferences]
  );

  const updateItemFilters = useCallback(
    (filters: Partial<WorkflowPreferences["defaultItemFilters"]>) => {
      const updated: WorkflowPreferences = {
        ...preferences,
        defaultItemFilters: {
          ...preferences.defaultItemFilters,
          ...filters,
        } as Required<WorkflowPreferences["defaultItemFilters"]>,
      };
      savePreferences(updated);
    },
    [preferences, savePreferences]
  );

  const updateCartSettings = useCallback(
    (settings: Partial<WorkflowPreferences["cartSettings"]>) => {
      const updated: WorkflowPreferences = {
        ...preferences,
        cartSettings: {
          ...preferences.cartSettings,
          ...settings,
        } as Required<WorkflowPreferences["cartSettings"]>,
      };
      savePreferences(updated);
    },
    [preferences, savePreferences]
  );

  const updateUIPreferences = useCallback(
    (uiPrefs: Partial<WorkflowPreferences["uiPreferences"]>) => {
      const updated: WorkflowPreferences = {
        ...preferences,
        uiPreferences: {
          ...preferences.uiPreferences,
          ...uiPrefs,
        } as Required<WorkflowPreferences["uiPreferences"]>,
      };
      savePreferences(updated);
    },
    [preferences, savePreferences]
  );

  const updateLastUsedDestination = useCallback(
    (destination: string) => {
      const updated = { ...preferences, lastUsedDestination: destination };
      savePreferences(updated);
    },
    [preferences, savePreferences]
  );

  // Reset preferences to defaults
  const resetPreferences = useCallback(() => {
    savePreferences(DEFAULT_PREFERENCES);
  }, [savePreferences]);

  // Clear all workflow data for current warehouse
  const clearWorkflowData = useCallback(() => {
    if (!currentWarehouse?.id) return;

    try {
      const keys = [
        getStorageKey("preferences"),
        getStorageKey("cart"),
        getStorageKey("filters"),
      ].filter(Boolean);

      keys.forEach((key) => {
        if (key) localStorage.removeItem(key);
      });

      setPreferences(DEFAULT_PREFERENCES);
    } catch (error) {
      console.warn("Failed to clear workflow data:", error);
    }
  }, [currentWarehouse?.id, getStorageKey]);

  // Load preferences when warehouse changes
  useEffect(() => {
    loadPreferences();
  }, [loadPreferences]);

  // Auto-save preferences when they change (debounced)
  useEffect(() => {
    if (isLoading) return;

    const timeoutId = setTimeout(() => {
      if (currentWarehouse?.id) {
        const storageKey = getStorageKey("preferences");
        if (storageKey) {
          try {
            localStorage.setItem(storageKey, JSON.stringify(preferences));
          } catch (error) {
            console.warn("Failed to auto-save preferences:", error);
          }
        }
      }
    }, 1000); // 1 second debounce

    return () => clearTimeout(timeoutId);
  }, [preferences, currentWarehouse?.id, getStorageKey, isLoading]);

  return {
    // State
    preferences,
    isLoading,

    // Actions
    updatePickingMode,
    updateItemFilters,
    updateCartSettings,
    updateUIPreferences,
    updateLastUsedDestination,
    resetPreferences,
    clearWorkflowData,

    // Utilities
    getStorageKey,
  };
}

/**
 * Utility function to migrate old localStorage keys to new format
 * Call this during app initialization to preserve user preferences
 */
export function migrateWorkflowPreferences(warehouseId: string) {
  try {
    // Migrate old picking mode key
    const oldPickingMode = localStorage.getItem(`picking-mode-${warehouseId}`);
    if (oldPickingMode) {
      const newKey = `workflow-preferences-${warehouseId}`;
      const existing = localStorage.getItem(newKey);

      if (!existing) {
        const preferences = {
          ...DEFAULT_PREFERENCES,
          pickingMode: oldPickingMode,
        };
        localStorage.setItem(newKey, JSON.stringify(preferences));
      }

      // Remove old key
      localStorage.removeItem(`picking-mode-${warehouseId}`);
    }
  } catch (error) {
    console.warn("Failed to migrate workflow preferences:", error);
  }
}
