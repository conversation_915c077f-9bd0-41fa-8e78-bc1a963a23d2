import { useEffect, useCallback } from "react";
import { useQueryClient } from "@tanstack/react-query";
import { useAuth } from "@/components/providers/auth-provider";
import { useWarehouse } from "@/components/providers/warehouse-provider";
import { fetchWithAuth } from "@/lib/api";
import type {
  ItemSearchResponse,
  ItemLocationResponse,
  ShipmentSearchResponse,
  Location,
} from "@quildora/types";

// API functions for prefetching
const prefetchItemSearchAPI = async (
  query: string,
  warehouseId: string,
  token: string | null
): Promise<ItemSearchResponse> => {
  const searchParams = new URLSearchParams({
    q: query,
    warehouseId,
    limit: "20",
  });

  return fetchWithAuth(`/api/items/search?${searchParams.toString()}`, {
    token,
  });
};

const prefetchItemLocations = async (
  itemId: string,
  warehouseId: string,
  token: string | null
): Promise<ItemLocationResponse> => {
  const searchParams = new URLSearchParams({ warehouseId });

  return fetchWithAuth(
    `/api/items/${itemId}/locations?${searchParams.toString()}`,
    {
      token,
    }
  );
};

const prefetchShipments = async (
  warehouseId: string,
  token: string | null
): Promise<ShipmentSearchResponse> => {
  const searchParams = new URLSearchParams({
    warehouseId,
    limit: "20",
    sortBy: "createdAt",
    sortOrder: "desc",
  });

  return fetchWithAuth(`/api/shipments/search?${searchParams.toString()}`, {
    token,
  });
};

const prefetchLocations = async (
  warehouseId: string,
  token: string | null
): Promise<Location[]> => {
  const searchParams = new URLSearchParams({ warehouseId });

  return fetchWithAuth(`/api/locations?${searchParams.toString()}`, {
    token,
  });
};

interface UsePrefetchingStrategyOptions {
  enableItemSearch?: boolean;
  enableShipmentData?: boolean;
  enableLocationData?: boolean;
  enableWarehouseSwitch?: boolean;
  prefetchDelay?: number;
}

export function usePrefetchingStrategy(
  options: UsePrefetchingStrategyOptions = {}
) {
  const {
    enableItemSearch = true,
    enableShipmentData = true,
    enableLocationData = true,
    enableWarehouseSwitch = true,
    prefetchDelay = 100, // Small delay to avoid blocking initial render
  } = options;

  const { appToken } = useAuth();
  const { currentWarehouse, accessibleWarehouses } = useWarehouse();
  const queryClient = useQueryClient();

  // Prefetch core warehouse data when warehouse changes
  const prefetchWarehouseData = useCallback(
    async (warehouseId: string) => {
      if (!appToken || !warehouseId) return;

      try {
        // Prefetch locations (always needed for pallet operations)
        if (enableLocationData) {
          await queryClient.prefetchQuery({
            queryKey: ["locations", warehouseId],
            queryFn: () => prefetchLocations(warehouseId, appToken),
            staleTime: 300000, // 5 minutes
          });
        }

        // Prefetch recent shipments (for shipment management screens)
        if (enableShipmentData) {
          await queryClient.prefetchQuery({
            queryKey: [
              "shipments-search",
              warehouseId,
              {
                limit: 20,
                sortBy: "createdAt",
                sortOrder: "desc",
              },
            ],
            queryFn: () => prefetchShipments(warehouseId, appToken),
            staleTime: 60000, // 1 minute for dynamic data
          });
        }

        // Prefetch common item searches (empty query for recent items)
        if (enableItemSearch) {
          await queryClient.prefetchQuery({
            queryKey: ["items-search", warehouseId, ""],
            queryFn: () => prefetchItemSearchAPI("", warehouseId, appToken),
            staleTime: 180000, // 3 minutes
          });
        }
      } catch (error) {
        // Silently fail prefetching - don't block user interactions
        console.warn("Prefetching failed:", error);
      }
    },
    [
      appToken,
      queryClient,
      enableLocationData,
      enableShipmentData,
      enableItemSearch,
    ]
  );

  // Prefetch data for other warehouses (for quick switching)
  const prefetchOtherWarehouses = useCallback(async () => {
    if (!enableWarehouseSwitch || !appToken || !accessibleWarehouses) return;

    const otherWarehouses = accessibleWarehouses.filter(
      (warehouse) => warehouse.id !== currentWarehouse?.id
    );

    // Prefetch data for up to 3 other warehouses to avoid excessive requests
    const warehousesToPrefetch = otherWarehouses.slice(0, 3);

    for (const warehouse of warehousesToPrefetch) {
      try {
        // Only prefetch locations for other warehouses (most commonly needed)
        await queryClient.prefetchQuery({
          queryKey: ["locations", warehouse.id],
          queryFn: () => prefetchLocations(warehouse.id, appToken),
          staleTime: 600000, // 10 minutes for other warehouses
        });
      } catch (error) {
        // Continue with other warehouses if one fails
        console.warn(
          `Prefetching failed for warehouse ${warehouse.id}:`,
          error
        );
      }
    }
  }, [
    enableWarehouseSwitch,
    appToken,
    accessibleWarehouses,
    currentWarehouse?.id,
    queryClient,
  ]);

  // Prefetch item locations when user hovers over items
  const prefetchItemLocationOnHover = useCallback(
    (itemId: string) => {
      if (!appToken || !currentWarehouse?.id) return;

      queryClient.prefetchQuery({
        queryKey: ["item-locations", itemId, currentWarehouse.id],
        queryFn: () =>
          prefetchItemLocations(itemId, currentWarehouse.id, appToken),
        staleTime: 300000, // 5 minutes
      });
    },
    [appToken, currentWarehouse?.id, queryClient]
  );

  // Prefetch item search results for common queries
  const prefetchItemSearch = useCallback(
    (query: string) => {
      if (!appToken || !currentWarehouse?.id || query.length < 2) return;

      queryClient.prefetchQuery({
        queryKey: ["items-search", currentWarehouse.id, query],
        queryFn: () =>
          prefetchItemSearchAPI(query, currentWarehouse.id, appToken),
        staleTime: 180000, // 3 minutes
      });
    },
    [appToken, currentWarehouse?.id, queryClient]
  );

  // Prefetch shipment details when user hovers over shipment cards
  const prefetchShipmentDetails = useCallback(
    (shipmentId: string) => {
      if (!appToken) return;

      // Prefetch packing list
      queryClient.prefetchQuery({
        queryKey: ["packing-list", shipmentId],
        queryFn: () =>
          fetchWithAuth(`/api/shipments/${shipmentId}/packing-list`, {
            token: appToken,
          }),
        staleTime: 300000, // 5 minutes
      });
    },
    [appToken, queryClient]
  );

  // Main prefetching effect when warehouse changes
  useEffect(() => {
    if (!currentWarehouse?.id) return;

    const timeoutId = setTimeout(() => {
      prefetchWarehouseData(currentWarehouse.id);
    }, prefetchDelay);

    return () => clearTimeout(timeoutId);
  }, [currentWarehouse?.id, prefetchWarehouseData, prefetchDelay]);

  // Prefetch other warehouses after initial load
  useEffect(() => {
    if (!currentWarehouse?.id) return;

    const timeoutId = setTimeout(() => {
      prefetchOtherWarehouses();
    }, prefetchDelay + 1000); // Delay more to not interfere with main prefetching

    return () => clearTimeout(timeoutId);
  }, [currentWarehouse?.id, prefetchOtherWarehouses, prefetchDelay]);

  // Invalidate cache when switching warehouses to ensure fresh data
  const invalidateWarehouseCache = useCallback(
    (warehouseId: string) => {
      // Invalidate dynamic data that should be fresh
      queryClient.invalidateQueries({
        queryKey: ["shipments-search", warehouseId],
      });
      queryClient.invalidateQueries({
        queryKey: ["items-search", warehouseId],
      });
    },
    [queryClient]
  );

  // Prefetch route-specific data
  const prefetchRouteData = useCallback(
    (route: "picking" | "shipments" | "pallets" | "items") => {
      if (!appToken || !currentWarehouse?.id) return;

      const warehouseId = currentWarehouse.id;

      switch (route) {
        case "picking":
          // Prefetch common picking data
          prefetchItemSearch("");
          break;

        case "shipments":
          // Prefetch shipment data with different filters
          queryClient.prefetchQuery({
            queryKey: [
              "shipments-search",
              warehouseId,
              { status: "PREPARING" },
            ],
            queryFn: () => prefetchShipments(warehouseId, appToken),
            staleTime: 60000,
          });
          break;

        case "pallets":
          // Prefetch locations for pallet operations
          if (!queryClient.getQueryData(["locations", warehouseId])) {
            queryClient.prefetchQuery({
              queryKey: ["locations", warehouseId],
              queryFn: () => prefetchLocations(warehouseId, appToken),
              staleTime: 300000,
            });
          }
          break;

        case "items":
          // Prefetch item search data
          prefetchItemSearch("");
          break;
      }
    },
    [appToken, currentWarehouse?.id, queryClient, prefetchItemSearch]
  );

  return {
    // Prefetching functions for components to use
    prefetchItemLocationOnHover,
    prefetchItemSearch,
    prefetchShipmentDetails,
    prefetchRouteData,
    invalidateWarehouseCache,

    // Status
    isReady: !!currentWarehouse?.id && !!appToken,
    currentWarehouseId: currentWarehouse?.id,
  };
}

// Hook for route-level prefetching
export function useRoutePrefetching(
  route: "picking" | "shipments" | "pallets" | "items"
) {
  const { prefetchRouteData, isReady } = usePrefetchingStrategy();

  useEffect(() => {
    if (isReady) {
      prefetchRouteData(route);
    }
  }, [route, prefetchRouteData, isReady]);
}

// Hook for optimistic navigation prefetching
export function useNavigationPrefetching() {
  const { prefetchRouteData } = usePrefetchingStrategy();

  const prefetchOnHover = useCallback(
    (route: "picking" | "shipments" | "pallets" | "items") => {
      prefetchRouteData(route);
    },
    [prefetchRouteData]
  );

  return { prefetchOnHover };
}
