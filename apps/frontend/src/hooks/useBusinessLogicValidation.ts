import { useCallback, useMemo } from "react";
import { toast } from "sonner";
import { useWarehouse } from "@/components/providers/warehouse-provider";
import type {
  PickedItem,
  ItemPickingCart,
  OutgoingShipment,
  ItemLocationResult,
  Pallet,
} from "@quildora/types";

// Validation result interface
interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

// Validation configuration
interface ValidationConfig {
  maxItemsPerShipment?: number;
  maxQuantityPerItem?: number;
  maxShipmentWeight?: number;
  requireDestination?: boolean;
  allowNegativeInventory?: boolean;
  enableWarehouseCapacityCheck?: boolean;
  enableItemAvailabilityCheck?: boolean;
}

// Default validation configuration
const DEFAULT_CONFIG: ValidationConfig = {
  maxItemsPerShipment: 50,
  maxQuantityPerItem: 1000,
  maxShipmentWeight: 10000, // kg
  requireDestination: false,
  allowNegativeInventory: false,
  enableWarehouseCapacityCheck: true,
  enableItemAvailabilityCheck: true,
};

interface UseBusinessLogicValidationOptions {
  config?: Partial<ValidationConfig>;
  enableToastNotifications?: boolean;
  onValidationError?: (errors: string[]) => void;
  onValidationWarning?: (warnings: string[]) => void;
}

export function useBusinessLogicValidation(
  options: UseBusinessLogicValidationOptions = {}
) {
  const {
    config = {},
    enableToastNotifications = true,
    onValidationError,
    onValidationWarning,
  } = options;

  const { currentWarehouse } = useWarehouse();
  const validationConfig = { ...DEFAULT_CONFIG, ...config };

  // Show validation feedback to user
  const showValidationFeedback = useCallback(
    (result: ValidationResult) => {
      if (!enableToastNotifications) return;

      if (result.errors.length > 0) {
        toast.error("Validation Error", {
          description: result.errors[0], // Show first error
          duration: 4000,
        });
      }

      if (result.warnings.length > 0) {
        toast.warning("Validation Warning", {
          description: result.warnings[0], // Show first warning
          duration: 3000,
        });
      }
    },
    [enableToastNotifications]
  );

  // Validate item quantity constraints
  const validateItemQuantity = useCallback(
    (item: PickedItem, availableQuantity?: number): ValidationResult => {
      const errors: string[] = [];
      const warnings: string[] = [];

      // Check maximum quantity per item
      if (
        validationConfig.maxQuantityPerItem &&
        item.quantity > validationConfig.maxQuantityPerItem
      ) {
        errors.push(
          `Quantity ${item.quantity} exceeds maximum allowed (${validationConfig.maxQuantityPerItem}) for ${item.itemName}`
        );
      }

      // Check negative quantities
      if (item.quantity <= 0) {
        errors.push(`Quantity must be greater than 0 for ${item.itemName}`);
      }

      // Check availability if provided
      if (
        validationConfig.enableItemAvailabilityCheck &&
        availableQuantity !== undefined
      ) {
        if (item.quantity > availableQuantity) {
          if (validationConfig.allowNegativeInventory) {
            warnings.push(
              `Picking ${item.quantity} of ${item.itemName} will result in negative inventory (available: ${availableQuantity})`
            );
          } else {
            errors.push(
              `Insufficient inventory for ${item.itemName}. Available: ${availableQuantity}, Requested: ${item.quantity}`
            );
          }
        }
      }

      // Warn about large quantities
      if (item.quantity > 100) {
        warnings.push(
          `Large quantity (${item.quantity}) selected for ${item.itemName}. Please verify this is correct.`
        );
      }

      return {
        isValid: errors.length === 0,
        errors,
        warnings,
      };
    },
    [validationConfig]
  );

  // Validate cart constraints
  const validateCart = useCallback(
    (cart: ItemPickingCart): ValidationResult => {
      const errors: string[] = [];
      const warnings: string[] = [];

      // Check if cart is empty
      if (cart.items.length === 0) {
        errors.push("Cart is empty. Please add items before proceeding.");
        return { isValid: false, errors, warnings };
      }

      // Check maximum items per shipment
      if (
        validationConfig.maxItemsPerShipment &&
        cart.items.length > validationConfig.maxItemsPerShipment
      ) {
        errors.push(
          `Too many items in cart (${cart.items.length}). Maximum allowed: ${validationConfig.maxItemsPerShipment}`
        );
      }

      // Check destination requirement
      if (validationConfig.requireDestination && !cart.destination?.trim()) {
        errors.push("Destination is required for shipment creation.");
      }

      // Validate each item in cart
      let hasItemErrors = false;
      cart.items.forEach((item) => {
        const itemValidation = validateItemQuantity(item);
        if (!itemValidation.isValid) {
          hasItemErrors = true;
          errors.push(...itemValidation.errors);
        }
        warnings.push(...itemValidation.warnings);
      });

      // Check for duplicate items from same source
      const duplicates = cart.items.filter(
        (item, index, arr) =>
          arr.findIndex(
            (other) =>
              other.itemId === item.itemId &&
              other.sourcePalletId === item.sourcePalletId
          ) !== index
      );

      if (duplicates.length > 0) {
        warnings.push(
          `Duplicate items detected: ${duplicates
            .map((d) => d.itemName)
            .join(", ")}. Consider consolidating quantities.`
        );
      }

      // Warn about large shipments
      if (cart.totalItems > 20) {
        warnings.push(
          `Large shipment with ${cart.totalItems} total items. Consider splitting into multiple shipments for easier handling.`
        );
      }

      return {
        isValid: errors.length === 0,
        errors,
        warnings,
      };
    },
    [validationConfig, validateItemQuantity]
  );

  // Validate shipment creation
  const validateShipmentCreation = useCallback(
    (
      cart: ItemPickingCart,
      destination?: string,
      destinationCode?: string
    ): ValidationResult => {
      const errors: string[] = [];
      const warnings: string[] = [];

      // First validate the cart
      const cartValidation = validateCart(cart);
      errors.push(...cartValidation.errors);
      warnings.push(...cartValidation.warnings);

      // Validate destination if provided
      if (destination?.trim()) {
        if (destination.length < 2) {
          errors.push("Destination name must be at least 2 characters long.");
        }
        if (destination.length > 100) {
          errors.push("Destination name cannot exceed 100 characters.");
        }
      }

      // Validate destination code if provided
      if (destinationCode?.trim()) {
        if (!/^\d+$/.test(destinationCode.trim())) {
          errors.push("Destination code must contain only numbers.");
        }
        if (destinationCode.length > 10) {
          errors.push("Destination code cannot exceed 10 digits.");
        }
      }

      // Check warehouse context
      if (!currentWarehouse?.id) {
        errors.push(
          "No warehouse selected. Please select a warehouse before creating shipments."
        );
      }

      return {
        isValid: errors.length === 0,
        errors,
        warnings,
      };
    },
    [validateCart, currentWarehouse?.id]
  );

  // Validate pallet creation from picked items
  const validatePalletCreation = useCallback(
    (
      pickedItems: PickedItem[],
      locationId?: string,
      description?: string
    ): ValidationResult => {
      const errors: string[] = [];
      const warnings: string[] = [];

      // Check if items are provided
      if (!pickedItems || pickedItems.length === 0) {
        errors.push("No items selected for pallet creation.");
        return { isValid: false, errors, warnings };
      }

      // Validate location requirement
      if (!locationId?.trim()) {
        errors.push("Storage location is required for pallet creation.");
      }

      // Validate description length
      if (description && description.length > 500) {
        errors.push("Pallet description cannot exceed 500 characters.");
      }

      // Validate each picked item
      pickedItems.forEach((item) => {
        const itemValidation = validateItemQuantity(item);
        errors.push(...itemValidation.errors);
        warnings.push(...itemValidation.warnings);
      });

      // Check for items from multiple locations (potential inefficiency)
      const uniqueLocations = new Set(
        pickedItems.map((item) => item.locationName)
      );
      if (uniqueLocations.size > 3) {
        warnings.push(
          `Items are being picked from ${uniqueLocations.size} different locations. Consider organizing by location for efficiency.`
        );
      }

      // Warn about mixed item types
      const uniqueItems = new Set(pickedItems.map((item) => item.itemId));
      if (uniqueItems.size > 10) {
        warnings.push(
          `Pallet contains ${uniqueItems.size} different item types. Consider grouping similar items together.`
        );
      }

      return {
        isValid: errors.length === 0,
        errors,
        warnings,
      };
    },
    [validateItemQuantity]
  );

  // Validate item location selection
  const validateItemLocationSelection = useCallback(
    (
      item: { id: string; name: string },
      location: ItemLocationResult,
      requestedQuantity: number
    ): ValidationResult => {
      const errors: string[] = [];
      const warnings: string[] = [];

      // Check if location has sufficient quantity
      if (
        validationConfig.enableItemAvailabilityCheck &&
        location.quantity < requestedQuantity
      ) {
        if (validationConfig.allowNegativeInventory) {
          warnings.push(
            `Requested quantity (${requestedQuantity}) exceeds available quantity (${location.quantity}) at ${location.location.name}`
          );
        } else {
          errors.push(
            `Insufficient quantity at ${location.location.name}. Available: ${location.quantity}, Requested: ${requestedQuantity}`
          );
        }
      }

      // Warn about picking entire pallet
      if (location.quantity === requestedQuantity && location.quantity > 1) {
        warnings.push(
          `You are picking the entire quantity from ${location.location.name}. Consider moving the whole pallet instead.`
        );
      }

      // Note: Pallet status checking would require additional API call
      // For now, we'll skip this check as ItemLocationResult doesn't include pallet status
      // This could be enhanced in the future with a separate pallet status lookup

      return {
        isValid: errors.length === 0,
        errors,
        warnings,
      };
    },
    [validationConfig]
  );

  // Validate warehouse capacity constraints
  const validateWarehouseCapacity = useCallback(
    (
      operation: "receiving" | "storage" | "shipping",
      additionalItems?: number
    ): ValidationResult => {
      const errors: string[] = [];
      const warnings: string[] = [];

      if (!validationConfig.enableWarehouseCapacityCheck) {
        return { isValid: true, errors, warnings };
      }

      // This would typically check against warehouse capacity limits
      // For now, we'll implement basic checks
      const estimatedAdditionalItems = additionalItems || 0;

      if (operation === "receiving" && estimatedAdditionalItems > 100) {
        warnings.push(
          `Large receiving operation (${estimatedAdditionalItems} items). Ensure adequate storage space is available.`
        );
      }

      if (operation === "shipping" && estimatedAdditionalItems > 50) {
        warnings.push(
          `Large shipping operation (${estimatedAdditionalItems} items). Ensure adequate staging area is available.`
        );
      }

      return {
        isValid: errors.length === 0,
        errors,
        warnings,
      };
    },
    [validationConfig]
  );

  // Main validation function that handles feedback
  const validateWithFeedback = useCallback(
    (validationFn: () => ValidationResult): ValidationResult => {
      const result = validationFn();

      showValidationFeedback(result);

      if (onValidationError && result.errors.length > 0) {
        onValidationError(result.errors);
      }

      if (onValidationWarning && result.warnings.length > 0) {
        onValidationWarning(result.warnings);
      }

      return result;
    },
    [showValidationFeedback, onValidationError, onValidationWarning]
  );

  return {
    // Validation functions
    validateItemQuantity,
    validateCart,
    validateShipmentCreation,
    validatePalletCreation,
    validateItemLocationSelection,
    validateWarehouseCapacity,

    // Validation with automatic feedback
    validateWithFeedback,

    // Configuration
    validationConfig,

    // Utilities
    showValidationFeedback,
  };
}

// Utility function for quick validation checks
export function createQuickValidator(config?: Partial<ValidationConfig>) {
  return (cart: ItemPickingCart): boolean => {
    const validator = useBusinessLogicValidation({
      config,
      enableToastNotifications: false,
    });
    const result = validator.validateCart(cart);
    return result.isValid;
  };
}

// Pre-configured validation configs for common scenarios
export const validationConfigs = {
  // Strict validation for production environments
  strict: {
    maxItemsPerShipment: 25,
    maxQuantityPerItem: 500,
    requireDestination: true,
    allowNegativeInventory: false,
    enableWarehouseCapacityCheck: true,
    enableItemAvailabilityCheck: true,
  },

  // Lenient validation for testing/development
  lenient: {
    maxItemsPerShipment: 100,
    maxQuantityPerItem: 2000,
    requireDestination: false,
    allowNegativeInventory: true,
    enableWarehouseCapacityCheck: false,
    enableItemAvailabilityCheck: false,
  },

  // Warehouse floor validation (optimized for speed)
  warehouseFloor: {
    maxItemsPerShipment: 50,
    maxQuantityPerItem: 1000,
    requireDestination: false,
    allowNegativeInventory: false,
    enableWarehouseCapacityCheck: true,
    enableItemAvailabilityCheck: true,
  },
};

// Custom hooks for pre-configured validators
export function useStrictValidation(
  options?: UseBusinessLogicValidationOptions
) {
  return useBusinessLogicValidation({
    ...options,
    config: {
      ...validationConfigs.strict,
      ...options?.config,
    },
  });
}

export function useLenientValidation(
  options?: UseBusinessLogicValidationOptions
) {
  return useBusinessLogicValidation({
    ...options,
    config: {
      ...validationConfigs.lenient,
      ...options?.config,
    },
  });
}

export function useWarehouseFloorValidation(
  options?: UseBusinessLogicValidationOptions
) {
  return useBusinessLogicValidation({
    ...options,
    config: {
      ...validationConfigs.warehouseFloor,
      ...options?.config,
    },
  });
}
