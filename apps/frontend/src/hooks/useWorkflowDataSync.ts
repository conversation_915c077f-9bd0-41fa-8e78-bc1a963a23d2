"use client";

import { useEffect, useCallback } from "react";
import { useQueryClient } from "@tanstack/react-query";
import { useWarehouse } from "@/components/providers/warehouse-provider";

/**
 * Hook for managing cross-workflow data synchronization
 * Ensures data consistency between destination-based and item-based picking workflows
 */
export function useWorkflowDataSync() {
  const queryClient = useQueryClient();
  const { currentWarehouse } = useWarehouse();

  // Define query keys that need synchronization across workflows
  const getQueryKeys = useCallback(() => {
    if (!currentWarehouse?.id) {
      return {
        pallets: [],
        destinations: [],
        items: [],
        locations: [],
        shipments: [],
        picklists: [],
        cartData: [],
        metrics: [],
        performance: [],
      };
    }

    return {
      // Core data that affects both workflows
      pallets: ["pallets", currentWarehouse.id],
      destinations: ["destinations", currentWarehouse.id],
      items: ["items", currentWarehouse.id],
      locations: ["locations", currentWarehouse.id],
      shipments: ["shipments", currentWarehouse.id],

      // Workflow-specific data that may affect other workflows
      picklists: ["picklists", currentWarehouse.id],
      cartData: ["cart", currentWarehouse.id],

      // Dashboard and metrics data
      metrics: ["metrics", currentWarehouse.id],
      performance: ["performance", currentWarehouse.id],
    };
  }, [currentWarehouse?.id]);

  /**
   * Invalidate all workflow-related queries to ensure fresh data
   */
  const invalidateAllWorkflowData = useCallback(async () => {
    const queryKeys = getQueryKeys();

    const invalidationPromises = Object.values(queryKeys).map((queryKey) =>
      queryClient.invalidateQueries({ queryKey })
    );

    await Promise.all(invalidationPromises);
  }, [queryClient, getQueryKeys]);

  /**
   * Invalidate specific data types that affect multiple workflows
   */
  const invalidateSharedData = useCallback(
    async (dataTypes: string[] = []) => {
      const queryKeys = getQueryKeys();

      const typesToInvalidate =
        dataTypes.length > 0
          ? dataTypes
          : ["pallets", "destinations", "items", "shipments"]; // Default shared data

      const invalidationPromises = typesToInvalidate
        .filter((type) => queryKeys[type as keyof typeof queryKeys])
        .map((type) =>
          queryClient.invalidateQueries({
            queryKey: queryKeys[type as keyof typeof queryKeys],
          })
        );

      await Promise.all(invalidationPromises);
    },
    [queryClient, getQueryKeys]
  );

  /**
   * Sync data after pallet operations (affects both workflows)
   */
  const syncAfterPalletOperation = useCallback(
    async (operation: string, palletId?: string) => {
      const queryKeys = getQueryKeys();

      // Invalidate pallet-related data
      await queryClient.invalidateQueries({ queryKey: queryKeys.pallets });

      // If specific pallet, invalidate its details
      if (palletId) {
        await queryClient.invalidateQueries({
          queryKey: ["pallet", currentWarehouse?.id, palletId],
        });
      }

      // Invalidate related data based on operation type
      switch (operation) {
        case "move":
        case "create":
        case "update":
          await queryClient.invalidateQueries({
            queryKey: queryKeys.locations,
          });
          await queryClient.invalidateQueries({ queryKey: queryKeys.items });
          break;
        case "release":
        case "ship":
          await queryClient.invalidateQueries({
            queryKey: queryKeys.shipments,
          });
          await queryClient.invalidateQueries({
            queryKey: queryKeys.destinations,
          });
          break;
      }

      // Always invalidate metrics after pallet operations
      await queryClient.invalidateQueries({ queryKey: queryKeys.metrics });
    },
    [queryClient, getQueryKeys, currentWarehouse?.id]
  );

  /**
   * Sync data after shipment operations (affects item picking workflow)
   */
  const syncAfterShipmentOperation = useCallback(
    async (operation: string, shipmentId?: string) => {
      const queryKeys = getQueryKeys();

      // Invalidate shipment-related data
      await queryClient.invalidateQueries({ queryKey: queryKeys.shipments });

      // If specific shipment, invalidate its details
      if (shipmentId) {
        await queryClient.invalidateQueries({
          queryKey: ["shipment", currentWarehouse?.id, shipmentId],
        });
      }

      // Invalidate related data based on operation type
      switch (operation) {
        case "create":
        case "update":
          await queryClient.invalidateQueries({ queryKey: queryKeys.items });
          await queryClient.invalidateQueries({ queryKey: queryKeys.pallets });
          break;
        case "pack":
        case "release":
          await queryClient.invalidateQueries({ queryKey: queryKeys.pallets });
          await queryClient.invalidateQueries({
            queryKey: queryKeys.destinations,
          });
          break;
      }

      // Always invalidate metrics after shipment operations
      await queryClient.invalidateQueries({ queryKey: queryKeys.metrics });
    },
    [queryClient, getQueryKeys, currentWarehouse?.id]
  );

  /**
   * Sync data after item operations (affects both workflows)
   */
  const syncAfterItemOperation = useCallback(
    async (operation: string, itemId?: string) => {
      const queryKeys = getQueryKeys();

      // Invalidate item-related data
      await queryClient.invalidateQueries({ queryKey: queryKeys.items });

      // If specific item, invalidate its details
      if (itemId) {
        await queryClient.invalidateQueries({
          queryKey: ["item", currentWarehouse?.id, itemId],
        });
        await queryClient.invalidateQueries({
          queryKey: ["item-locations", currentWarehouse?.id, itemId],
        });
      }

      // Item operations always affect pallets and potentially shipments
      await queryClient.invalidateQueries({ queryKey: queryKeys.pallets });

      if (operation === "pick" || operation === "release") {
        await queryClient.invalidateQueries({ queryKey: queryKeys.shipments });
      }

      // Always invalidate metrics after item operations
      await queryClient.invalidateQueries({ queryKey: queryKeys.metrics });
    },
    [queryClient, getQueryKeys, currentWarehouse?.id]
  );

  /**
   * Sync data after destination changes (affects both workflows)
   */
  const syncAfterDestinationOperation = useCallback(async () => {
    const queryKeys = getQueryKeys();

    // Invalidate destination-related data
    await queryClient.invalidateQueries({ queryKey: queryKeys.destinations });
    await queryClient.invalidateQueries({ queryKey: queryKeys.pallets });
    await queryClient.invalidateQueries({ queryKey: queryKeys.shipments });

    // Invalidate metrics as destination changes affect workflow metrics
    await queryClient.invalidateQueries({ queryKey: queryKeys.metrics });
  }, [queryClient, getQueryKeys]);

  /**
   * Prefetch related data when switching between workflows
   */
  const prefetchForWorkflow = useCallback(
    async (workflowType: "pallet" | "item" | "picklist") => {
      const queryKeys = getQueryKeys();

      // Common data needed by all workflows
      const commonPrefetches = [
        queryClient.prefetchQuery({
          queryKey: queryKeys.pallets,
          staleTime: 60000, // 1 minute
        }),
        queryClient.prefetchQuery({
          queryKey: queryKeys.destinations,
          staleTime: 300000, // 5 minutes
        }),
      ];

      // Workflow-specific prefetching
      const workflowPrefetches = [];

      switch (workflowType) {
        case "item":
          workflowPrefetches.push(
            queryClient.prefetchQuery({
              queryKey: queryKeys.items,
              staleTime: 300000, // 5 minutes
            }),
            queryClient.prefetchQuery({
              queryKey: queryKeys.shipments,
              staleTime: 60000, // 1 minute
            })
          );
          break;
        case "pallet":
          workflowPrefetches.push(
            queryClient.prefetchQuery({
              queryKey: queryKeys.locations,
              staleTime: 300000, // 5 minutes
            })
          );
          break;
        case "picklist":
          workflowPrefetches.push(
            queryClient.prefetchQuery({
              queryKey: queryKeys.picklists,
              staleTime: 60000, // 1 minute
            })
          );
          break;
      }

      await Promise.all([...commonPrefetches, ...workflowPrefetches]);
    },
    [queryClient, getQueryKeys]
  );

  /**
   * Set up real-time synchronization listeners
   */
  useEffect(() => {
    if (!currentWarehouse?.id) return;

    // Set up periodic cache validation for critical data
    const syncInterval = setInterval(() => {
      // Validate cache freshness for shared data
      const queryKeys = getQueryKeys();

      // Check if critical data is stale and refresh if needed
      const criticalQueries = [
        queryKeys.pallets,
        queryKeys.destinations,
        queryKeys.shipments,
      ];

      criticalQueries.forEach((queryKey) => {
        // Only invalidate if the query key is not empty (warehouse is available)
        if (queryKey.length > 0) {
          queryClient.invalidateQueries({ queryKey });
        }
      });
    }, 30000); // Check every 30 seconds

    return () => clearInterval(syncInterval);
  }, [currentWarehouse?.id, queryClient, getQueryKeys]);

  return {
    // Data invalidation methods
    invalidateAllWorkflowData,
    invalidateSharedData,

    // Operation-specific sync methods
    syncAfterPalletOperation,
    syncAfterShipmentOperation,
    syncAfterItemOperation,
    syncAfterDestinationOperation,

    // Workflow transition methods
    prefetchForWorkflow,

    // Utility methods
    getQueryKeys,
  };
}
