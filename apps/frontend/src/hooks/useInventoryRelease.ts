import { useState, useCallback } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { fetchWithAuth } from "@/lib/api";
import { useAuth } from "@/components/providers/auth-provider";
import { useWarehouse } from "@/components/providers/warehouse-provider";
import type {
  ReleaseInventoryDto,
  InventoryReleaseResponse,
  OutgoingShipment,
} from "@quildora/types";

// API function for releasing inventory
const releaseInventory = async (
  shipmentId: string,
  data: ReleaseInventoryDto,
  token: string | null
): Promise<InventoryReleaseResponse> => {
  return fetchWithAuth(`/api/shipments/${shipmentId}/release`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
    token,
  });
};

// API function for getting shipment details (for optimistic updates)
const getShipmentDetails = async (
  shipmentId: string,
  token: string | null
): Promise<OutgoingShipment> => {
  return fetchWithAuth(`/api/shipments/${shipmentId}`, {
    token,
  });
};

interface UseInventoryReleaseOptions {
  onSuccess?: (response: InventoryReleaseResponse) => void;
  onError?: (error: Error) => void;
  enableOptimisticUpdates?: boolean;
  showSuccessAnimation?: boolean;
}

export function useInventoryRelease(options: UseInventoryReleaseOptions = {}) {
  const {
    onSuccess,
    onError,
    enableOptimisticUpdates = true,
    showSuccessAnimation = true,
  } = options;

  const { appToken } = useAuth();
  const { currentWarehouse } = useWarehouse();
  const queryClient = useQueryClient();

  const [releaseState, setReleaseState] = useState<{
    isReleasing: boolean;
    showConfirmation: boolean;
    lastReleaseResponse: InventoryReleaseResponse | null;
    error: string | null;
  }>({
    isReleasing: false,
    showConfirmation: false,
    lastReleaseResponse: null,
    error: null,
  });

  // Release inventory mutation
  const releaseMutation = useMutation({
    mutationFn: ({
      shipmentId,
      data,
    }: {
      shipmentId: string;
      data: ReleaseInventoryDto;
    }) => releaseInventory(shipmentId, data, appToken),

    onMutate: async ({ shipmentId }) => {
      setReleaseState((prev) => ({
        ...prev,
        isReleasing: true,
        error: null,
      }));

      if (enableOptimisticUpdates) {
        // Cancel any outgoing refetches
        await queryClient.cancelQueries({ queryKey: ["shipments"] });
        await queryClient.cancelQueries({ queryKey: ["packing-list"] });

        // Snapshot the previous value
        const previousShipments = queryClient.getQueryData([
          "shipments",
          currentWarehouse?.id,
        ]);

        // Optimistically update shipment status
        queryClient.setQueryData(
          ["shipments", currentWarehouse?.id],
          (old: any) => {
            if (!old?.shipments) return old;

            return {
              ...old,
              shipments: old.shipments.map((shipment: any) =>
                shipment.id === shipmentId
                  ? { ...shipment, status: "SHIPPED" }
                  : shipment
              ),
            };
          }
        );

        // Return context for rollback
        return { previousShipments };
      }
    },

    onSuccess: (response, { shipmentId }) => {
      setReleaseState((prev) => ({
        ...prev,
        isReleasing: false,
        showConfirmation: showSuccessAnimation,
        lastReleaseResponse: response,
      }));

      // Invalidate and refetch relevant queries with warehouse context
      const warehouseId = currentWarehouse?.id;
      if (warehouseId) {
        // Invalidate shipment-related queries
        queryClient.invalidateQueries({
          queryKey: ["shipments", warehouseId],
        });
        queryClient.invalidateQueries({
          queryKey: ["shipments-search", warehouseId],
        });
        queryClient.invalidateQueries({
          queryKey: ["packing-list", shipmentId],
        });

        // Invalidate inventory-related queries
        queryClient.invalidateQueries({
          queryKey: ["item-locations", warehouseId],
        });
        queryClient.invalidateQueries({
          queryKey: ["pallets", warehouseId],
        });
        queryClient.invalidateQueries({
          queryKey: ["items", warehouseId],
        });
      }

      // Show success toast
      toast.success("Inventory released successfully!", {
        description: `${response.inventoryUpdates.itemsReleased} items shipped`,
        duration: 5000,
      });

      // Call success callback
      onSuccess?.(response);

      // Auto-hide confirmation after delay
      if (showSuccessAnimation) {
        setTimeout(() => {
          setReleaseState((prev) => ({
            ...prev,
            showConfirmation: false,
            lastReleaseResponse: null,
          }));
        }, 3000);
      }
    },

    onError: (error, { shipmentId }, context) => {
      setReleaseState((prev) => ({
        ...prev,
        isReleasing: false,
        error:
          error instanceof Error
            ? error.message
            : "Failed to release inventory",
      }));

      // Rollback optimistic updates
      if (enableOptimisticUpdates && context?.previousShipments) {
        queryClient.setQueryData(
          ["shipments", currentWarehouse?.id],
          context.previousShipments
        );
      }

      // Enhanced error handling with user-friendly messages
      const errorMessage =
        error instanceof Error ? error.message : "Failed to release inventory";

      if (errorMessage.includes("insufficient inventory")) {
        toast.error("Insufficient inventory", {
          description: "Some items don't have enough stock available",
          duration: 6000,
        });
      } else if (errorMessage.includes("already shipped")) {
        toast.error("Shipment already processed", {
          description: "This shipment has already been shipped",
          duration: 6000,
        });
      } else if (errorMessage.includes("warehouse")) {
        toast.error("Warehouse access error", {
          description: "Please check your warehouse permissions",
          duration: 6000,
        });
      } else {
        toast.error("Failed to release inventory", {
          description: errorMessage,
          duration: 6000,
        });
      }

      // Call error callback
      onError?.(error instanceof Error ? error : new Error(errorMessage));
    },
  });

  // Release inventory function
  const releaseInventoryFn = useCallback(
    (shipmentId: string, data: ReleaseInventoryDto) => {
      if (!currentWarehouse) {
        toast.error("No warehouse selected", {
          description: "Please select a warehouse before releasing inventory",
        });
        return;
      }

      releaseMutation.mutate({ shipmentId, data });
    },
    [releaseMutation, currentWarehouse]
  );

  // Quick release function (minimal data)
  const quickRelease = useCallback(
    (shipmentId: string, trackingNumber?: string) => {
      releaseInventoryFn(shipmentId, {
        trackingNumber,
        releaseNotes: "Quick release from warehouse operations",
      });
    },
    [releaseInventoryFn]
  );

  // Batch release function for multiple shipments
  const batchRelease = useCallback(
    async (
      shipmentIds: string[],
      commonData?: Partial<ReleaseInventoryDto>
    ) => {
      if (!currentWarehouse) {
        toast.error("No warehouse selected");
        return;
      }

      const results = [];
      let successCount = 0;
      let errorCount = 0;

      for (const shipmentId of shipmentIds) {
        try {
          // Use the API function directly for batch operations
          const result = await releaseInventory(
            shipmentId,
            {
              releaseNotes: "Batch release",
              ...commonData,
            },
            appToken
          );
          results.push({ shipmentId, success: true, result });
          successCount++;
        } catch (error) {
          results.push({
            shipmentId,
            success: false,
            error: error instanceof Error ? error.message : "Unknown error",
          });
          errorCount++;
        }
      }

      // Show batch results
      if (successCount > 0 && errorCount === 0) {
        toast.success(`Batch release completed`, {
          description: `${successCount} shipments released successfully`,
        });
      } else if (successCount > 0 && errorCount > 0) {
        toast.warning(`Batch release partially completed`, {
          description: `${successCount} succeeded, ${errorCount} failed`,
        });
      } else {
        toast.error(`Batch release failed`, {
          description: `All ${errorCount} shipments failed to release`,
        });
      }

      return results;
    },
    [appToken, currentWarehouse]
  );

  // Clear error state
  const clearError = useCallback(() => {
    setReleaseState((prev) => ({
      ...prev,
      error: null,
    }));
  }, []);

  // Dismiss confirmation
  const dismissConfirmation = useCallback(() => {
    setReleaseState((prev) => ({
      ...prev,
      showConfirmation: false,
      lastReleaseResponse: null,
    }));
  }, []);

  return {
    // State
    isReleasing: releaseState.isReleasing,
    showConfirmation: releaseState.showConfirmation,
    lastReleaseResponse: releaseState.lastReleaseResponse,
    error: releaseState.error,

    // Operations
    releaseInventory: releaseInventoryFn,
    quickRelease,
    batchRelease,

    // Utilities
    clearError,
    dismissConfirmation,

    // Status checks
    canRelease: !releaseState.isReleasing && !!currentWarehouse,
    hasError: !!releaseState.error,
    hasSuccessResponse: !!releaseState.lastReleaseResponse,
  };
}
