import { useState, useCallback, useMemo } from "react";
import type { ItemSearchResult } from "@quildora/types";

interface ItemFilters {
  searchQuery: string;
  minQuantity?: number;
  maxQuantity?: number;
  hasStock?: boolean;
  unitOfMeasure?: string;
}

interface UseItemFilteringOptions {
  enableFuzzySearch?: boolean;
  caseSensitive?: boolean;
  searchFields?: Array<keyof ItemSearchResult>;
}

/**
 * Custom hook for client-side item filtering
 * Provides instant search results without API calls
 */
export function useItemFiltering(
  items: ItemSearchResult[] = [],
  options: UseItemFilteringOptions = {}
) {
  const {
    enableFuzzySearch = false,
    caseSensitive = false,
    searchFields = ["name", "sku", "description"],
  } = options;

  const [filters, setFilters] = useState<ItemFilters>({
    searchQuery: "",
  });

  // Update search query
  const updateSearchQuery = useCallback((query: string) => {
    setFilters(prev => ({ ...prev, searchQuery: query }));
  }, []);

  // Update other filters
  const updateFilters = useCallback((newFilters: Partial<ItemFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  }, []);

  // Reset all filters
  const resetFilters = useCallback(() => {
    setFilters({ searchQuery: "" });
  }, []);

  // Fuzzy search implementation (simple Levenshtein distance)
  const fuzzyMatch = useCallback((text: string, query: string): boolean => {
    if (!enableFuzzySearch) return false;
    
    const textLower = caseSensitive ? text : text.toLowerCase();
    const queryLower = caseSensitive ? query : query.toLowerCase();
    
    // Simple fuzzy matching - allow 1 character difference for every 4 characters
    const maxDistance = Math.floor(queryLower.length / 4) + 1;
    
    // Simple implementation - check if most characters match
    let matches = 0;
    for (let i = 0; i < Math.min(textLower.length, queryLower.length); i++) {
      if (textLower[i] === queryLower[i]) matches++;
    }
    
    const similarity = matches / Math.max(textLower.length, queryLower.length);
    return similarity >= 0.7; // 70% similarity threshold
  }, [enableFuzzySearch, caseSensitive]);

  // Text matching function
  const matchesText = useCallback((text: string | null, query: string): boolean => {
    if (!text || !query.trim()) return !query.trim();
    
    const textToSearch = caseSensitive ? text : text.toLowerCase();
    const searchQuery = caseSensitive ? query : query.toLowerCase();
    
    // Exact substring match
    if (textToSearch.includes(searchQuery)) return true;
    
    // Fuzzy match if enabled
    return fuzzyMatch(text, query);
  }, [caseSensitive, fuzzyMatch]);

  // Main filtering function
  const filteredItems = useMemo(() => {
    if (!items.length) return [];

    return items.filter((item) => {
      // Search query filter
      if (filters.searchQuery.trim()) {
        const query = filters.searchQuery.trim();
        const matchesAnyField = searchFields.some(field => {
          const value = item[field];
          if (typeof value === "string") {
            return matchesText(value, query);
          }
          return false;
        });

        if (!matchesAnyField) return false;
      }

      // Stock filters
      if (filters.hasStock !== undefined) {
        const hasStock = item.availableQuantity > 0;
        if (filters.hasStock !== hasStock) return false;
      }

      if (filters.minQuantity !== undefined) {
        if (item.availableQuantity < filters.minQuantity) return false;
      }

      if (filters.maxQuantity !== undefined) {
        if (item.availableQuantity > filters.maxQuantity) return false;
      }

      // Unit of measure filter
      if (filters.unitOfMeasure) {
        if (item.unitOfMeasure !== filters.unitOfMeasure) return false;
      }

      return true;
    });
  }, [items, filters, searchFields, matchesText]);

  // Search suggestions based on current query
  const searchSuggestions = useMemo(() => {
    if (!filters.searchQuery.trim() || filters.searchQuery.length < 2) {
      return [];
    }

    // Return top 10 matches for autocomplete
    return filteredItems.slice(0, 10);
  }, [filteredItems, filters.searchQuery]);

  // Get unique values for filter options
  const filterOptions = useMemo(() => {
    const unitOfMeasures = new Set<string>();
    let minQuantity = Infinity;
    let maxQuantity = -Infinity;

    items.forEach(item => {
      unitOfMeasures.add(item.unitOfMeasure);
      minQuantity = Math.min(minQuantity, item.availableQuantity);
      maxQuantity = Math.max(maxQuantity, item.availableQuantity);
    });

    return {
      unitOfMeasures: Array.from(unitOfMeasures).sort(),
      quantityRange: {
        min: minQuantity === Infinity ? 0 : minQuantity,
        max: maxQuantity === -Infinity ? 0 : maxQuantity,
      },
    };
  }, [items]);

  // Performance metrics
  const metrics = useMemo(() => ({
    totalItems: items.length,
    filteredItems: filteredItems.length,
    filteringRatio: items.length > 0 ? filteredItems.length / items.length : 0,
    hasActiveFilters: filters.searchQuery.trim() !== "" || 
                     filters.hasStock !== undefined ||
                     filters.minQuantity !== undefined ||
                     filters.maxQuantity !== undefined ||
                     filters.unitOfMeasure !== undefined,
  }), [items.length, filteredItems.length, filters]);

  // Quick search function for external use
  const quickSearch = useCallback((query: string): ItemSearchResult[] => {
    if (!query.trim() || query.length < 2) return [];
    
    return items.filter(item => {
      return searchFields.some(field => {
        const value = item[field];
        if (typeof value === "string") {
          return matchesText(value, query);
        }
        return false;
      });
    }).slice(0, 10);
  }, [items, searchFields, matchesText]);

  return {
    // Filtered data
    filteredItems,
    searchSuggestions,
    
    // Filter state
    filters,
    updateSearchQuery,
    updateFilters,
    resetFilters,
    
    // Filter options
    filterOptions,
    
    // Utilities
    quickSearch,
    metrics,
    
    // Status
    isFiltering: filters.searchQuery.trim() !== "",
    hasResults: filteredItems.length > 0,
    isEmpty: items.length === 0,
  };
}

// Utility function for highlighting search terms in text
export function highlightSearchTerm(
  text: string, 
  searchTerm: string, 
  className: string = "bg-yellow-200 font-medium"
): string {
  if (!searchTerm.trim()) return text;
  
  const regex = new RegExp(`(${searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
  return text.replace(regex, `<span class="${className}">$1</span>`);
}

// Utility function for creating search result snippets
export function createSearchSnippet(
  text: string, 
  searchTerm: string, 
  maxLength: number = 100
): string {
  if (!searchTerm.trim() || !text) return text.slice(0, maxLength);
  
  const index = text.toLowerCase().indexOf(searchTerm.toLowerCase());
  if (index === -1) return text.slice(0, maxLength);
  
  const start = Math.max(0, index - Math.floor((maxLength - searchTerm.length) / 2));
  const end = Math.min(text.length, start + maxLength);
  
  let snippet = text.slice(start, end);
  if (start > 0) snippet = "..." + snippet;
  if (end < text.length) snippet = snippet + "...";
  
  return snippet;
}
