import { useEffect, useCallback, useRef } from "react";
import { useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { useAuth } from "@/components/providers/auth-provider";
import { useWarehouse } from "@/components/providers/warehouse-provider";
import type { ShipmentStatus, OutgoingShipment } from "@quildora/types";

// Simulated real-time events (in production, this would be WebSocket/SSE)
interface ShipmentUpdateEvent {
  type: "SHIPMENT_STATUS_CHANGED" | "SHIPMENT_CREATED" | "SHIPMENT_DELETED";
  shipmentId: string;
  warehouseId: string;
  data: {
    status?: ShipmentStatus;
    shipment?: OutgoingShipment;
    timestamp: number;
  };
}

// Polling intervals for different update types
const POLLING_INTERVALS = {
  ACTIVE_SHIPMENTS: 30000, // 30 seconds for active shipments
  ALL_SHIPMENTS: 60000, // 1 minute for general shipment list
  BACKGROUND: 300000, // 5 minutes for background updates
} as const;

interface UseRealtimeShipmentUpdatesOptions {
  enablePolling?: boolean;
  enableToastNotifications?: boolean;
  pollingInterval?: number;
  focusedShipmentId?: string;
}

export function useRealtimeShipmentUpdates(
  options: UseRealtimeShipmentUpdatesOptions = {}
) {
  const {
    enablePolling = true,
    enableToastNotifications = true,
    pollingInterval = POLLING_INTERVALS.ACTIVE_SHIPMENTS,
    focusedShipmentId,
  } = options;

  const { appToken } = useAuth();
  const { currentWarehouse } = useWarehouse();
  const queryClient = useQueryClient();
  const pollingRef = useRef<NodeJS.Timeout | null>(null);
  const lastUpdateRef = useRef<number>(Date.now());

  // Handle shipment status updates
  const handleShipmentUpdate = useCallback(
    (event: ShipmentUpdateEvent) => {
      if (event.warehouseId !== currentWarehouse?.id) return;

      const { type, shipmentId, data } = event;

      switch (type) {
        case "SHIPMENT_STATUS_CHANGED":
          // Update shipment in all relevant queries
          queryClient.setQueriesData(
            { queryKey: ["shipments"] },
            (oldData: any) => {
              if (!oldData?.shipments) return oldData;

              return {
                ...oldData,
                shipments: oldData.shipments.map((shipment: OutgoingShipment) =>
                  shipment.id === shipmentId
                    ? {
                        ...shipment,
                        status: data.status,
                        updatedAt: new Date().toISOString(),
                      }
                    : shipment
                ),
              };
            }
          );

          // Update specific shipment query if it exists
          queryClient.setQueryData(
            ["shipment", shipmentId],
            (oldData: OutgoingShipment | undefined) => {
              if (!oldData) return oldData;
              return {
                ...oldData,
                status: data.status!,
                updatedAt: new Date().toISOString(),
              };
            }
          );

          // Show toast notification for status changes
          if (enableToastNotifications && data.status) {
            const statusMessages: Record<ShipmentStatus, string> = {
              PREPARING: "Shipment is being prepared",
              PACKED: "Shipment has been packed",
              SHIPPED: "Shipment has been shipped",
              DELIVERED: "Shipment has been delivered",
              CANCELLED: "Shipment has been cancelled",
            };

            toast.info("Shipment Status Updated", {
              description: `${statusMessages[data.status]} (${shipmentId.slice(
                -8
              )})`,
              duration: 4000,
            });
          }
          break;

        case "SHIPMENT_CREATED":
          // Invalidate shipment lists to include new shipment
          queryClient.invalidateQueries({
            queryKey: ["shipments", currentWarehouse?.id],
          });
          queryClient.invalidateQueries({
            queryKey: ["shipments-search", currentWarehouse?.id],
          });

          if (enableToastNotifications) {
            toast.success("New Shipment Created", {
              description: `Shipment ${shipmentId.slice(-8)} has been created`,
              duration: 4000,
            });
          }
          break;

        case "SHIPMENT_DELETED":
          // Remove shipment from all queries
          queryClient.setQueriesData(
            { queryKey: ["shipments"] },
            (oldData: any) => {
              if (!oldData?.shipments) return oldData;

              return {
                ...oldData,
                shipments: oldData.shipments.filter(
                  (shipment: OutgoingShipment) => shipment.id !== shipmentId
                ),
              };
            }
          );

          // Remove specific shipment query
          queryClient.removeQueries({ queryKey: ["shipment", shipmentId] });

          if (enableToastNotifications) {
            toast.info("Shipment Removed", {
              description: `Shipment ${shipmentId.slice(-8)} has been removed`,
              duration: 4000,
            });
          }
          break;
      }

      lastUpdateRef.current = Date.now();
    },
    [currentWarehouse?.id, queryClient, enableToastNotifications]
  );

  // Simulate real-time updates with polling (replace with WebSocket in production)
  const startPolling = useCallback(() => {
    if (!enablePolling || !appToken || !currentWarehouse?.id) return;

    const poll = async () => {
      try {
        // In production, this would be a WebSocket connection or Server-Sent Events
        // For now, we'll use intelligent cache invalidation based on time
        const timeSinceLastUpdate = Date.now() - lastUpdateRef.current;

        // Only invalidate if enough time has passed and user is active
        if (timeSinceLastUpdate > pollingInterval) {
          // Invalidate active shipment queries to trigger refetch
          queryClient.invalidateQueries({
            queryKey: ["shipments-search", currentWarehouse.id],
            refetchType: "active", // Only refetch if component is mounted
          });

          // If focused on a specific shipment, update it more frequently
          if (focusedShipmentId) {
            queryClient.invalidateQueries({
              queryKey: ["shipment", focusedShipmentId],
              refetchType: "active",
            });
            queryClient.invalidateQueries({
              queryKey: ["packing-list", focusedShipmentId],
              refetchType: "active",
            });
          }

          lastUpdateRef.current = Date.now();
        }
      } catch (error) {
        console.warn("Polling update failed:", error);
      }
    };

    // Start polling
    poll();
    pollingRef.current = setInterval(poll, pollingInterval);
  }, [
    enablePolling,
    appToken,
    currentWarehouse?.id,
    pollingInterval,
    focusedShipmentId,
    queryClient,
  ]);

  // Stop polling
  const stopPolling = useCallback(() => {
    if (pollingRef.current) {
      clearInterval(pollingRef.current);
      pollingRef.current = null;
    }
  }, []);

  // Restart polling with new interval
  const restartPolling = useCallback(
    (newInterval?: number) => {
      stopPolling();
      if (newInterval) {
        setTimeout(() => startPolling(), 100);
      } else {
        startPolling();
      }
    },
    [stopPolling, startPolling]
  );

  // Manual refresh function
  const refreshShipmentData = useCallback(() => {
    if (!currentWarehouse?.id) return;

    // Invalidate all shipment-related queries
    queryClient.invalidateQueries({
      queryKey: ["shipments", currentWarehouse.id],
    });
    queryClient.invalidateQueries({
      queryKey: ["shipments-search", currentWarehouse.id],
    });

    if (focusedShipmentId) {
      queryClient.invalidateQueries({
        queryKey: ["shipment", focusedShipmentId],
      });
      queryClient.invalidateQueries({
        queryKey: ["packing-list", focusedShipmentId],
      });
    }

    toast.success("Shipment data refreshed", {
      duration: 2000,
    });
  }, [currentWarehouse?.id, focusedShipmentId, queryClient]);

  // Subscribe to shipment updates for specific shipment
  const subscribeToShipment = useCallback(
    (shipmentId: string) => {
      // In production, this would establish a WebSocket subscription
      // For now, we'll use more frequent polling for the specific shipment
      queryClient.invalidateQueries({
        queryKey: ["shipment", shipmentId],
        refetchType: "active",
      });
    },
    [queryClient]
  );

  // Unsubscribe from shipment updates
  const unsubscribeFromShipment = useCallback(
    (shipmentId: string) => {
      // In production, this would close the WebSocket subscription
      // For now, we'll just ensure the query is not being actively polled
      queryClient.cancelQueries({
        queryKey: ["shipment", shipmentId],
      });
    },
    [queryClient]
  );

  // Handle page visibility changes to optimize polling
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden) {
        // Reduce polling frequency when page is hidden
        stopPolling();
        setTimeout(() => {
          if (!document.hidden) return; // Don't start if page became visible
          pollingRef.current = setInterval(() => {
            queryClient.invalidateQueries({
              queryKey: ["shipments-search", currentWarehouse?.id],
              refetchType: "none", // Don't refetch immediately
            });
          }, POLLING_INTERVALS.BACKGROUND);
        }, 1000);
      } else {
        // Resume normal polling when page becomes visible
        restartPolling();
      }
    };

    document.addEventListener("visibilitychange", handleVisibilityChange);
    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange);
    };
  }, [stopPolling, restartPolling, queryClient, currentWarehouse?.id]);

  // Start/stop polling based on options and warehouse
  useEffect(() => {
    if (enablePolling && currentWarehouse?.id) {
      startPolling();
    } else {
      stopPolling();
    }

    return stopPolling;
  }, [enablePolling, currentWarehouse?.id, startPolling, stopPolling]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopPolling();
    };
  }, [stopPolling]);

  return {
    // Control functions
    refreshShipmentData,
    subscribeToShipment,
    unsubscribeFromShipment,
    restartPolling,

    // Status
    isPolling: pollingRef.current !== null,
    lastUpdate: lastUpdateRef.current,

    // Manual event simulation (for testing)
    simulateShipmentUpdate: handleShipmentUpdate,
  };
}

// Hook for shipment-specific real-time updates
export function useShipmentRealtimeUpdates(shipmentId: string) {
  return useRealtimeShipmentUpdates({
    focusedShipmentId: shipmentId,
    pollingInterval: POLLING_INTERVALS.ACTIVE_SHIPMENTS,
    enableToastNotifications: true,
  });
}

// Hook for background shipment monitoring
export function useBackgroundShipmentMonitoring() {
  return useRealtimeShipmentUpdates({
    enablePolling: true,
    enableToastNotifications: false,
    pollingInterval: POLLING_INTERVALS.BACKGROUND,
  });
}
