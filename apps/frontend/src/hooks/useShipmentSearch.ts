import { useState, useCallback, useMemo } from "react";
import { useSuspenseQuery } from "@tanstack/react-query";
import { toast } from "sonner";
import { fetchWithAuth } from "@/lib/api";
import { useAuth } from "@/components/providers/auth-provider";
import { useWarehouse } from "@/components/providers/warehouse-provider";
import { useDebounce } from "@/hooks/useDebounce";
import { ShipmentStatus } from "@quildora/types";
import type {
  ShipmentSearchResponse,
  ShipmentSearchResult,
  ShipmentFilters,
} from "@quildora/types";

// Extended filters type for search functionality
type ExtendedShipmentFilters = ShipmentFilters & {
  destination?: string;
  shipmentNumber?: string;
  dateFrom?: string;
  dateTo?: string;
  offset: number;
};

// API function for searching shipments
const searchShipments = async (
  filters: ExtendedShipmentFilters,
  warehouseId: string,
  token: string | null
): Promise<ShipmentSearchResponse> => {
  const searchParams = new URLSearchParams();

  // Add warehouse context
  searchParams.append("warehouseId", warehouseId);

  // Add filters
  if (filters.status) searchParams.append("status", filters.status);
  if (filters.destination)
    searchParams.append("destination", filters.destination);
  if (filters.shipmentNumber)
    searchParams.append("shipmentNumber", filters.shipmentNumber);
  if (filters.dateFrom) searchParams.append("dateFrom", filters.dateFrom);
  if (filters.dateTo) searchParams.append("dateTo", filters.dateTo);

  // Add pagination
  if (filters.limit) searchParams.append("limit", filters.limit.toString());
  if (filters.offset) searchParams.append("offset", filters.offset.toString());

  // Add sorting
  if (filters.sortBy) searchParams.append("sortBy", filters.sortBy);
  if (filters.sortOrder) searchParams.append("sortOrder", filters.sortOrder);

  return fetchWithAuth(`/api/shipments/search?${searchParams.toString()}`, {
    token,
  });
};

interface UseShipmentSearchOptions {
  pageSize?: number;
  defaultStatus?: ShipmentStatus;
  enableAutoRefresh?: boolean;
  refreshInterval?: number;
}

export function useShipmentSearch(options: UseShipmentSearchOptions = {}) {
  const {
    pageSize = 20,
    defaultStatus,
    enableAutoRefresh = false,
    refreshInterval = 30000,
  } = options;

  const { appToken } = useAuth();
  const { currentWarehouse } = useWarehouse();

  // Filter state
  const [filters, setFilters] = useState<ExtendedShipmentFilters>({
    status: defaultStatus,
    destination: "",
    shipmentNumber: "",
    dateFrom: "",
    dateTo: "",
    limit: pageSize,
    offset: 0,
    sortBy: "createdAt",
    sortOrder: "desc",
  });

  // Debounce text-based filters to avoid excessive API calls
  const debouncedDestination = useDebounce(filters.destination || "", 300);
  const debouncedShipmentNumber = useDebounce(
    filters.shipmentNumber || "",
    300
  );

  // Create effective filters with debounced values
  const effectiveFilters = useMemo(
    () => ({
      ...filters,
      destination: debouncedDestination || undefined,
      shipmentNumber: debouncedShipmentNumber || undefined,
    }),
    [filters, debouncedDestination, debouncedShipmentNumber]
  );

  // Fetch shipments with useSuspenseQuery
  const { data: searchResults, error } = useSuspenseQuery<
    ShipmentSearchResponse,
    Error
  >({
    queryKey: ["shipments-search", currentWarehouse?.id, effectiveFilters],
    queryFn: () => {
      if (!currentWarehouse?.id) {
        throw new Error("No warehouse selected");
      }
      return searchShipments(effectiveFilters, currentWarehouse.id, appToken);
    },
    staleTime: enableAutoRefresh ? refreshInterval : 300000, // 5 minutes default
    refetchInterval: enableAutoRefresh ? refreshInterval : false,
    retry: (failureCount, error) => {
      // Don't retry on authentication or permission errors
      if (
        error.message.includes("unauthorized") ||
        error.message.includes("forbidden")
      ) {
        return false;
      }
      return failureCount < 2;
    },
  });

  const shipments = searchResults?.shipments || [];
  const pagination = searchResults?.pagination;
  const availableFilters = searchResults?.filters;

  // Filter update functions
  const updateStatus = useCallback((status: ShipmentStatus | "") => {
    setFilters((prev) => ({
      ...prev,
      status: status || undefined,
      offset: 0, // Reset to first page
    }));
  }, []);

  const updateDestination = useCallback((destination: string) => {
    setFilters((prev) => ({
      ...prev,
      destination,
      offset: 0, // Reset to first page
    }));
  }, []);

  const updateShipmentNumber = useCallback((shipmentNumber: string) => {
    setFilters((prev) => ({
      ...prev,
      shipmentNumber,
      offset: 0, // Reset to first page
    }));
  }, []);

  const updateDateRange = useCallback((dateFrom?: string, dateTo?: string) => {
    setFilters((prev) => ({
      ...prev,
      dateFrom,
      dateTo,
      offset: 0, // Reset to first page
    }));
  }, []);

  const updateSorting = useCallback(
    (sortBy: string, sortOrder: "asc" | "desc" = "desc") => {
      setFilters((prev) => ({
        ...prev,
        sortBy,
        sortOrder,
        offset: 0, // Reset to first page
      }));
    },
    []
  );

  // Pagination functions
  const goToPage = useCallback(
    (page: number) => {
      const newOffset = page * pageSize;
      setFilters((prev) => ({
        ...prev,
        offset: newOffset,
      }));
    },
    [pageSize]
  );

  const nextPage = useCallback(() => {
    if (pagination?.hasMore) {
      setFilters((prev) => ({
        ...prev,
        offset: prev.offset + pageSize,
      }));
    }
  }, [pagination?.hasMore, pageSize]);

  const prevPage = useCallback(() => {
    if (filters.offset > 0) {
      setFilters((prev) => ({
        ...prev,
        offset: Math.max(0, prev.offset - pageSize),
      }));
    }
  }, [filters.offset, pageSize]);

  // Clear all filters
  const clearFilters = useCallback(() => {
    setFilters({
      status: defaultStatus,
      destination: "",
      shipmentNumber: "",
      dateFrom: "",
      dateTo: "",
      limit: pageSize,
      offset: 0,
      sortBy: "createdAt",
      sortOrder: "desc",
    });
    toast.success("Filters cleared");
  }, [defaultStatus, pageSize]);

  // Quick filter presets
  const applyQuickFilter = useCallback(
    (preset: "today" | "week" | "pending" | "shipped") => {
      const today = new Date();
      const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);

      switch (preset) {
        case "today":
          updateDateRange(
            today.toISOString().split("T")[0],
            today.toISOString().split("T")[0]
          );
          break;
        case "week":
          updateDateRange(
            weekAgo.toISOString().split("T")[0],
            today.toISOString().split("T")[0]
          );
          break;
        case "pending":
          updateStatus(ShipmentStatus.PREPARING);
          break;
        case "shipped":
          updateStatus(ShipmentStatus.SHIPPED);
          break;
      }
    },
    [updateDateRange, updateStatus]
  );

  // Search utilities
  const hasActiveFilters = useMemo(() => {
    return !!(
      filters.status ||
      filters.destination ||
      filters.shipmentNumber ||
      filters.dateFrom ||
      filters.dateTo
    );
  }, [filters]);

  const currentPage = Math.floor(filters.offset / pageSize);
  const totalPages = pagination ? Math.ceil(pagination.total / pageSize) : 0;

  // Find specific shipment
  const findShipment = useCallback(
    (shipmentId: string): ShipmentSearchResult | undefined => {
      return shipments.find((shipment) => shipment.id === shipmentId);
    },
    [shipments]
  );

  // Get shipments by status
  const getShipmentsByStatus = useCallback(
    (status: ShipmentStatus): ShipmentSearchResult[] => {
      return shipments.filter((shipment) => shipment.status === status);
    },
    [shipments]
  );

  return {
    // Data
    shipments,
    pagination,
    availableFilters,
    error,

    // Current state
    filters: effectiveFilters,
    hasActiveFilters,
    currentPage,
    totalPages,

    // Filter functions
    updateStatus,
    updateDestination,
    updateShipmentNumber,
    updateDateRange,
    updateSorting,
    clearFilters,
    applyQuickFilter,

    // Pagination functions
    goToPage,
    nextPage,
    prevPage,
    canGoNext: pagination?.hasMore || false,
    canGoPrev: filters.offset > 0,

    // Utility functions
    findShipment,
    getShipmentsByStatus,

    // Loading states
    isLoading: false, // useSuspenseQuery doesn't have loading state
    isEmpty: shipments.length === 0,
    hasResults: shipments.length > 0,
  };
}
