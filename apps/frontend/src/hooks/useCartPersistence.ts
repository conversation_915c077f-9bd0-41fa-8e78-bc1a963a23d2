import { useState, useEffect, useCallback } from "react";
import { useWarehouse } from "@/components/providers/warehouse-provider";
import { useAuth } from "@/components/providers/auth-provider";
import type { ItemPickingCart, PickedItem } from "@quildora/types";

// Storage keys for different cart types
const STORAGE_KEYS = {
  PICKING_CART: "quildora_picking_cart",
  CART_METADATA: "quildora_cart_metadata",
} as const;

// Cart metadata for validation and cleanup
interface CartMetadata {
  warehouseId: string;
  userId: string;
  lastUpdated: number;
  version: string;
}

// Default empty cart
const createEmptyCart = (): ItemPickingCart => ({
  items: [],
  totalItems: 0,
  totalQuantity: 0,
  destination: "",
  destinationCode: "",
});

// Cart validation and migration
const validateCart = (cart: any): cart is ItemPickingCart => {
  return (
    cart &&
    typeof cart === "object" &&
    Array.isArray(cart.items) &&
    typeof cart.totalItems === "number" &&
    typeof cart.totalQuantity === "number" &&
    typeof cart.destination === "string" &&
    typeof cart.destinationCode === "string"
  );
};

// Migrate old cart formats if needed
const migrateCart = (cart: any): ItemPickingCart => {
  if (!cart || typeof cart !== "object") {
    return createEmptyCart();
  }

  // Calculate totals from items if missing
  const items = Array.isArray(cart.items) ? cart.items : [];
  const totalItems = items.reduce(
    (sum: number, item: any) => sum + (item.quantity || 0),
    0
  );
  const totalQuantity = totalItems; // For now, totalQuantity equals totalItems

  // Ensure all required fields exist
  return {
    items,
    totalItems:
      typeof cart.totalItems === "number" ? cart.totalItems : totalItems,
    totalQuantity:
      typeof cart.totalQuantity === "number"
        ? cart.totalQuantity
        : totalQuantity,
    destination: typeof cart.destination === "string" ? cart.destination : "",
    destinationCode:
      typeof cart.destinationCode === "string" ? cart.destinationCode : "",
  };
};

interface UseCartPersistenceOptions {
  autoSave?: boolean;
  saveDelay?: number;
  maxAge?: number; // Maximum age in milliseconds before cart expires
  enableCrossSession?: boolean;
}

export function useCartPersistence(options: UseCartPersistenceOptions = {}) {
  const {
    autoSave = true,
    saveDelay = 1000, // 1 second delay for debounced saving
    maxAge = 24 * 60 * 60 * 1000, // 24 hours
    enableCrossSession = true,
  } = options;

  const { currentWarehouse } = useWarehouse();
  const { appUser } = useAuth();
  const [cart, setCart] = useState<ItemPickingCart>(createEmptyCart);
  const [isLoading, setIsLoading] = useState(true);
  const [saveTimeout, setSaveTimeout] = useState<NodeJS.Timeout | null>(null);

  // Generate storage key with warehouse and user context
  const getStorageKey = useCallback(
    (key: string) => {
      if (!currentWarehouse?.id || !appUser?.id) return null;
      return `${key}_${currentWarehouse.id}_${appUser.id}`;
    },
    [currentWarehouse?.id, appUser?.id]
  );

  // Load cart from localStorage
  const loadCart = useCallback(() => {
    if (!enableCrossSession) {
      setIsLoading(false);
      return;
    }

    const cartKey = getStorageKey(STORAGE_KEYS.PICKING_CART);
    const metadataKey = getStorageKey(STORAGE_KEYS.CART_METADATA);

    if (!cartKey || !metadataKey) {
      setIsLoading(false);
      return;
    }

    try {
      const savedCart = localStorage.getItem(cartKey);
      const savedMetadata = localStorage.getItem(metadataKey);

      if (!savedCart || !savedMetadata) {
        setIsLoading(false);
        return;
      }

      const metadata: CartMetadata = JSON.parse(savedMetadata);
      const now = Date.now();

      // Check if cart is expired
      if (now - metadata.lastUpdated > maxAge) {
        clearCart();
        setIsLoading(false);
        return;
      }

      // Validate warehouse and user context
      if (
        metadata.warehouseId !== currentWarehouse?.id ||
        metadata.userId !== appUser?.id
      ) {
        setIsLoading(false);
        return;
      }

      const parsedCart = JSON.parse(savedCart);

      if (validateCart(parsedCart)) {
        setCart(parsedCart);
      } else {
        // Try to migrate if validation fails
        const migratedCart = migrateCart(parsedCart);
        setCart(migratedCart);
      }
    } catch (error) {
      console.warn("Failed to load cart from storage:", error);
      setCart(createEmptyCart());
    } finally {
      setIsLoading(false);
    }
  }, [
    enableCrossSession,
    getStorageKey,
    maxAge,
    currentWarehouse?.id,
    appUser?.id,
  ]);

  // Save cart to localStorage
  const saveCart = useCallback(
    (cartToSave: ItemPickingCart) => {
      if (!enableCrossSession) return;

      const cartKey = getStorageKey(STORAGE_KEYS.PICKING_CART);
      const metadataKey = getStorageKey(STORAGE_KEYS.CART_METADATA);

      if (!cartKey || !metadataKey || !currentWarehouse?.id || !appUser?.id) {
        return;
      }

      try {
        const metadata: CartMetadata = {
          warehouseId: currentWarehouse.id,
          userId: appUser.id,
          lastUpdated: Date.now(),
          version: "1.0",
        };

        localStorage.setItem(cartKey, JSON.stringify(cartToSave));
        localStorage.setItem(metadataKey, JSON.stringify(metadata));
      } catch (error) {
        console.warn("Failed to save cart to storage:", error);
      }
    },
    [enableCrossSession, getStorageKey, currentWarehouse?.id, appUser?.id]
  );

  // Debounced save function
  const debouncedSave = useCallback(
    (cartToSave: ItemPickingCart) => {
      if (!autoSave) return;

      if (saveTimeout) {
        clearTimeout(saveTimeout);
      }

      const timeout = setTimeout(() => {
        saveCart(cartToSave);
        setSaveTimeout(null);
      }, saveDelay);

      setSaveTimeout(timeout);
    },
    [autoSave, saveTimeout, saveDelay, saveCart]
  );

  // Clear cart and storage
  const clearCart = useCallback(() => {
    const cartKey = getStorageKey(STORAGE_KEYS.PICKING_CART);
    const metadataKey = getStorageKey(STORAGE_KEYS.CART_METADATA);

    setCart(createEmptyCart());

    if (cartKey && metadataKey) {
      try {
        localStorage.removeItem(cartKey);
        localStorage.removeItem(metadataKey);
      } catch (error) {
        console.warn("Failed to clear cart from storage:", error);
      }
    }
  }, [getStorageKey]);

  // Update cart with automatic saving
  const updateCart = useCallback(
    (updater: (prevCart: ItemPickingCart) => ItemPickingCart) => {
      setCart((prevCart) => {
        const newCart = updater(prevCart);
        debouncedSave(newCart);
        return newCart;
      });
    },
    [debouncedSave]
  );

  // Add item to cart
  const addItem = useCallback(
    (item: PickedItem) => {
      updateCart((prevCart) => {
        const existingItemIndex = prevCart.items.findIndex(
          (existingItem) =>
            existingItem.itemId === item.itemId &&
            existingItem.sourcePalletId === item.sourcePalletId
        );

        let newItems: PickedItem[];

        if (existingItemIndex >= 0) {
          // Update existing item quantity
          newItems = [...prevCart.items];
          newItems[existingItemIndex] = {
            ...newItems[existingItemIndex],
            quantity: newItems[existingItemIndex].quantity + item.quantity,
          };
        } else {
          // Add new item
          newItems = [...prevCart.items, item];
        }

        const totalItems = newItems.reduce(
          (sum, item) => sum + item.quantity,
          0
        );
        const totalQuantity = totalItems; // For now, totalQuantity equals totalItems

        return {
          ...prevCart,
          items: newItems,
          totalItems,
          totalQuantity,
        };
      });
    },
    [updateCart]
  );

  // Remove item from cart
  const removeItem = useCallback(
    (itemId: string, sourcePalletId: string | undefined) => {
      updateCart((prevCart) => {
        const newItems = prevCart.items.filter(
          (item) =>
            !(item.itemId === itemId && item.sourcePalletId === sourcePalletId)
        );

        const totalItems = newItems.reduce(
          (sum, item) => sum + item.quantity,
          0
        );
        const totalQuantity = totalItems; // For now, totalQuantity equals totalItems

        return {
          ...prevCart,
          items: newItems,
          totalItems,
          totalQuantity,
        };
      });
    },
    [updateCart]
  );

  // Update item quantity
  const updateItemQuantity = useCallback(
    (
      itemId: string,
      sourcePalletId: string | undefined,
      newQuantity: number
    ) => {
      if (newQuantity <= 0) {
        removeItem(itemId, sourcePalletId);
        return;
      }

      updateCart((prevCart) => {
        const newItems = prevCart.items.map((item) =>
          item.itemId === itemId && item.sourcePalletId === sourcePalletId
            ? { ...item, quantity: newQuantity }
            : item
        );

        const totalItems = newItems.reduce(
          (sum, item) => sum + item.quantity,
          0
        );
        const totalQuantity = totalItems; // For now, totalQuantity equals totalItems

        return {
          ...prevCart,
          items: newItems,
          totalItems,
          totalQuantity,
        };
      });
    },
    [updateCart, removeItem]
  );

  // Update destination
  const updateDestination = useCallback(
    (destination: string, destinationCode?: string) => {
      updateCart((prevCart) => ({
        ...prevCart,
        destination,
        destinationCode: destinationCode || "",
      }));
    },
    [updateCart]
  );

  // Force save cart immediately
  const forceSave = useCallback(() => {
    if (saveTimeout) {
      clearTimeout(saveTimeout);
      setSaveTimeout(null);
    }
    saveCart(cart);
  }, [saveTimeout, saveCart, cart]);

  // Load cart when warehouse or user changes
  useEffect(() => {
    if (currentWarehouse?.id && appUser?.id) {
      loadCart();
    }
  }, [currentWarehouse?.id, appUser?.id, loadCart]);

  // Clear timeout on unmount
  useEffect(() => {
    return () => {
      if (saveTimeout) {
        clearTimeout(saveTimeout);
      }
    };
  }, [saveTimeout]);

  // Auto-clear expired carts on app start
  useEffect(() => {
    if (!enableCrossSession) return;

    const cleanupExpiredCarts = () => {
      try {
        const keys = Object.keys(localStorage);
        const cartKeys = keys.filter((key) =>
          key.includes(STORAGE_KEYS.CART_METADATA)
        );

        cartKeys.forEach((metadataKey) => {
          try {
            const metadata = JSON.parse(
              localStorage.getItem(metadataKey) || ""
            );
            const now = Date.now();

            if (now - metadata.lastUpdated > maxAge) {
              const cartKey = metadataKey.replace(
                STORAGE_KEYS.CART_METADATA,
                STORAGE_KEYS.PICKING_CART
              );
              localStorage.removeItem(cartKey);
              localStorage.removeItem(metadataKey);
            }
          } catch (error) {
            // Remove invalid metadata
            localStorage.removeItem(metadataKey);
          }
        });
      } catch (error) {
        console.warn("Failed to cleanup expired carts:", error);
      }
    };

    cleanupExpiredCarts();
  }, [enableCrossSession, maxAge]);

  return {
    // Cart state
    cart,
    isLoading,
    isEmpty: cart.items.length === 0,
    hasItems: cart.items.length > 0,

    // Cart operations
    addItem,
    removeItem,
    updateItemQuantity,
    updateDestination,
    clearCart,
    forceSave,

    // Utilities
    isItemInCart: useCallback(
      (itemId: string, sourcePalletId: string | undefined) => {
        return cart.items.some(
          (item) =>
            item.itemId === itemId && item.sourcePalletId === sourcePalletId
        );
      },
      [cart.items]
    ),

    getItemQuantity: useCallback(
      (itemId: string, sourcePalletId: string | undefined) => {
        const item = cart.items.find(
          (item) =>
            item.itemId === itemId && item.sourcePalletId === sourcePalletId
        );
        return item?.quantity || 0;
      },
      [cart.items]
    ),
  };
}
