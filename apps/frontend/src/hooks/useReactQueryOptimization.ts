import { useCallback, useEffect, useRef } from "react";
import { useQueryClient, QueryKey } from "@tanstack/react-query";
import { useAuth } from "@/components/providers/auth-provider";
import { useWarehouse } from "@/components/providers/warehouse-provider";

// Cache configuration for different data types
interface CacheConfig {
  staleTime: number;
  gcTime: number; // Garbage collection time (formerly cacheTime)
  refetchOnWindowFocus: boolean;
  refetchOnMount: boolean;
  refetchOnReconnect: boolean;
  retry: number | boolean;
}

// Predefined cache configurations for different data types
const CACHE_CONFIGS: Record<string, CacheConfig> = {
  // Static/semi-static data (locations, items, users)
  static: {
    staleTime: 300000, // 5 minutes
    gcTime: 600000, // 10 minutes
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: true,
    retry: 3,
  },

  // Dynamic data (pallets, shipments, inventory)
  dynamic: {
    staleTime: 60000, // 1 minute
    gcTime: 300000, // 5 minutes
    refetchOnWindowFocus: false,
    refetchOnMount: true,
    refetchOnReconnect: true,
    retry: 2,
  },

  // Real-time data (active operations, status updates)
  realtime: {
    staleTime: 10000, // 10 seconds
    gcTime: 60000, // 1 minute
    refetchOnWindowFocus: true,
    refetchOnMount: true,
    refetchOnReconnect: true,
    retry: 1,
  },

  // Search results (temporary, user-driven)
  search: {
    staleTime: 180000, // 3 minutes
    gcTime: 300000, // 5 minutes
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false,
    retry: 1,
  },

  // Background data (prefetched, low priority)
  background: {
    staleTime: 600000, // 10 minutes
    gcTime: 1800000, // 30 minutes
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false,
    retry: false,
  },
};

// Query key patterns for intelligent invalidation
const QUERY_KEY_PATTERNS = {
  warehouse: (warehouseId: string) => ["warehouse", warehouseId],
  items: (warehouseId: string) => ["items", warehouseId],
  pallets: (warehouseId: string) => ["pallets", warehouseId],
  shipments: (warehouseId: string) => ["shipments", warehouseId],
  locations: (warehouseId: string) => ["locations", warehouseId],
  destinations: (warehouseId: string) => ["destinations", warehouseId],
  search: (warehouseId: string, type: string) => ["search", warehouseId, type],
  user: (userId: string) => ["user", userId],
  tenant: (tenantId: string) => ["tenant", tenantId],
};

interface UseReactQueryOptimizationOptions {
  enableIntelligentInvalidation?: boolean;
  enableBackgroundRefetch?: boolean;
  enableStaleWhileRevalidate?: boolean;
  enableQueryDeduplication?: boolean;
  enablePersistence?: boolean;
}

export function useReactQueryOptimization(
  options: UseReactQueryOptimizationOptions = {}
) {
  const {
    enableIntelligentInvalidation = true,
    enableBackgroundRefetch = true,
    enableStaleWhileRevalidate = true,
    enableQueryDeduplication = true,
    enablePersistence = false,
  } = options;

  const queryClient = useQueryClient();
  const { appUser } = useAuth();
  const { currentWarehouse } = useWarehouse();
  const lastInvalidationRef = useRef<Record<string, number>>({});

  // Get cache configuration for data type
  const getCacheConfig = useCallback(
    (dataType: keyof typeof CACHE_CONFIGS): CacheConfig => {
      return CACHE_CONFIGS[dataType] || CACHE_CONFIGS.dynamic;
    },
    []
  );

  // Intelligent cache invalidation with debouncing
  const invalidateQueries = useCallback(
    (
      queryKey: QueryKey,
      options: {
        exact?: boolean;
        refetchType?: "active" | "inactive" | "all" | "none";
        debounceMs?: number;
      } = {}
    ) => {
      if (!enableIntelligentInvalidation) return;

      const {
        exact = false,
        refetchType = "active",
        debounceMs = 1000,
      } = options;
      const keyString = JSON.stringify(queryKey);
      const now = Date.now();

      // Debounce invalidations to prevent excessive refetching
      if (
        lastInvalidationRef.current[keyString] &&
        now - lastInvalidationRef.current[keyString] < debounceMs
      ) {
        return;
      }

      lastInvalidationRef.current[keyString] = now;

      queryClient.invalidateQueries({
        queryKey,
        exact,
        refetchType,
      });
    },
    [queryClient, enableIntelligentInvalidation]
  );

  // Smart cache invalidation based on data relationships
  const invalidateRelatedQueries = useCallback(
    (
      operation: "create" | "update" | "delete",
      entityType: "item" | "pallet" | "shipment" | "location" | "user",
      entityId?: string
    ) => {
      if (!currentWarehouse?.id) return;

      const warehouseId = currentWarehouse.id;

      switch (entityType) {
        case "item":
          // Invalidate item-related queries
          invalidateQueries(QUERY_KEY_PATTERNS.items(warehouseId));
          invalidateQueries(QUERY_KEY_PATTERNS.search(warehouseId, "items"));
          if (operation !== "create") {
            // Also invalidate pallets that might contain this item
            invalidateQueries(QUERY_KEY_PATTERNS.pallets(warehouseId));
          }
          break;

        case "pallet":
          // Invalidate pallet-related queries
          invalidateQueries(QUERY_KEY_PATTERNS.pallets(warehouseId));
          invalidateQueries(QUERY_KEY_PATTERNS.search(warehouseId, "pallets"));
          // Invalidate items as pallet changes affect item locations
          invalidateQueries(QUERY_KEY_PATTERNS.items(warehouseId));
          break;

        case "shipment":
          // Invalidate shipment-related queries
          invalidateQueries(QUERY_KEY_PATTERNS.shipments(warehouseId));
          invalidateQueries(
            QUERY_KEY_PATTERNS.search(warehouseId, "shipments")
          );
          if (operation === "create" || operation === "delete") {
            // New/deleted shipments might affect inventory
            invalidateQueries(QUERY_KEY_PATTERNS.pallets(warehouseId));
            invalidateQueries(QUERY_KEY_PATTERNS.items(warehouseId));
          }
          break;

        case "location":
          // Invalidate location-related queries
          invalidateQueries(QUERY_KEY_PATTERNS.locations(warehouseId));
          if (operation !== "create") {
            // Location changes might affect pallets and items
            invalidateQueries(QUERY_KEY_PATTERNS.pallets(warehouseId));
            invalidateQueries(QUERY_KEY_PATTERNS.items(warehouseId));
          }
          break;

        case "user":
          // Invalidate user-related queries
          if (appUser?.id) {
            invalidateQueries(QUERY_KEY_PATTERNS.user(appUser.id));
          }
          break;
      }
    },
    [currentWarehouse?.id, appUser?.id, invalidateQueries]
  );

  // Background refetch for stale data
  const enableBackgroundRefetching = useCallback(() => {
    if (!enableBackgroundRefetch || !currentWarehouse?.id) return;

    const warehouseId = currentWarehouse.id;

    // Refetch critical data in background
    const criticalQueries = [
      QUERY_KEY_PATTERNS.pallets(warehouseId),
      QUERY_KEY_PATTERNS.shipments(warehouseId),
      QUERY_KEY_PATTERNS.items(warehouseId),
    ];

    criticalQueries.forEach((queryKey) => {
      queryClient.refetchQueries({
        queryKey,
        type: "active",
      });
    });
  }, [queryClient, enableBackgroundRefetch, currentWarehouse?.id]);

  // Stale-while-revalidate pattern implementation
  const fetchWithStaleWhileRevalidate = useCallback(
    async <T>(
      queryKey: QueryKey,
      queryFn: () => Promise<T>,
      staleTime: number = 60000
    ): Promise<T> => {
      if (!enableStaleWhileRevalidate) {
        return queryFn();
      }

      // Get cached data
      const cachedData = queryClient.getQueryData<T>(queryKey);
      const queryState = queryClient.getQueryState(queryKey);

      // If we have fresh data, return it
      if (
        cachedData &&
        queryState &&
        Date.now() - queryState.dataUpdatedAt < staleTime
      ) {
        return cachedData;
      }

      // If we have stale data, return it and refetch in background
      if (cachedData) {
        // Trigger background refetch
        queryClient.refetchQueries({ queryKey, type: "active" });
        return cachedData;
      }

      // No cached data, fetch normally
      return queryFn();
    },
    [queryClient, enableStaleWhileRevalidate]
  );

  // Query deduplication for identical requests
  const deduplicateQuery = useCallback(
    <T>(queryKey: QueryKey, queryFn: () => Promise<T>): Promise<T> => {
      if (!enableQueryDeduplication) {
        return queryFn();
      }

      // Check if query is already in flight
      const queryState = queryClient.getQueryState(queryKey);
      if (queryState?.fetchStatus === "fetching") {
        // Return the existing promise
        return queryClient.fetchQuery({ queryKey, queryFn });
      }

      return queryFn();
    },
    [queryClient, enableQueryDeduplication]
  );

  // Optimize query defaults for warehouse context
  const getOptimizedQueryOptions = useCallback(
    (dataType: keyof typeof CACHE_CONFIGS, customOptions: any = {}) => {
      const baseConfig = getCacheConfig(dataType);

      return {
        ...baseConfig,
        ...customOptions,
        // Ensure warehouse context is included in query key
        queryKey: customOptions.queryKey?.includes(currentWarehouse?.id)
          ? customOptions.queryKey
          : [...(customOptions.queryKey || []), currentWarehouse?.id],
      };
    },
    [getCacheConfig, currentWarehouse?.id]
  );

  // Cleanup stale queries when switching warehouses
  const cleanupStaleQueries = useCallback(
    (previousWarehouseId?: string) => {
      if (!previousWarehouseId) return;

      // Remove queries for previous warehouse after delay
      setTimeout(() => {
        const staleQueryKeys = [
          QUERY_KEY_PATTERNS.items(previousWarehouseId),
          QUERY_KEY_PATTERNS.pallets(previousWarehouseId),
          QUERY_KEY_PATTERNS.shipments(previousWarehouseId),
          QUERY_KEY_PATTERNS.search(previousWarehouseId, "items"),
          QUERY_KEY_PATTERNS.search(previousWarehouseId, "pallets"),
          QUERY_KEY_PATTERNS.search(previousWarehouseId, "shipments"),
        ];

        staleQueryKeys.forEach((queryKey) => {
          queryClient.removeQueries({ queryKey });
        });
      }, 30000); // 30 seconds delay
    },
    [queryClient]
  );

  // Prefetch related data based on user behavior
  const prefetchRelatedData = useCallback(
    (entityType: "item" | "pallet" | "shipment", entityId: string) => {
      if (!currentWarehouse?.id) return;

      const warehouseId = currentWarehouse.id;

      switch (entityType) {
        case "item":
          // Prefetch item locations
          queryClient.prefetchQuery({
            queryKey: ["item-locations", entityId, warehouseId],
            staleTime: CACHE_CONFIGS.dynamic.staleTime,
          });
          break;

        case "pallet":
          // Prefetch pallet items and location
          queryClient.prefetchQuery({
            queryKey: ["pallet", entityId],
            staleTime: CACHE_CONFIGS.dynamic.staleTime,
          });
          break;

        case "shipment":
          // Prefetch packing list
          queryClient.prefetchQuery({
            queryKey: ["packing-list", entityId],
            staleTime: CACHE_CONFIGS.dynamic.staleTime,
          });
          break;
      }
    },
    [queryClient, currentWarehouse?.id]
  );

  // Setup automatic background refetching
  useEffect(() => {
    if (!enableBackgroundRefetch) return;

    const interval = setInterval(() => {
      enableBackgroundRefetching();
    }, 300000); // Every 5 minutes

    return () => clearInterval(interval);
  }, [enableBackgroundRefetch, enableBackgroundRefetching]);

  // Handle warehouse changes
  useEffect(() => {
    const previousWarehouseId = queryClient.getQueryData<string>([
      "previous-warehouse-id",
    ]);

    if (previousWarehouseId && previousWarehouseId !== currentWarehouse?.id) {
      cleanupStaleQueries(previousWarehouseId);
    }

    if (currentWarehouse?.id) {
      queryClient.setQueryData(["previous-warehouse-id"], currentWarehouse.id);
    }
  }, [currentWarehouse?.id, queryClient, cleanupStaleQueries]);

  return {
    // Cache configuration
    getCacheConfig,
    getOptimizedQueryOptions,

    // Invalidation strategies
    invalidateQueries,
    invalidateRelatedQueries,

    // Performance optimizations
    fetchWithStaleWhileRevalidate,
    deduplicateQuery,
    prefetchRelatedData,

    // Background operations
    enableBackgroundRefetching,
    cleanupStaleQueries,

    // Query patterns
    queryKeyPatterns: QUERY_KEY_PATTERNS,
    cacheConfigs: CACHE_CONFIGS,
  };
}

// Utility function to create optimized query options
export function createOptimizedQueryOptions(
  dataType: keyof typeof CACHE_CONFIGS,
  queryKey: QueryKey,
  customOptions: any = {}
) {
  const baseConfig = CACHE_CONFIGS[dataType] || CACHE_CONFIGS.dynamic;

  return {
    queryKey,
    ...baseConfig,
    ...customOptions,
  };
}

// Pre-configured optimization configurations
export const optimizationConfigs = {
  // High-performance strategy for warehouse floor operations
  warehouseFloor: {
    enableIntelligentInvalidation: true,
    enableBackgroundRefetch: true,
    enableStaleWhileRevalidate: true,
    enableQueryDeduplication: true,
    enablePersistence: false,
  },

  // Conservative strategy for admin interfaces
  admin: {
    enableIntelligentInvalidation: true,
    enableBackgroundRefetch: false,
    enableStaleWhileRevalidate: false,
    enableQueryDeduplication: true,
    enablePersistence: true,
  },

  // Minimal strategy for low-bandwidth environments
  minimal: {
    enableIntelligentInvalidation: false,
    enableBackgroundRefetch: false,
    enableStaleWhileRevalidate: false,
    enableQueryDeduplication: true,
    enablePersistence: false,
  },
};

// Custom hooks for pre-configured optimization strategies
export function useWarehouseFloorOptimization() {
  return useReactQueryOptimization(optimizationConfigs.warehouseFloor);
}

export function useAdminOptimization() {
  return useReactQueryOptimization(optimizationConfigs.admin);
}

export function useMinimalOptimization() {
  return useReactQueryOptimization(optimizationConfigs.minimal);
}
