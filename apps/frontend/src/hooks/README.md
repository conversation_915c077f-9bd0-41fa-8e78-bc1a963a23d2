# Quildora Frontend Hooks Documentation

This directory contains custom React hooks that provide reusable business logic and state management for the Quildora inventory management system.

## Shipping & Outbound Operations Hooks

### useShipmentSearch

Advanced shipment search and filtering hook with debounced inputs and pagination.

**Features:**
- Debounced text search (300ms) for destination and shipment number
- Status-based filtering with quick presets
- Date range filtering
- Sorting and pagination
- Warehouse-scoped access control
- Real-time data with useSuspenseQuery

**Usage:**
```typescript
import { useShipmentSearch } from "@/hooks/useShipmentSearch";

function ShipmentList() {
  const {
    shipments,
    pagination,
    updateStatus,
    updateDestination,
    applyQuickFilter,
    clearFilters,
    hasActiveFilters,
    currentPage,
    totalPages,
    nextPage,
    prevPage,
  } = useShipmentSearch({
    pageSize: 20,
    defaultStatus: ShipmentStatus.PREPARING,
    enableAutoRefresh: true,
  });

  return (
    <div>
      {/* Filter controls */}
      <button onClick={() => applyQuickFilter("pending")}>
        Show Pending
      </button>
      <button onClick={() => applyQuickFilter("today")}>
        Today's Shipments
      </button>
      
      {/* Shipment list */}
      {shipments.map(shipment => (
        <div key={shipment.id}>{shipment.shipmentNumber}</div>
      ))}
      
      {/* Pagination */}
      <button onClick={prevPage} disabled={!canGoPrev}>
        Previous
      </button>
      <span>Page {currentPage + 1} of {totalPages}</span>
      <button onClick={nextPage} disabled={!canGoNext}>
        Next
      </button>
    </div>
  );
}
```

**Quick Filter Presets:**
- `"today"` - Shipments created today
- `"week"` - Shipments from the last 7 days
- `"pending"` - Shipments with PREPARING status
- `"shipped"` - Shipments with SHIPPED status

### usePackingListOperations

Comprehensive packing list management with PDF generation and print functionality.

**Features:**
- Fetch packing list data with auto-refresh
- Generate and download PDF packing lists
- Print functionality with popup blocker workaround
- Update packing list notes
- Download text version
- Warehouse-scoped operations

**Usage:**
```typescript
import { usePackingListOperations } from "@/hooks/usePackingListOperations";

function PackingListView({ shipmentId }: { shipmentId: string }) {
  const {
    packingList,
    handlePrint,
    downloadPdf,
    downloadText,
    updateNotes,
    refresh,
    isPrintingPdf,
    printStatus,
    canPrint,
    hasItems,
  } = usePackingListOperations({
    shipmentId,
    enableAutoRefresh: true,
    refreshInterval: 60000, // 1 minute
  });

  return (
    <div>
      <h2>Packing List - {packingList?.shipmentNumber}</h2>
      
      {/* Action buttons */}
      <button 
        onClick={handlePrint} 
        disabled={!canPrint}
      >
        {printStatus === "printing" ? "Printing..." : "Print"}
      </button>
      
      <button 
        onClick={downloadPdf} 
        disabled={isPrintingPdf}
      >
        {isPrintingPdf ? "Generating..." : "Download PDF"}
      </button>
      
      <button onClick={downloadText}>
        Download Text
      </button>
      
      <button onClick={refresh}>
        Refresh
      </button>
      
      {/* Packing list content */}
      {hasItems && (
        <div>
          <h3>Items ({packingList?.summary.totalItems})</h3>
          {packingList?.items.map(item => (
            <div key={item.id}>
              {item.quantity}x {item.name}
            </div>
          ))}
        </div>
      )}
      
      {/* Notes section */}
      <textarea
        value={packingList?.notes || ""}
        onChange={(e) => updateNotes(e.target.value)}
        placeholder="Add packing notes..."
      />
    </div>
  );
}
```

**Print Status Values:**
- `"idle"` - Ready to print
- `"preparing"` - Preparing print content
- `"printing"` - Print dialog open
- `"success"` - Print completed successfully
- `"error"` - Print failed

### useInventoryRelease

Advanced inventory release operations with optimistic updates and batch processing.

**Features:**
- Single and batch inventory release
- Optimistic UI updates
- Comprehensive error handling
- Warehouse context invalidation
- Success animations and confirmations
- Audit trail integration

**Usage:**
```typescript
import { useInventoryRelease } from "@/hooks/useInventoryRelease";

function ShipmentReleaseButton({ shipmentId }: { shipmentId: string }) {
  const {
    releaseInventory,
    quickRelease,
    batchRelease,
    isReleasing,
    showConfirmation,
    lastReleaseResponse,
    error,
    clearError,
    dismissConfirmation,
    canRelease,
  } = useInventoryRelease({
    showSuccessAnimation: true,
    enableOptimisticUpdates: true,
    onSuccess: (response) => {
      console.log(`Released ${response.inventoryUpdates.itemsReleased} items`);
    },
  });

  const handleRelease = () => {
    releaseInventory(shipmentId, {
      trackingNumber: "TRACK123",
      releaseNotes: "Standard release",
      carrierInfo: {
        name: "FedEx",
        service: "Ground",
      },
    });
  };

  const handleQuickRelease = () => {
    quickRelease(shipmentId, "QUICK456");
  };

  return (
    <div>
      <button 
        onClick={handleRelease} 
        disabled={!canRelease}
      >
        {isReleasing ? "Releasing..." : "Release Inventory"}
      </button>
      
      <button 
        onClick={handleQuickRelease} 
        disabled={!canRelease}
      >
        Quick Release
      </button>
      
      {error && (
        <div className="error">
          {error}
          <button onClick={clearError}>Dismiss</button>
        </div>
      )}
      
      {showConfirmation && lastReleaseResponse && (
        <div className="success-animation">
          🎉 Successfully released {lastReleaseResponse.inventoryUpdates.itemsReleased} items!
          <button onClick={dismissConfirmation}>Close</button>
        </div>
      )}
    </div>
  );
}
```

**Batch Release Example:**
```typescript
const handleBatchRelease = async () => {
  const shipmentIds = ["ship1", "ship2", "ship3"];
  const results = await batchRelease(shipmentIds, {
    releaseNotes: "Batch release for daily shipments",
    carrierInfo: { name: "UPS", service: "Ground" },
  });
  
  // Results contain success/failure status for each shipment
  results.forEach(result => {
    if (result.success) {
      console.log(`Shipment ${result.shipmentId} released successfully`);
    } else {
      console.error(`Shipment ${result.shipmentId} failed: ${result.error}`);
    }
  });
};
```

## Hook Integration Patterns

### Warehouse Context Integration

All shipping hooks automatically integrate with the warehouse provider:

```typescript
import { useWarehouse } from "@/components/providers/warehouse-provider";

// Hooks automatically use current warehouse context
const { currentWarehouse } = useWarehouse();
const { shipments } = useShipmentSearch(); // Scoped to currentWarehouse
```

### Error Handling Patterns

Consistent error handling across all hooks:

```typescript
const {
  data,
  error,
  isLoading,
  retry,
} = useShipmentSearch();

// Handle errors in UI
if (error) {
  return <ErrorBoundary error={error} onRetry={retry} />;
}
```

### Performance Optimizations

All hooks implement performance best practices:

- **useSuspenseQuery**: Eliminates loading spinners
- **Debounced inputs**: Reduces API calls (300ms delay)
- **Smart caching**: 5-minute stale time for static data
- **Optimistic updates**: Immediate UI feedback
- **Warehouse-scoped invalidation**: Targeted cache updates

### Mobile/Tablet Optimization

Hooks support warehouse floor operations:

- Large touch targets in UI components
- Offline-capable caching strategies
- Quick action patterns (quick release, quick filters)
- Simplified error messages for warehouse staff

## Testing Recommendations

### Unit Testing

Test hook logic in isolation:

```typescript
import { renderHook } from "@testing-library/react";
import { useShipmentSearch } from "@/hooks/useShipmentSearch";

test("should filter shipments by status", () => {
  const { result } = renderHook(() => useShipmentSearch());
  
  act(() => {
    result.current.updateStatus(ShipmentStatus.SHIPPED);
  });
  
  expect(result.current.filters.status).toBe(ShipmentStatus.SHIPPED);
});
```

### Integration Testing

Test hooks with React Query and warehouse context:

```typescript
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { WarehouseProvider } from "@/components/providers/warehouse-provider";

const TestWrapper = ({ children }) => (
  <QueryClientProvider client={new QueryClient()}>
    <WarehouseProvider>
      {children}
    </WarehouseProvider>
  </QueryClientProvider>
);

test("should fetch shipments for current warehouse", async () => {
  const { result } = renderHook(() => useShipmentSearch(), {
    wrapper: TestWrapper,
  });
  
  await waitFor(() => {
    expect(result.current.shipments).toHaveLength(5);
  });
});
```

## Migration Guide

When upgrading from older hook patterns:

1. **Replace useQuery with useSuspenseQuery** for better UX
2. **Add warehouse context** to all data fetching
3. **Implement debounced search** for text inputs
4. **Add optimistic updates** for better perceived performance
5. **Use batch operations** for multiple item actions

## Contributing

When adding new shipping/outbound hooks:

1. Follow the established patterns in existing hooks
2. Include warehouse-scoped access control
3. Implement proper error handling and loading states
4. Add comprehensive TypeScript types
5. Include usage examples in documentation
6. Write unit and integration tests
7. Consider mobile/tablet warehouse workflows
