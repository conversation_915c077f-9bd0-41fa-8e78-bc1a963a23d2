import { useState, useCallback, useMemo } from "react";
import { UseFormReturn } from "react-hook-form";
import { toast } from "sonner";
import type { DestinationResponse } from "@quildora/types";
import {
  useDestinations,
  useDestinationsWithCodes,
  useDestinationsByName,
} from "./api/useWarehouseData";

interface UseDestinationManagementOptions {
  form?: UseFormReturn<any>;
  supportCodes?: boolean;
  enableAutoPopulation?: boolean;
  enableValidation?: boolean;
  onSuccess?: (message: string) => void;
  onError?: (error: string) => void;
}

interface UseDestinationManagementReturn {
  // Data
  destinations: string[];
  destinationsWithCodes: DestinationResponse[];
  isLoading: boolean;

  // Autocomplete functionality
  suggestions: DestinationResponse[];
  query: string;
  showSuggestions: boolean;
  selectedIndex: number;

  // Auto-population functionality
  isLookingUp: boolean;

  // Actions
  handleInputChange: (value: string) => void;
  handleDestinationSelect: (destination: DestinationResponse | null) => void;
  handleDestinationCodeLookup: (code: string) => Promise<void>;
  handleKeyNavigation: (key: string) => void;
  selectSuggestion: (index: number) => void;
  clearSuggestions: () => void;

  // Validation
  validateDestinationCode: (code: string) => boolean;
  formatDestinationDisplay: (destination: DestinationResponse) => string;

  // Search and filtering
  searchDestinations: (
    searchTerm: string,
    searchType?: "name" | "code"
  ) => DestinationResponse[];
  organizeDestinations: (priorityDestinations?: string[]) => {
    priority: DestinationResponse[];
    others: DestinationResponse[];
  };
}

export function useDestinationManagement(
  options: UseDestinationManagementOptions = {}
): UseDestinationManagementReturn {
  const {
    form,
    supportCodes = false,
    enableAutoPopulation = true,
    enableValidation = true,
    onSuccess,
    onError,
  } = options;

  // State for autocomplete functionality
  const [query, setQuery] = useState("");
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const [isLookingUp, setIsLookingUp] = useState(false);

  // Fetch destination data using warehouse-aware hooks
  const { data: destinations = [], isLoading: isLoadingDestinations } =
    useDestinations();
  const { data: destinationsWithCodes = [], isLoading: isLoadingWithCodes } =
    useDestinationsWithCodes();

  const isLoading = isLoadingDestinations || isLoadingWithCodes;

  // Generate autocomplete suggestions based on query
  const suggestions = useMemo(() => {
    if (!query.trim() || query.length < 2) {
      return [];
    }

    const searchTerm = query.toLowerCase();
    const dataSource = supportCodes
      ? destinationsWithCodes
      : destinations.map((name) => ({
          name,
          code: null,
          displayName: name,
        }));

    return dataSource
      .filter(
        (dest) =>
          dest.name.toLowerCase().includes(searchTerm) ||
          (dest.code && dest.code.toLowerCase().includes(searchTerm))
      )
      .slice(0, 10); // Limit to 10 suggestions for performance
  }, [query, destinations, destinationsWithCodes, supportCodes]);

  // Handle input change with autocomplete logic
  const handleInputChange = useCallback(
    (value: string) => {
      setQuery(value);
      setShowSuggestions(value.length >= 2);
      setSelectedIndex(-1);

      // Auto-select if exact match found
      if (enableAutoPopulation && value.length >= 2) {
        const dataSource = supportCodes
          ? destinationsWithCodes
          : destinations.map((name) => ({
              name,
              code: null,
              displayName: name,
            }));

        const exactMatch = dataSource.find(
          (dest) =>
            dest.name.toLowerCase() === value.toLowerCase() ||
            (dest.code && dest.code.toLowerCase() === value.toLowerCase())
        );

        if (exactMatch && form) {
          handleDestinationSelect(exactMatch);
        }
      }
    },
    [
      destinations,
      destinationsWithCodes,
      supportCodes,
      enableAutoPopulation,
      form,
    ]
  );

  // Handle destination selection from autocomplete (Name → Code workflow)
  const handleDestinationSelect = useCallback(
    (destination: DestinationResponse | null) => {
      if (!destination) return;

      if (form && enableAutoPopulation && destination.code) {
        form.setValue("destinationCode", destination.code);
        const message = `Auto-filled destination code: ${destination.code}`;

        toast.success(message, {
          description: `Code for ${destination.name}`,
          duration: 2000,
        });

        if (onSuccess) {
          onSuccess(message);
        }
      }

      // Clear autocomplete state
      setShowSuggestions(false);
      setSelectedIndex(-1);
    },
    [form, enableAutoPopulation, onSuccess]
  );

  // Handle destination code lookup (Code → Name workflow)
  const handleDestinationCodeLookup = useCallback(
    async (code: string): Promise<void> => {
      if (!code.trim() || !enableAutoPopulation || !form) return;

      // Validate code format (numbers only)
      if (enableValidation && !/^\d+$/.test(code.trim())) {
        return; // Let form validation handle the error message
      }

      setIsLookingUp(true);

      try {
        // Search for destination by code in the current destinations
        const matchingDestination = destinationsWithCodes.find(
          (dest) => dest.code === code.trim()
        );

        if (matchingDestination) {
          // Auto-populate the destination name
          form.setValue("shipToDestination", matchingDestination.name);

          const message = `Auto-filled destination: ${matchingDestination.name}`;

          toast.success(message, {
            description: `Found destination for code ${code}`,
            duration: 2000,
          });

          if (onSuccess) {
            onSuccess(message);
          }
        }
        // Note: No error for code not found - user might be creating new destination
      } catch (error) {
        console.error("Error looking up destination by code:", error);
        const errorMessage = "Failed to lookup destination";

        toast.error(errorMessage, {
          description: "Please try again or enter manually",
          duration: 3000,
        });

        if (onError) {
          onError(errorMessage);
        }
      } finally {
        setIsLookingUp(false);
      }
    },
    [
      destinationsWithCodes,
      enableAutoPopulation,
      enableValidation,
      form,
      onSuccess,
      onError,
    ]
  );

  // Handle keyboard navigation for autocomplete
  const handleKeyNavigation = useCallback(
    (key: string) => {
      if (!showSuggestions || suggestions.length === 0) return;

      switch (key) {
        case "ArrowDown":
          setSelectedIndex((prev) =>
            prev < suggestions.length - 1 ? prev + 1 : 0
          );
          break;
        case "ArrowUp":
          setSelectedIndex((prev) =>
            prev > 0 ? prev - 1 : suggestions.length - 1
          );
          break;
        case "Enter":
          if (selectedIndex >= 0 && selectedIndex < suggestions.length) {
            const selected = suggestions[selectedIndex];
            setQuery(selected.name);
            handleDestinationSelect(selected);
          }
          break;
        case "Escape":
          setShowSuggestions(false);
          setSelectedIndex(-1);
          break;
      }
    },
    [showSuggestions, suggestions, selectedIndex, handleDestinationSelect]
  );

  // Select suggestion by index
  const selectSuggestion = useCallback(
    (index: number) => {
      if (index >= 0 && index < suggestions.length) {
        const selected = suggestions[index];
        setQuery(selected.name);
        handleDestinationSelect(selected);
      }
    },
    [suggestions, handleDestinationSelect]
  );

  // Clear suggestions
  const clearSuggestions = useCallback(() => {
    setShowSuggestions(false);
    setSelectedIndex(-1);
  }, []);

  // Validation utilities
  const validateDestinationCode = useCallback(
    (code: string): boolean => {
      if (!enableValidation) return true;
      if (!code.trim()) return true; // Empty is valid (optional field)
      return /^\d+$/.test(code.trim());
    },
    [enableValidation]
  );

  // Format destination display name
  const formatDestinationDisplay = useCallback(
    (destination: DestinationResponse): string => {
      if (destination.code) {
        return `${destination.name} (${destination.code})`;
      }
      return destination.name;
    },
    []
  );

  // Search destinations with flexible criteria
  const searchDestinations = useCallback(
    (
      searchTerm: string,
      searchType: "name" | "code" = "name"
    ): DestinationResponse[] => {
      if (!searchTerm.trim()) return [];

      const term = searchTerm.toLowerCase();
      const dataSource = supportCodes
        ? destinationsWithCodes
        : destinations.map((name) => ({
            name,
            code: null,
            displayName: name,
          }));

      return dataSource.filter((dest) => {
        if (searchType === "name") {
          return dest.name.toLowerCase().includes(term);
        } else if (searchType === "code") {
          return dest.code && dest.code.toLowerCase().includes(term);
        }
        // Default: search both name and code
        return (
          dest.name.toLowerCase().includes(term) ||
          (dest.code && dest.code.toLowerCase().includes(term))
        );
      });
    },
    [destinations, destinationsWithCodes, supportCodes]
  );

  // Organize destinations with priority ordering
  const organizeDestinations = useCallback(
    (priorityDestinations: string[] = []) => {
      const dataSource = supportCodes
        ? destinationsWithCodes
        : destinations.map((name) => ({
            name,
            code: null,
            displayName: name,
          }));

      const priority = dataSource
        .filter((dest) => priorityDestinations.includes(dest.name))
        .sort((a, b) => a.name.localeCompare(b.name));

      const others = dataSource
        .filter((dest) => !priorityDestinations.includes(dest.name))
        .sort((a, b) => a.name.localeCompare(b.name));

      return { priority, others };
    },
    [destinations, destinationsWithCodes, supportCodes]
  );

  return {
    // Data
    destinations,
    destinationsWithCodes,
    isLoading,

    // Autocomplete state
    suggestions,
    query,
    showSuggestions,
    selectedIndex,

    // Auto-population state
    isLookingUp,

    // Actions
    handleInputChange,
    handleDestinationSelect,
    handleDestinationCodeLookup,
    handleKeyNavigation,
    selectSuggestion,
    clearSuggestions,

    // Validation utilities
    validateDestinationCode,
    formatDestinationDisplay,

    // Search and filtering
    searchDestinations,
    organizeDestinations,
  };
}

// Utility function to create destination code input handler with debouncing
export function createDestinationCodeHandler(
  handleDestinationCodeLookup: (code: string) => Promise<void>,
  debounceMs: number = 500
) {
  let timeoutId: NodeJS.Timeout;

  return (code: string) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => {
      handleDestinationCodeLookup(code);
    }, debounceMs);
  };
}

// Re-export utility functions for backward compatibility
export {
  formatDestinationDisplay,
  isValidDestinationCode,
} from "./useDestinationAutoPopulation";
