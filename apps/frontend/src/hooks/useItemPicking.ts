import { useState, useCallback, useEffect } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { fetchWithAuth } from "@/lib/api";
import { useAuth } from "@/components/providers/auth-provider";
import { useWarehouse } from "@/components/providers/warehouse-provider";
import type {
  PickedItem,
  ItemPickingCart,
  CreateShipmentDto,
  OutgoingShipment,
  ItemSearchResult,
  ItemLocationResult,
} from "@quildora/types";

// API function for creating shipments
const createShipment = async (
  shipmentData: CreateShipmentDto,
  token: string | null
): Promise<OutgoingShipment> => {
  return fetchWithAuth("/api/shipments", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(shipmentData),
    token,
  });
};

// Local storage key for cart persistence
const CART_STORAGE_KEY = "quildora-item-picking-cart";

export function useItemPicking() {
  const { appToken } = useAuth();
  const { currentWarehouse } = useWarehouse();
  const queryClient = useQueryClient();

  // Initialize cart from localStorage or default state
  const [cart, setCart] = useState<ItemPickingCart>(() => {
    if (typeof window === "undefined") {
      return {
        items: [],
        totalItems: 0,
        totalQuantity: 0,
        destination: undefined,
        destinationCode: undefined,
      };
    }

    try {
      const savedCart = localStorage.getItem(CART_STORAGE_KEY);
      if (savedCart) {
        const parsedCart = JSON.parse(savedCart);
        // Validate cart structure
        if (parsedCart.items && Array.isArray(parsedCart.items)) {
          return parsedCart;
        }
      }
    } catch (error) {
      console.warn("Failed to load cart from localStorage:", error);
    }

    return {
      items: [],
      totalItems: 0,
      totalQuantity: 0,
      destination: undefined,
      destinationCode: undefined,
    };
  });

  // Persist cart to localStorage whenever it changes
  useEffect(() => {
    if (typeof window !== "undefined") {
      try {
        localStorage.setItem(CART_STORAGE_KEY, JSON.stringify(cart));
      } catch (error) {
        console.warn("Failed to save cart to localStorage:", error);
      }
    }
  }, [cart]);

  // Clear cart when warehouse changes (warehouse-scoped access control)
  useEffect(() => {
    // Store the current warehouse ID to detect changes
    const currentWarehouseId = currentWarehouse?.id;

    if (currentWarehouseId && cart.items.length > 0) {
      // Since items are warehouse-scoped, clear cart on warehouse change
      // This ensures data integrity and prevents cross-warehouse operations
      const savedWarehouseId = localStorage.getItem(
        "quildora-current-warehouse-id"
      );

      if (savedWarehouseId && savedWarehouseId !== currentWarehouseId) {
        setCart({
          items: [],
          totalItems: 0,
          totalQuantity: 0,
          destination: undefined,
          destinationCode: undefined,
        });
        toast.info("Cart cleared due to warehouse change");
      }

      // Update stored warehouse ID
      localStorage.setItem("quildora-current-warehouse-id", currentWarehouseId);
    }
  }, [currentWarehouse?.id, cart.items.length]);

  // Create shipment mutation with enhanced error handling
  const createShipmentMutation = useMutation({
    mutationFn: (shipmentData: CreateShipmentDto) =>
      createShipment(shipmentData, appToken),
    onSuccess: (shipment) => {
      // Show success toast
      toast.success(
        `Shipment ${shipment.shipmentNumber} created successfully`,
        {
          description: `${cart.totalItems} items ready for packing`,
        }
      );

      // Invalidate relevant queries with warehouse context
      const warehouseId = currentWarehouse?.id;
      if (warehouseId) {
        queryClient.invalidateQueries({
          queryKey: ["shipments", warehouseId],
        });
        queryClient.invalidateQueries({
          queryKey: ["item-locations", warehouseId],
        });
        queryClient.invalidateQueries({
          queryKey: ["pallets", warehouseId],
        });
      }
    },
    onError: (error) => {
      // Enhanced error handling with user-friendly messages
      const errorMessage =
        error instanceof Error ? error.message : "Failed to create shipment";

      if (errorMessage.includes("insufficient inventory")) {
        toast.error("Insufficient inventory", {
          description: "Some items don't have enough stock available",
        });
      } else if (errorMessage.includes("warehouse")) {
        toast.error("Warehouse access error", {
          description: "Please check your warehouse permissions",
        });
      } else {
        toast.error("Failed to create shipment", {
          description: errorMessage,
        });
      }
    },
  });

  // Helper function to update cart totals
  const updateCartTotals = useCallback(
    (items: PickedItem[]): ItemPickingCart => {
      const totalItems = items.length;
      const totalQuantity = items.reduce((sum, item) => sum + item.quantity, 0);

      return {
        items,
        totalItems,
        totalQuantity,
        destination: cart.destination,
        destinationCode: cart.destinationCode,
      };
    },
    [cart.destination, cart.destinationCode]
  );

  // Add item to cart with validation and user feedback
  const addItemToCart = useCallback(
    (item: PickedItem) => {
      // Validation checks
      if (!item.itemId || !item.itemName || item.quantity <= 0) {
        toast.error("Invalid item data", {
          description: "Please check the item details and try again",
        });
        return;
      }

      if (!currentWarehouse) {
        toast.error("No warehouse selected", {
          description: "Please select a warehouse before adding items",
        });
        return;
      }

      setCart((prevCart) => {
        // Check if item from same pallet already exists
        const existingIndex = prevCart.items.findIndex(
          (cartItem) =>
            cartItem.itemId === item.itemId &&
            cartItem.sourcePalletId === item.sourcePalletId
        );

        let newItems: PickedItem[];

        if (existingIndex >= 0) {
          // Update existing item quantity
          newItems = [...prevCart.items];
          const oldQuantity = newItems[existingIndex].quantity;
          newItems[existingIndex] = {
            ...newItems[existingIndex],
            quantity: oldQuantity + item.quantity,
          };

          toast.success(`Updated ${item.itemName}`, {
            description: `Quantity: ${oldQuantity} → ${
              oldQuantity + item.quantity
            }`,
          });
        } else {
          // Add new item
          newItems = [...prevCart.items, item];

          toast.success(`Added ${item.itemName}`, {
            description: `${item.quantity} ${item.unitOfMeasure} from ${
              item.locationName || "location"
            }`,
          });
        }

        return updateCartTotals(newItems);
      });
    },
    [updateCartTotals, currentWarehouse]
  );

  // Remove item from cart
  const removeItemFromCart = useCallback(
    (itemId: string, sourcePalletId?: string) => {
      setCart((prevCart) => {
        const newItems = prevCart.items.filter(
          (item) =>
            !(item.itemId === itemId && item.sourcePalletId === sourcePalletId)
        );
        return updateCartTotals(newItems);
      });
    },
    [updateCartTotals]
  );

  // Update item quantity in cart
  const updateItemQuantity = useCallback(
    (
      itemId: string,
      sourcePalletId: string | undefined,
      newQuantity: number
    ) => {
      if (newQuantity <= 0) {
        removeItemFromCart(itemId, sourcePalletId);
        return;
      }

      setCart((prevCart) => {
        const newItems = prevCart.items.map((item) => {
          if (
            item.itemId === itemId &&
            item.sourcePalletId === sourcePalletId
          ) {
            return { ...item, quantity: newQuantity };
          }
          return item;
        });
        return updateCartTotals(newItems);
      });
    },
    [removeItemFromCart, updateCartTotals]
  );

  // Clear cart with user confirmation for non-empty carts
  const clearCart = useCallback(
    (silent = false) => {
      const wasEmpty = cart.items.length === 0;

      setCart({
        items: [],
        totalItems: 0,
        totalQuantity: 0,
        destination: undefined,
        destinationCode: undefined,
      });

      if (!silent && !wasEmpty) {
        toast.success("Cart cleared", {
          description: "All items have been removed from your cart",
        });
      }
    },
    [cart.items.length]
  );

  // Helper function to create PickedItem from search result and location
  const createPickedItemFromLocation = useCallback(
    (
      item: ItemSearchResult,
      quantity: number,
      location: ItemLocationResult
    ): PickedItem => {
      return {
        itemId: item.id,
        itemName: item.name,
        sku: item.sku,
        unitOfMeasure: item.unitOfMeasure,
        quantity,
        sourcePalletId: location.palletId,
        sourcePalletBarcode: location.palletBarcode || undefined,
        locationName: location.location.name,
      };
    },
    []
  );

  // Set destination
  const setDestination = useCallback(
    (destination?: string, destinationCode?: string) => {
      setCart((prevCart) => ({
        ...prevCart,
        destination,
        destinationCode,
      }));
    },
    []
  );

  // Create shipment from cart
  const createShipmentFromCart = useCallback(
    async (
      destination: string,
      destinationCode?: string
    ): Promise<OutgoingShipment> => {
      if (cart.items.length === 0) {
        throw new Error("Cart is empty");
      }

      // Convert cart items to shipment items
      const shipmentItems = cart.items.map((item) => ({
        itemId: item.itemId,
        quantity: item.quantity,
        sourcePalletId: item.sourcePalletId,
      }));

      const shipmentData: CreateShipmentDto = {
        destination,
        destinationCode,
        items: shipmentItems,
        pallets: [], // For now, we're only handling item-based picking
      };

      return createShipmentMutation.mutateAsync(shipmentData);
    },
    [cart.items, createShipmentMutation]
  );

  // Get cart item count for a specific item
  const getCartItemQuantity = useCallback(
    (itemId: string, sourcePalletId?: string): number => {
      const cartItem = cart.items.find(
        (item) =>
          item.itemId === itemId && item.sourcePalletId === sourcePalletId
      );
      return cartItem?.quantity || 0;
    },
    [cart.items]
  );

  // Check if cart has items
  const hasItems = cart.totalItems > 0;

  // Check if cart has specific item
  const hasItem = useCallback(
    (itemId: string, sourcePalletId?: string): boolean => {
      return cart.items.some(
        (item) =>
          item.itemId === itemId && item.sourcePalletId === sourcePalletId
      );
    },
    [cart.items]
  );

  return {
    // Cart state
    cart,
    hasItems,

    // Cart operations
    addItemToCart,
    removeItemFromCart,
    updateItemQuantity,
    clearCart,
    setDestination,

    // Utility functions
    getCartItemQuantity,
    hasItem,
    createPickedItemFromLocation,

    // Shipment operations
    createShipmentFromCart,
    isCreatingShipment: createShipmentMutation.isPending,
    shipmentError: createShipmentMutation.error,
  };
}
