import { useState, useCallback } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { fetchWithAuth } from "@/lib/api";
import { useAuth } from "@/components/providers/auth-provider";
import { useWarehouse } from "@/components/providers/warehouse-provider";
import type { 
  PickedItem, 
  ItemPickingCart, 
  CreateShipmentDto,
  OutgoingShipment 
} from "@quildora/types";

// API function for creating shipments
const createShipment = async (
  shipmentData: CreateShipmentDto,
  token: string | null
): Promise<OutgoingShipment> => {
  return fetchWithAuth("/api/shipments", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(shipmentData),
    token,
  });
};

export function useItemPicking() {
  const { appToken } = useAuth();
  const { currentWarehouse } = useWarehouse();
  const queryClient = useQueryClient();

  // Cart state
  const [cart, setCart] = useState<ItemPickingCart>({
    items: [],
    totalItems: 0,
    totalQuantity: 0,
    destination: undefined,
    destinationCode: undefined,
  });

  // Create shipment mutation
  const createShipmentMutation = useMutation({
    mutationFn: (shipmentData: CreateShipmentDto) => 
      createShipment(shipmentData, appToken),
    onSuccess: () => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ["shipments"] });
      queryClient.invalidateQueries({ queryKey: ["item-locations"] });
      queryClient.invalidateQueries({ queryKey: ["pallets"] });
    },
  });

  // Helper function to update cart totals
  const updateCartTotals = useCallback((items: PickedItem[]): ItemPickingCart => {
    const totalItems = items.length;
    const totalQuantity = items.reduce((sum, item) => sum + item.quantity, 0);
    
    return {
      items,
      totalItems,
      totalQuantity,
      destination: cart.destination,
      destinationCode: cart.destinationCode,
    };
  }, [cart.destination, cart.destinationCode]);

  // Add item to cart
  const addItemToCart = useCallback((item: PickedItem) => {
    setCart(prevCart => {
      // Check if item from same pallet already exists
      const existingIndex = prevCart.items.findIndex(
        cartItem => 
          cartItem.itemId === item.itemId && 
          cartItem.sourcePalletId === item.sourcePalletId
      );

      let newItems: PickedItem[];
      
      if (existingIndex >= 0) {
        // Update existing item quantity
        newItems = [...prevCart.items];
        newItems[existingIndex] = {
          ...newItems[existingIndex],
          quantity: newItems[existingIndex].quantity + item.quantity,
        };
      } else {
        // Add new item
        newItems = [...prevCart.items, item];
      }

      return updateCartTotals(newItems);
    });
  }, [updateCartTotals]);

  // Remove item from cart
  const removeItemFromCart = useCallback((itemId: string, sourcePalletId?: string) => {
    setCart(prevCart => {
      const newItems = prevCart.items.filter(
        item => !(item.itemId === itemId && item.sourcePalletId === sourcePalletId)
      );
      return updateCartTotals(newItems);
    });
  }, [updateCartTotals]);

  // Update item quantity in cart
  const updateItemQuantity = useCallback((
    itemId: string, 
    sourcePalletId: string | undefined, 
    newQuantity: number
  ) => {
    if (newQuantity <= 0) {
      removeItemFromCart(itemId, sourcePalletId);
      return;
    }

    setCart(prevCart => {
      const newItems = prevCart.items.map(item => {
        if (item.itemId === itemId && item.sourcePalletId === sourcePalletId) {
          return { ...item, quantity: newQuantity };
        }
        return item;
      });
      return updateCartTotals(newItems);
    });
  }, [removeItemFromCart, updateCartTotals]);

  // Clear cart
  const clearCart = useCallback(() => {
    setCart({
      items: [],
      totalItems: 0,
      totalQuantity: 0,
      destination: undefined,
      destinationCode: undefined,
    });
  }, []);

  // Set destination
  const setDestination = useCallback((destination?: string, destinationCode?: string) => {
    setCart(prevCart => ({
      ...prevCart,
      destination,
      destinationCode,
    }));
  }, []);

  // Create shipment from cart
  const createShipmentFromCart = useCallback(async (
    destination: string,
    destinationCode?: string
  ): Promise<OutgoingShipment> => {
    if (cart.items.length === 0) {
      throw new Error("Cart is empty");
    }

    // Convert cart items to shipment items
    const shipmentItems = cart.items.map(item => ({
      itemId: item.itemId,
      quantity: item.quantity,
      sourcePalletId: item.sourcePalletId,
    }));

    const shipmentData: CreateShipmentDto = {
      destination,
      destinationCode,
      items: shipmentItems,
      pallets: [], // For now, we're only handling item-based picking
    };

    return createShipmentMutation.mutateAsync(shipmentData);
  }, [cart.items, createShipmentMutation]);

  // Get cart item count for a specific item
  const getCartItemQuantity = useCallback((itemId: string, sourcePalletId?: string): number => {
    const cartItem = cart.items.find(
      item => item.itemId === itemId && item.sourcePalletId === sourcePalletId
    );
    return cartItem?.quantity || 0;
  }, [cart.items]);

  // Check if cart has items
  const hasItems = cart.totalItems > 0;

  // Check if cart has specific item
  const hasItem = useCallback((itemId: string, sourcePalletId?: string): boolean => {
    return cart.items.some(
      item => item.itemId === itemId && item.sourcePalletId === sourcePalletId
    );
  }, [cart.items]);

  return {
    // Cart state
    cart,
    hasItems,
    
    // Cart operations
    addItemToCart,
    removeItemFromCart,
    updateItemQuantity,
    clearCart,
    setDestination,
    
    // Utility functions
    getCartItemQuantity,
    hasItem,
    
    // Shipment operations
    createShipmentFromCart,
    isCreatingShipment: createShipmentMutation.isPending,
    shipmentError: createShipmentMutation.error,
  };
}
