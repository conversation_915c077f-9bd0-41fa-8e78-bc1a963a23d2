"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import { useWarehouse } from "@/components/providers/warehouse-provider";
import {
  LayoutDashboard,
  Box, // Changed from Pallet (which doesn't exist) to Box
  Package,
  ScanLine,
  Settings,
  ArrowRightLeft,
  Truck, // Icon for picking/outbound
  Ship, // Icon for shipments
  AlertTriangle,
} from "lucide-react";

const navItems = [
  { href: "/", label: "Dashboard", icon: LayoutDashboard },
  { href: "/pallets", label: "Pallets", icon: Box },
  { href: "/move", label: "Move", icon: ArrowRightLeft },
  { href: "/picking", label: "Picking", icon: Truck },
  { href: "/shipments", label: "Shipments", icon: Ship },
  { href: "/items", label: "Items", icon: Package },
  { href: "/receiving", label: "Receiving", icon: <PERSON>an<PERSON><PERSON> },
  { href: "/settings", label: "Settings", icon: Settings },
];

export default function PrimaryNav() {
  const pathname = usePathname();
  const { currentWarehouse, isLoadingWarehouses } = useWarehouse();

  // Show warning state if no warehouse is selected (but not while loading)
  const showWarehouseWarning = !isLoadingWarehouses && !currentWarehouse;

  return (
    <nav
      className="fixed bottom-0 left-0 right-0 z-50 h-16 px-2 sm:px-4 bg-card border-t border-slate-200 shadow-[0_-1px_3px_0_rgba(0,0,0,0.07),0_-1px_2px_-1px_rgba(0,0,0,0.04)] safe-area-inset-bottom"
      role="navigation"
      aria-label="Main navigation"
    >
      {/* Warehouse warning banner */}
      {showWarehouseWarning && (
        <div
          className="absolute -top-8 left-0 right-0 bg-amber-50 border-t border-amber-200 px-4 py-1"
          role="alert"
          aria-live="polite"
        >
          <div className="flex items-center justify-center gap-1 text-xs text-amber-700">
            <AlertTriangle className="h-3 w-3" aria-hidden="true" />
            <span>Select a warehouse to access all features</span>
          </div>
        </div>
      )}

      <div className="flex justify-around items-center h-full">
        {navItems.map((item) => {
          const isActive = pathname === item.href;

          // Disable warehouse-dependent navigation items if no warehouse selected
          const isWarehouseDependent = [
            "/",
            "/pallets",
            "/move",
            "/picking",
            "/shipments",
            "/items",
            "/receiving",
          ].includes(item.href);
          const isDisabled = isWarehouseDependent && showWarehouseWarning;

          const linkContent = (
            <>
              <item.icon
                className={cn(
                  "size-5 mb-0.5",
                  isDisabled
                    ? "text-slate-300"
                    : isActive
                    ? "text-primary"
                    : "text-slate-500 group-hover:text-primary"
                )}
                strokeWidth={isActive ? 2.5 : 2}
                aria-hidden="true"
              />
              <span className="sr-only">
                {isActive ? `Current page: ` : ""}
              </span>
              {item.label}
            </>
          );

          if (isDisabled) {
            return (
              <div
                key={item.href}
                className="flex flex-col items-center justify-center w-full h-full text-xs text-slate-300 cursor-not-allowed min-h-[44px] touch-manipulation px-1 py-2"
                role="button"
                aria-disabled="true"
                aria-describedby="warehouse-warning"
                title={`${item.label} - Requires warehouse selection`}
              >
                {linkContent}
              </div>
            );
          }

          return (
            <Link
              key={item.href}
              href={item.href}
              className={cn(
                "flex flex-col items-center justify-center w-full h-full text-xs min-h-[44px] touch-manipulation",
                isActive ? "text-primary" : "text-slate-500",
                "hover:text-primary active:bg-slate-100 transition-all duration-150 ease-in-out group focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 rounded-md",
                "px-1 py-2"
              )}
              aria-current={isActive ? "page" : undefined}
              title={item.label}
            >
              {linkContent}
            </Link>
          );
        })}
      </div>
    </nav>
  );
}
