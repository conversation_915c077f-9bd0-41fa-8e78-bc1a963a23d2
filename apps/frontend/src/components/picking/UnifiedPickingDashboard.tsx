"use client";

import { useMemo } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import {
  Package,
  ShoppingCart,
  Truck,
  Clock,
  CheckCircle,
  TrendingUp,
  Users,
  BarChart3,
} from "lucide-react";
import { usePalletsSuspense, useShipments } from "@/hooks/api";
import { useWarehouse } from "@/components/providers/warehouse-provider";
import type { Pallet, Shipment } from "@quildora/types";

interface UnifiedPickingDashboardProps {
  onNavigateToMode?: (mode: "pallet" | "item" | "picklist") => void;
  className?: string;
}

interface PickingMetrics {
  // Destination-based (Pallet) picking metrics
  palletPicking: {
    totalPallets: number;
    readyForShipping: number;
    inProgress: number;
    destinations: number;
  };

  // Item-based picking metrics
  itemPicking: {
    totalItems: number;
    pickedItems: number;
    pendingItems: number;
    activeShipments: number;
  };

  // Picklist metrics
  picklists: {
    active: number;
    completed: number;
    totalItems: number;
  };

  // Overall performance
  performance: {
    completionRate: number;
    averagePickTime: string;
    todayShipments: number;
  };
}

export function UnifiedPickingDashboard({
  onNavigateToMode,
  className = "",
}: UnifiedPickingDashboardProps) {
  const { currentWarehouse } = useWarehouse();

  // Fetch data using existing hooks
  const { data: allPallets = [] } = usePalletsSuspense({});
  const { data: shipmentsResponse = { shipments: [] } } = useShipments();
  const shipments = Array.isArray(shipmentsResponse)
    ? shipmentsResponse
    : shipmentsResponse.shipments || [];

  // Calculate metrics from real data
  const metrics: PickingMetrics = useMemo(() => {
    // Pallet picking metrics
    const readyPallets = allPallets.filter(
      (p: Pallet) => p.status === "Stored" && p.shipToDestination
    );
    const inProgressPallets = allPallets.filter(
      (p: Pallet) => p.status === "Picking"
    );
    const uniqueDestinations = new Set(
      allPallets
        .filter((p: Pallet) => p.shipToDestination)
        .map((p: Pallet) => p.shipToDestination)
    ).size;

    // Item picking metrics (estimated from pallet data)
    const totalItems = allPallets.reduce(
      (sum: number, pallet: Pallet) => sum + (pallet.palletItems?.length || 0),
      0
    );
    const pickedItems = allPallets
      .filter((p: Pallet) => p.status === "Released")
      .reduce(
        (sum: number, pallet: Pallet) =>
          sum + (pallet.palletItems?.length || 0),
        0
      );

    // Shipment metrics
    const activeShipments = shipments.filter(
      (s: Shipment) => s.status === "preparing" || s.status === "ready"
    ).length;
    const todayShipments = shipments.filter((s: Shipment) => {
      const shipmentDate = new Date(s.createdAt);
      const today = new Date();
      return shipmentDate.toDateString() === today.toDateString();
    }).length;

    // Performance calculations
    const totalProcessed = allPallets.filter(
      (p: Pallet) => p.status === "Released"
    ).length;
    const completionRate =
      allPallets.length > 0
        ? Math.round((totalProcessed / allPallets.length) * 100)
        : 0;

    return {
      palletPicking: {
        totalPallets: allPallets.length,
        readyForShipping: readyPallets.length,
        inProgress: inProgressPallets.length,
        destinations: uniqueDestinations,
      },
      itemPicking: {
        totalItems,
        pickedItems,
        pendingItems: totalItems - pickedItems,
        activeShipments,
      },
      picklists: {
        active: 0, // TODO: Implement when picklist data is available
        completed: 0,
        totalItems: 0,
      },
      performance: {
        completionRate,
        averagePickTime: "12 min", // TODO: Calculate from real data
        todayShipments,
      },
    };
  }, [allPallets, shipments]);

  const handleModeNavigation = (mode: "pallet" | "item" | "picklist") => {
    onNavigateToMode?.(mode);
  };

  return (
    <div
      className={`space-y-6 ${className}`}
      role="main"
      aria-labelledby="dashboard-heading"
    >
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 id="dashboard-heading" className="text-2xl font-bold">
            Picking Dashboard
          </h2>
          <p className="text-muted-foreground">
            Overview of all picking operations in {currentWarehouse?.name}
          </p>
        </div>
        <Badge
          variant="outline"
          className="text-sm"
          role="status"
          aria-live="polite"
        >
          <BarChart3 className="h-4 w-4 mr-1" aria-hidden="true" />
          Real-time
        </Badge>
      </div>

      {/* Performance Overview */}
      <section aria-labelledby="performance-heading">
        <h3 id="performance-heading" className="sr-only">
          Performance Overview
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card role="region" aria-labelledby="completion-rate-title">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle
                id="completion-rate-title"
                className="text-sm font-medium"
              >
                Completion Rate
              </CardTitle>
              <TrendingUp
                className="h-4 w-4 text-muted-foreground"
                aria-hidden="true"
              />
            </CardHeader>
            <CardContent>
              <div
                className="text-2xl font-bold"
                aria-describedby="completion-rate-desc"
              >
                {metrics.performance.completionRate}%
              </div>
              <p
                id="completion-rate-desc"
                className="text-xs text-muted-foreground"
              >
                Overall picking efficiency
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Avg Pick Time
              </CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {metrics.performance.averagePickTime}
              </div>
              <p className="text-xs text-muted-foreground">
                Per pallet/shipment
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Today's Shipments
              </CardTitle>
              <Truck className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {metrics.performance.todayShipments}
              </div>
              <p className="text-xs text-muted-foreground">Completed today</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Active Operations
              </CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {metrics.palletPicking.inProgress +
                  metrics.itemPicking.activeShipments}
              </div>
              <p className="text-xs text-muted-foreground">In progress now</p>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Workflow-Specific Metrics */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Pallet Picking (Destination-based) */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              Pallet Picking
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm">Total Pallets</span>
                <Badge variant="secondary">
                  {metrics.palletPicking.totalPallets}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span className="text-sm">Ready for Shipping</span>
                <Badge variant="default">
                  {metrics.palletPicking.readyForShipping}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span className="text-sm">In Progress</span>
                <Badge variant="outline">
                  {metrics.palletPicking.inProgress}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span className="text-sm">Destinations</span>
                <Badge variant="secondary">
                  {metrics.palletPicking.destinations}
                </Badge>
              </div>
            </div>
            <Separator />
            <Button
              variant="outline"
              className="w-full focus:ring-2 focus:ring-primary focus:ring-offset-2"
              onClick={() => handleModeNavigation("pallet")}
              aria-describedby="pallet-mode-desc"
            >
              Switch to Pallet Mode
            </Button>
            <span id="pallet-mode-desc" className="sr-only">
              Navigate to destination-based pallet picking workflow
            </span>
          </CardContent>
        </Card>

        {/* Item Picking */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <ShoppingCart className="h-5 w-5" />
              Item Picking
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm">Total Items</span>
                <Badge variant="secondary">
                  {metrics.itemPicking.totalItems}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span className="text-sm">Picked Items</span>
                <Badge variant="default">
                  {metrics.itemPicking.pickedItems}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span className="text-sm">Pending Items</span>
                <Badge variant="outline">
                  {metrics.itemPicking.pendingItems}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span className="text-sm">Active Shipments</span>
                <Badge variant="secondary">
                  {metrics.itemPicking.activeShipments}
                </Badge>
              </div>
            </div>
            <Separator />
            <Button
              variant="outline"
              className="w-full"
              onClick={() => handleModeNavigation("item")}
            >
              Switch to Item Mode
            </Button>
          </CardContent>
        </Card>

        {/* Picklist Operations */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5" />
              Picklist Operations
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm">Active Picklists</span>
                <Badge variant="secondary">{metrics.picklists.active}</Badge>
              </div>
              <div className="flex justify-between">
                <span className="text-sm">Completed Today</span>
                <Badge variant="default">{metrics.picklists.completed}</Badge>
              </div>
              <div className="flex justify-between">
                <span className="text-sm">Total Items</span>
                <Badge variant="outline">{metrics.picklists.totalItems}</Badge>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">
                  Coming Soon
                </span>
                <Badge variant="outline">Beta</Badge>
              </div>
            </div>
            <Separator />
            <Button
              variant="outline"
              className="w-full"
              onClick={() => handleModeNavigation("picklist")}
              disabled
            >
              Switch to Picklist Mode
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
