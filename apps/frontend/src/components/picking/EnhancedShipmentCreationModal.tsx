"use client";

import { useState, useEffect, useMemo } from "react";
import { useSuspenseQuery } from "@tanstack/react-query";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  <PERSON><PERSON><PERSON><PERSON>le,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";

import { Checkbox } from "@/components/ui/checkbox";
import {
  Package,
  MapPin,
  ShoppingCart,
  Loader2,
  AlertCircle,
  Plus,
  Truck,
} from "lucide-react";
import { useAuth } from "@/components/providers/auth-provider";
import { useWarehouse } from "@/components/providers/warehouse-provider";
import { fetchWithAuth } from "@/lib/api";
import { usePalletsSuspense, useDestinationsWithCodes } from "@/hooks/api";
import { useDestinationAutocomplete } from "../../hooks/useDestinationAutocomplete";
import type {
  ItemPickingCart,
  DestinationResponse,
  Pallet,
} from "@quildora/types";

interface EnhancedShipmentCreationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onCreateShipment: (
    destination: string,
    destinationCode?: string,
    selectedPallets?: string[]
  ) => Promise<void>;
  cart: ItemPickingCart;
  isCreating: boolean;
}

export function EnhancedShipmentCreationModal({
  isOpen,
  onClose,
  onCreateShipment,
  cart,
  isCreating,
}: EnhancedShipmentCreationModalProps) {
  const [destination, setDestination] = useState("");
  const [destinationCode, setDestinationCode] = useState("");
  const [notes, setNotes] = useState("");
  const [error, setError] = useState("");
  const [selectedPallets, setSelectedPallets] = useState<Set<string>>(
    new Set()
  );
  const [activeTab, setActiveTab] = useState<"items" | "pallets">("items");

  // Fetch available pallets for selection
  const { data: allPallets = [] } = usePalletsSuspense({});

  // Filter pallets that are available for shipping (stored status)
  const availablePallets = allPallets.filter(
    (pallet: Pallet) => pallet.status === "Stored" && pallet.shipToDestination
  );

  // Fetch destinations with codes for autocomplete
  const { data: destinations = [] } = useDestinationsWithCodes();

  // Destination autocomplete
  const {
    suggestions,
    showSuggestions,
    selectedIndex,
    handleInputChange,
    handleKeyDown,
    handleSuggestionSelect,
    clearSuggestions,
  } = useDestinationAutocomplete(destinations);

  // Reset form when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setDestination(cart.destination || "");
      setDestinationCode(cart.destinationCode || "");
      setNotes("");
      setError("");
      setSelectedPallets(new Set());
      clearSuggestions();
    }
  }, [isOpen, cart.destination, cart.destinationCode, clearSuggestions]);

  const handleDestinationChange = (value: string) => {
    setDestination(value);
    setError("");
    handleInputChange(value, (suggestion) => {
      setDestination(suggestion.name);
      setDestinationCode(suggestion.code || "");
    });
  };

  const handleDestinationKeyDown = (e: React.KeyboardEvent) => {
    handleKeyDown(e, (suggestion) => {
      setDestination(suggestion.name);
      setDestinationCode(suggestion.code || "");
    });
  };

  const handleSuggestionClick = (suggestion: DestinationResponse) => {
    handleSuggestionSelect(suggestion);
    setDestination(suggestion.name);
    setDestinationCode(suggestion.code || "");
  };

  const handlePalletToggle = (palletId: string) => {
    const newSelected = new Set(selectedPallets);
    if (newSelected.has(palletId)) {
      newSelected.delete(palletId);
    } else {
      newSelected.add(palletId);
    }
    setSelectedPallets(newSelected);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!destination.trim()) {
      setError("Destination is required");
      return;
    }

    if (cart.items.length === 0 && selectedPallets.size === 0) {
      setError("Must include either items or pallets in shipment");
      return;
    }

    try {
      await onCreateShipment(
        destination.trim(),
        destinationCode.trim() || undefined,
        Array.from(selectedPallets)
      );
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "Failed to create shipment"
      );
    }
  };

  const isValid =
    destination.trim().length > 0 &&
    (cart.items.length > 0 || selectedPallets.size > 0);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <ShoppingCart className="h-5 w-5" />
            Create Mixed Shipment
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Shipment Details */}
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="destination">Destination *</Label>
              <div className="relative">
                <Input
                  id="destination"
                  value={destination}
                  onChange={(e) => handleDestinationChange(e.target.value)}
                  onKeyDown={handleDestinationKeyDown}
                  placeholder="Enter destination name..."
                  className="pr-10 min-h-[44px]"
                  disabled={isCreating}
                />

                {/* Destination Suggestions */}
                {showSuggestions && suggestions.length > 0 && (
                  <div className="absolute top-full left-0 right-0 z-50 mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-48 overflow-y-auto">
                    {suggestions.map((suggestion, index) => (
                      <button
                        key={`${suggestion.name}-${
                          suggestion.code || "no-code"
                        }`}
                        type="button"
                        onClick={() => handleSuggestionClick(suggestion)}
                        className={`w-full px-3 py-2 text-left hover:bg-gray-50 focus:bg-gray-50 focus:outline-none ${
                          index === selectedIndex ? "bg-gray-50" : ""
                        }`}
                      >
                        <div className="font-medium">{suggestion.name}</div>
                        {suggestion.code && (
                          <div className="text-sm text-gray-500">
                            Code: {suggestion.code}
                          </div>
                        )}
                      </button>
                    ))}
                  </div>
                )}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="destinationCode">Destination Code</Label>
                <Input
                  id="destinationCode"
                  value={destinationCode}
                  onChange={(e) => setDestinationCode(e.target.value)}
                  placeholder="Optional numerical code..."
                  className="min-h-[44px]"
                  disabled={isCreating}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="notes">Notes</Label>
              <Textarea
                id="notes"
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                placeholder="Optional shipment notes..."
                rows={3}
                disabled={isCreating}
              />
            </div>
          </div>

          <Separator />

          {/* Content Selection Tabs */}
          <div className="space-y-4">
            <div className="grid w-full grid-cols-2 bg-gray-100 rounded-lg p-1">
              <button
                type="button"
                onClick={() => setActiveTab("items")}
                className={`flex items-center justify-center gap-2 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                  activeTab === "items"
                    ? "bg-white text-gray-900 shadow-sm"
                    : "text-gray-600 hover:text-gray-900"
                }`}
              >
                <Package className="h-4 w-4" />
                Items ({cart.totalItems})
              </button>
              <button
                type="button"
                onClick={() => setActiveTab("pallets")}
                className={`flex items-center justify-center gap-2 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                  activeTab === "pallets"
                    ? "bg-white text-gray-900 shadow-sm"
                    : "text-gray-600 hover:text-gray-900"
                }`}
              >
                <Truck className="h-4 w-4" />
                Pallets ({selectedPallets.size})
              </button>
            </div>

            {activeTab === "items" && (
              <div className="space-y-4">
                <h3 className="font-medium">Items from Cart</h3>
                {cart.items.length > 0 ? (
                  <div className="max-h-48 overflow-y-auto space-y-2">
                    {cart.items.map((item, index) => (
                      <div
                        key={`${item.itemId}-${
                          item.sourcePalletId || "no-pallet"
                        }-${index}`}
                        className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                      >
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <Package className="h-4 w-4 text-gray-500" />
                            <span className="font-medium">{item.itemName}</span>
                          </div>
                          {item.sourcePalletBarcode && (
                            <div className="text-sm text-gray-500 flex items-center gap-1">
                              <MapPin className="h-3 w-3" />
                              From: {item.sourcePalletBarcode}
                            </div>
                          )}
                        </div>
                        <Badge variant="outline">
                          {item.quantity} {item.unitOfMeasure}
                        </Badge>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <Package className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p>No items in cart</p>
                  </div>
                )}
              </div>
            )}

            {activeTab === "pallets" && (
              <div className="space-y-4">
                <h3 className="font-medium">Available Pallets</h3>
                {availablePallets.length > 0 ? (
                  <div className="max-h-64 overflow-y-auto space-y-2">
                    {availablePallets.map((pallet: Pallet) => (
                      <div
                        key={pallet.id}
                        className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50"
                      >
                        <Checkbox
                          id={`pallet-${pallet.id}`}
                          checked={selectedPallets.has(pallet.id)}
                          onCheckedChange={() => handlePalletToggle(pallet.id)}
                        />
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <Package className="h-4 w-4 text-blue-500" />
                            <span className="font-medium">
                              {pallet.barcode}
                            </span>
                          </div>
                          <div className="text-sm text-gray-500">
                            Destination: {pallet.shipToDestination}
                          </div>
                          {pallet.location && (
                            <div className="text-sm text-gray-500">
                              Location: {pallet.location.name}
                            </div>
                          )}
                        </div>
                        <Badge variant="secondary">Pallet</Badge>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <Truck className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p>No pallets available for shipping</p>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Error Display */}
          {error && (
            <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg text-red-700">
              <AlertCircle className="h-4 w-4" />
              <span className="text-sm">{error}</span>
            </div>
          )}

          <DialogFooter className="gap-2">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isCreating}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={!isValid || isCreating}
              className="bg-green-600 hover:bg-green-700 min-h-[44px]"
            >
              {isCreating ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Creating Shipment...
                </>
              ) : (
                <>
                  <ShoppingCart className="h-4 w-4 mr-2" />
                  Create Shipment
                </>
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
