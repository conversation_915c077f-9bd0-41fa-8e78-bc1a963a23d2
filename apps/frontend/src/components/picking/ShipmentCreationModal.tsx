"use client";

import { useState, useEffect } from "react";
import { useSuspenseQuery } from "@tanstack/react-query";
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  Di<PERSON>Title,
  DialogFooter,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Package,
  MapPin,
  ShoppingCart,
  Loader2,
  AlertCircle,
} from "lucide-react";
import { useAuth } from "@/components/providers/auth-provider";
import { useWarehouse } from "@/components/providers/warehouse-provider";
import { fetchWithAuth } from "@/lib/api";
import { useDestinationAutocomplete } from "../../hooks/useDestinationAutocomplete";
import type { ItemPickingCart, DestinationResponse } from "@quildora/types";

interface ShipmentCreationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onCreateShipment: (
    destination: string,
    destinationCode?: string
  ) => Promise<void>;
  cart: ItemPickingCart;
  isCreating: boolean;
}

// API function for fetching destinations
const fetchDestinations = async (
  warehouseId: string,
  token: string | null
): Promise<DestinationResponse[]> => {
  const searchParams = new URLSearchParams();
  searchParams.append("warehouseId", warehouseId);

  return fetchWithAuth(`/api/pallets/destinations?${searchParams.toString()}`, {
    token,
  });
};

export function ShipmentCreationModal({
  isOpen,
  onClose,
  onCreateShipment,
  cart,
  isCreating,
}: ShipmentCreationModalProps) {
  const { appToken } = useAuth();
  const { currentWarehouse } = useWarehouse();

  const [destination, setDestination] = useState("");
  const [destinationCode, setDestinationCode] = useState("");
  const [notes, setNotes] = useState("");
  const [error, setError] = useState("");

  // Fetch existing destinations for autocomplete
  const { data: existingDestinations = [] } = useSuspenseQuery<
    DestinationResponse[],
    Error
  >({
    queryKey: ["destinations", currentWarehouse?.id],
    queryFn: () => fetchDestinations(currentWarehouse?.id || "", appToken),
  });

  // Use destination autocomplete hook
  const {
    suggestions,
    showSuggestions,
    selectedIndex,
    handleInputChange,
    handleKeyDown,
    handleSuggestionSelect,
    clearSuggestions,
  } = useDestinationAutocomplete(existingDestinations);

  // Reset form when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setDestination(cart.destination || "");
      setDestinationCode(cart.destinationCode || "");
      setNotes("");
      setError("");
    } else {
      setDestination("");
      setDestinationCode("");
      setNotes("");
      setError("");
      clearSuggestions();
    }
  }, [isOpen, cart.destination, cart.destinationCode, clearSuggestions]);

  const handleDestinationChange = (value: string) => {
    setDestination(value);
    setError("");
    handleInputChange(value, (suggestion) => {
      setDestination(suggestion.name);
      setDestinationCode(suggestion.code || "");
    });
  };

  const handleDestinationKeyDown = (e: React.KeyboardEvent) => {
    handleKeyDown(e, (suggestion) => {
      setDestination(suggestion.name);
      setDestinationCode(suggestion.code || "");
    });
  };

  const handleSuggestionClick = (suggestion: DestinationResponse) => {
    handleSuggestionSelect(suggestion);
    setDestination(suggestion.name);
    setDestinationCode(suggestion.code || "");
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!destination.trim()) {
      setError("Destination is required");
      return;
    }

    if (cart.items.length === 0) {
      setError("Cart is empty");
      return;
    }

    try {
      await onCreateShipment(
        destination.trim(),
        destinationCode.trim() || undefined
      );
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "Failed to create shipment"
      );
    }
  };

  const isValid = destination.trim().length > 0 && cart.items.length > 0;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <ShoppingCart className="h-5 w-5" />
            Create Outgoing Shipment
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Shipment Details */}
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="destination">Destination *</Label>
              <div className="relative">
                <Input
                  id="destination"
                  value={destination}
                  onChange={(e) => handleDestinationChange(e.target.value)}
                  onKeyDown={handleDestinationKeyDown}
                  placeholder="Enter destination name..."
                  className="pr-10"
                  disabled={isCreating}
                />

                {/* Destination Suggestions */}
                {showSuggestions && suggestions.length > 0 && (
                  <div className="absolute top-full left-0 right-0 z-50 mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-48 overflow-y-auto">
                    {suggestions.map((suggestion, index) => (
                      <button
                        key={`${suggestion.name}-${
                          suggestion.code || "no-code"
                        }`}
                        type="button"
                        onClick={() => handleSuggestionClick(suggestion)}
                        className={`w-full px-3 py-2 text-left hover:bg-gray-50 focus:bg-gray-50 focus:outline-none ${
                          index === selectedIndex ? "bg-gray-50" : ""
                        }`}
                      >
                        <div className="font-medium">{suggestion.name}</div>
                        {suggestion.code && (
                          <div className="text-sm text-gray-500">
                            Code: {suggestion.code}
                          </div>
                        )}
                      </button>
                    ))}
                  </div>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="destinationCode">Destination Code</Label>
              <Input
                id="destinationCode"
                value={destinationCode}
                onChange={(e) => setDestinationCode(e.target.value)}
                placeholder="Optional numerical code..."
                disabled={isCreating}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="notes">Notes</Label>
              <Textarea
                id="notes"
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                placeholder="Optional shipment notes..."
                rows={3}
                disabled={isCreating}
              />
            </div>
          </div>

          <Separator />

          {/* Cart Summary */}
          <div className="space-y-4">
            <h3 className="font-medium flex items-center gap-2">
              <Package className="h-4 w-4" />
              Items to Ship ({cart.totalItems})
            </h3>

            <div className="max-h-48 overflow-y-auto space-y-2">
              {cart.items.map((item, index) => (
                <div
                  key={`${item.itemId}-${
                    item.sourcePalletId || "no-pallet"
                  }-${index}`}
                  className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                >
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="font-medium truncate">
                        {item.itemName}
                      </span>
                      {item.sku && (
                        <Badge variant="outline" className="text-xs">
                          {item.sku}
                        </Badge>
                      )}
                    </div>
                    <div className="text-sm text-gray-500 space-y-1">
                      {item.locationName && (
                        <div className="flex items-center gap-1">
                          <MapPin className="h-3 w-3" />
                          <span>{item.locationName}</span>
                        </div>
                      )}
                      {item.sourcePalletBarcode && (
                        <div className="flex items-center gap-1">
                          <Package className="h-3 w-3" />
                          <span>Pallet: {item.sourcePalletBarcode}</span>
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-medium">
                      {item.quantity} {item.unitOfMeasure}
                    </div>
                  </div>
                </div>
              ))}
            </div>

            <div className="flex justify-between items-center p-3 bg-blue-50 rounded-lg">
              <span className="font-medium">Total Quantity:</span>
              <span className="font-semibold">{cart.totalQuantity}</span>
            </div>
          </div>

          {/* Error Display */}
          {error && (
            <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg text-red-700">
              <AlertCircle className="h-4 w-4" />
              <span className="text-sm">{error}</span>
            </div>
          )}

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isCreating}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={!isValid || isCreating}
              className="bg-green-600 hover:bg-green-700"
            >
              {isCreating ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Creating...
                </>
              ) : (
                <>
                  <ShoppingCart className="h-4 w-4 mr-2" />
                  Create Shipment
                </>
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
