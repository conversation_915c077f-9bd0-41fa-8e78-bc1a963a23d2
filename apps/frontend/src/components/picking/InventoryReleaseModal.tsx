"use client";

import { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  Di<PERSON>Title,
  DialogFooter,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Truck,
  Package,
  AlertTriangle,
  CheckCircle,
  Loader2,
  Sparkles,
} from "lucide-react";
import { useAuth } from "@/components/providers/auth-provider";
import { fetchWithAuth } from "@/lib/api";
import { useConfetti } from "@/lib/confetti";
import type {
  ReleaseInventoryDto,
  InventoryReleaseResponse,
} from "@quildora/types";

interface InventoryReleaseModalProps {
  isOpen: boolean;
  onClose: () => void;
  shipmentId: string;
  shipmentNumber: string;
  onSuccess?: (response: InventoryReleaseResponse) => void;
}

// API function for releasing inventory
const releaseInventory = async (
  shipmentId: string,
  data: ReleaseInventoryDto,
  token: string | null
): Promise<InventoryReleaseResponse> => {
  return fetchWithAuth(`/api/shipments/${shipmentId}/release`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
    token,
  });
};

export function InventoryReleaseModal({
  isOpen,
  onClose,
  shipmentId,
  shipmentNumber,
  onSuccess,
}: InventoryReleaseModalProps) {
  const { appToken } = useAuth();
  const queryClient = useQueryClient();
  const { triggerPreset } = useConfetti();

  const [trackingNumber, setTrackingNumber] = useState("");
  const [releaseNotes, setReleaseNotes] = useState("");
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [error, setError] = useState("");

  // Release inventory mutation
  const releaseMutation = useMutation({
    mutationFn: (data: ReleaseInventoryDto) =>
      releaseInventory(shipmentId, data, appToken),
    onSuccess: (response) => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ["shipments"] });
      queryClient.invalidateQueries({ queryKey: ["packing-list"] });
      queryClient.invalidateQueries({ queryKey: ["item-locations"] });

      // Show success and trigger confetti
      triggerPreset("inventoryReleased");
      setShowConfirmation(true);

      // Call success callback after a delay
      setTimeout(() => {
        onSuccess?.(response);
        handleClose();
      }, 3000);
    },
    onError: (err) => {
      setError(
        err instanceof Error ? err.message : "Failed to release inventory"
      );
    },
  });

  const handleClose = () => {
    if (!releaseMutation.isPending) {
      setTrackingNumber("");
      setReleaseNotes("");
      setShowConfirmation(false);
      setError("");
      onClose();
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setError("");

    const releaseData: ReleaseInventoryDto = {
      trackingNumber: trackingNumber.trim() || undefined,
      releaseNotes: releaseNotes.trim() || undefined,
    };

    releaseMutation.mutate(releaseData);
  };

  const isValid = !releaseMutation.isPending;

  // Success confirmation view
  if (showConfirmation) {
    return (
      <Dialog open={isOpen} onOpenChange={() => {}}>
        <DialogContent className="max-w-md">
          <div className="text-center py-6">
            <div className="relative mb-4">
              <CheckCircle className="h-16 w-16 text-green-600 mx-auto" />
              <Sparkles className="h-6 w-6 text-yellow-500 absolute -top-1 -right-1 animate-pulse" />
            </div>
            <h2 className="text-xl font-semibold mb-2">Shipment Released!</h2>
            <p className="text-muted-foreground mb-4">
              Inventory has been successfully released for shipment{" "}
              <span className="font-mono">{shipmentNumber}</span>
            </p>
            <div className="space-y-2 text-sm">
              {releaseMutation.data && (
                <>
                  <div className="flex justify-between">
                    <span>Items Released:</span>
                    <Badge variant="secondary">
                      {releaseMutation.data.inventoryUpdates.itemsReleased}
                    </Badge>
                  </div>
                  <div className="flex justify-between">
                    <span>Total Quantity:</span>
                    <Badge variant="secondary">
                      {
                        releaseMutation.data.inventoryUpdates
                          .totalQuantityReleased
                      }
                    </Badge>
                  </div>
                  {releaseMutation.data.trackingNumber && (
                    <div className="flex justify-between">
                      <span>Tracking:</span>
                      <Badge variant="outline" className="font-mono">
                        {releaseMutation.data.trackingNumber}
                      </Badge>
                    </div>
                  )}
                </>
              )}
            </div>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-lg">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Truck className="h-5 w-5" />
            Release Inventory for Shipment
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Shipment Info */}
          <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Package className="h-4 w-4 text-blue-600" />
              <span className="font-medium">Shipment Details</span>
            </div>
            <div className="space-y-1 text-sm">
              <div className="flex justify-between">
                <span>Shipment Number:</span>
                <span className="font-mono">{shipmentNumber}</span>
              </div>
              <div className="flex justify-between">
                <span>Status:</span>
                <Badge variant="outline" className="bg-blue-100 text-blue-800">
                  Ready to Ship
                </Badge>
              </div>
            </div>
          </div>

          {/* Warning */}
          <div className="flex items-start gap-3 p-4 bg-amber-50 border border-amber-200 rounded-lg">
            <AlertTriangle className="h-5 w-5 text-amber-600 mt-0.5 flex-shrink-0" />
            <div className="text-sm">
              <p className="font-medium text-amber-800 mb-1">
                Important Notice
              </p>
              <p className="text-amber-700">
                This action will permanently release inventory from your
                warehouse. Items will be marked as shipped and cannot be undone.
              </p>
            </div>
          </div>

          {/* Form Fields */}
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="trackingNumber">Tracking Number (Optional)</Label>
              <Input
                id="trackingNumber"
                value={trackingNumber}
                onChange={(e) => setTrackingNumber(e.target.value)}
                placeholder="Enter tracking number..."
                disabled={releaseMutation.isPending}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="releaseNotes">Release Notes (Optional)</Label>
              <Textarea
                id="releaseNotes"
                value={releaseNotes}
                onChange={(e) => setReleaseNotes(e.target.value)}
                placeholder="Add any notes about this shipment..."
                rows={3}
                disabled={releaseMutation.isPending}
              />
            </div>
          </div>

          {/* Error Display */}
          {error && (
            <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg text-red-700">
              <AlertTriangle className="h-4 w-4" />
              <span className="text-sm">{error}</span>
            </div>
          )}

          <Separator />

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={releaseMutation.isPending}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={!isValid}
              className="bg-green-600 hover:bg-green-700"
            >
              {releaseMutation.isPending ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Releasing...
                </>
              ) : (
                <>
                  <Truck className="h-4 w-4 mr-2" />
                  Release & Ship
                </>
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
