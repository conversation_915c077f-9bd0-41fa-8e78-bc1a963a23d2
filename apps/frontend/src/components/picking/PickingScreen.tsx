"use client";

import { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";

import { useAuth } from "@/components/providers/auth-provider";
import { useWarehouse } from "@/components/providers/warehouse-provider";
import { useDestinations, usePalletsSuspense } from "@/hooks/api";
import { Button } from "@/components/ui/button";
import { usePalletFiltering } from "@/hooks/usePalletFiltering";
import CreatePicklistModal from "./CreatePicklistModal";
import PicklistScreen from "./PicklistScreen";
import { usePickingOperations } from "@/hooks/usePickingOperations";
import { PickingOverview } from "./PickingOverview";
import { DestinationSelectionCard } from "./DestinationSelectionCard";
import { PalletDisplayCard } from "./PalletDisplayCard";
import { PickingModeSelector, type PickingMode } from "./PickingModeSelector";
import { ItemPickingView } from "./ItemPickingView";
import { UnifiedPickingDashboard } from "./UnifiedPickingDashboard";
import { useRoutePrefetching } from "@/hooks/usePrefetchingStrategy";
import { useWorkflowPersistence } from "@/hooks/useWorkflowPersistence";
import { useWorkflowDataSync } from "@/hooks/useWorkflowDataSync";

// API functions for data fetching

export default function PickingScreen() {
  const { currentWarehouse } = useWarehouse();
  const router = useRouter();
  const searchParams = useSearchParams();

  // Use comprehensive workflow persistence
  const { preferences, updatePickingMode, updateLastUsedDestination } =
    useWorkflowPersistence();

  // Use workflow data synchronization
  const { prefetchForWorkflow, invalidateSharedData } = useWorkflowDataSync();

  // Dashboard visibility state
  const [showDashboard, setShowDashboard] = useState(false);

  // Get workflow mode from URL parameters with fallback to persisted preferences
  const urlMode = searchParams.get("mode") as PickingMode;
  const [currentMode, setCurrentMode] = useState<PickingMode>(() => {
    // Priority: URL parameter > persisted preferences > default
    if (urlMode && ["pallet", "item", "picklist"].includes(urlMode)) {
      return urlMode;
    }
    return preferences.pickingMode;
  });

  // Sync URL with current mode and persist preferences
  useEffect(() => {
    if (currentWarehouse?.id) {
      // Update persisted preferences
      if (currentMode !== preferences.pickingMode) {
        updatePickingMode(currentMode);
      }

      // Update URL if different from current mode
      if (urlMode !== currentMode) {
        const newSearchParams = new URLSearchParams(searchParams.toString());
        newSearchParams.set("mode", currentMode);
        router.replace(`/picking?${newSearchParams.toString()}`, {
          scroll: false,
        });
      }
    }
  }, [
    currentMode,
    currentWarehouse?.id,
    urlMode,
    searchParams,
    router,
    preferences.pickingMode,
    updatePickingMode,
  ]);

  // Enable route-level prefetching for picking operations
  useRoutePrefetching("picking");

  // Use custom hook for picking operations
  const {
    selectedDestination,
    currentView,
    activePicklist,
    showCreatePicklist,
    setSelectedDestination,
    handleCreatePicklist,
    handleBackToDestinations,
    handleCompletePicklist,
    handleUpdateDestination,
    handleOpenCreatePicklist,
    handleCloseCreatePicklist,
  } = usePickingOperations();

  // Initialize selected destination from persisted preferences
  useEffect(() => {
    if (preferences.lastUsedDestination && !selectedDestination) {
      setSelectedDestination(preferences.lastUsedDestination);
    }
  }, [
    preferences.lastUsedDestination,
    selectedDestination,
    setSelectedDestination,
  ]);

  // Fetch destinations using existing warehouse-aware hook
  const { data: destinations = [] } = useDestinations();

  // Enhanced destination selection with persistence
  const handleDestinationSelectWithProcessing = (destination: string) => {
    // Direct selection without processing delays for better UX
    setSelectedDestination(destination);

    // Persist last used destination for better UX
    updateLastUsedDestination(destination);
  };

  // Handler for when processing starts (optimistic feedback)
  const handleProcessingStart = () => {
    // Removed processing state as it's no longer needed
  };

  // Fetch all pallets and filter by destination using existing hooks
  const { data: allPallets = [] } = usePalletsSuspense({});
  const { filterPallets } = usePalletFiltering();

  // Filter pallets by selected destination
  const pallets = selectedDestination
    ? filterPallets(allPallets).filter((pallet) =>
        pallet.shipToDestination
          ?.toLowerCase()
          .includes(selectedDestination.toLowerCase())
      )
    : [];

  // All handler functions moved to usePickingOperations hook

  // useSuspenseQuery handles loading and error states automatically

  // Handler for workflow mode changes with URL navigation and data sync
  const handleModeChange = async (mode: PickingMode) => {
    setCurrentMode(mode);

    // Prefetch data for the new workflow mode
    await prefetchForWorkflow(mode);

    // Update URL immediately for better UX
    const newSearchParams = new URLSearchParams(searchParams.toString());
    newSearchParams.set("mode", mode);
    router.push(`/picking?${newSearchParams.toString()}`, { scroll: false });
  };

  // Handler for navigating back from item picking to pallet mode
  const handleBackToPalletMode = () => {
    handleModeChange("pallet");
  };

  // Handler for dashboard navigation with data sync
  const handleDashboardModeChange = async (mode: PickingMode) => {
    setShowDashboard(false);

    // Ensure fresh data when navigating from dashboard
    await invalidateSharedData();
    await handleModeChange(mode);
  };

  // Handler for toggling dashboard
  const handleToggleDashboard = () => {
    setShowDashboard(!showDashboard);
  };

  // Handle browser back/forward navigation
  useEffect(() => {
    const handlePopState = () => {
      const newSearchParams = new URLSearchParams(window.location.search);
      const newMode = newSearchParams.get("mode") as PickingMode;
      if (newMode && ["pallet", "item", "picklist"].includes(newMode)) {
        setCurrentMode(newMode);
      }
    };

    window.addEventListener("popstate", handlePopState);
    return () => window.removeEventListener("popstate", handlePopState);
  }, []);

  // Show unified dashboard if requested
  if (showDashboard) {
    return (
      <div className="space-y-6">
        {/* Header with back button */}
        <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
          <h1 className="text-xl sm:text-2xl font-bold">Picking Dashboard</h1>
          <Button
            variant="outline"
            onClick={handleToggleDashboard}
            className="min-h-[44px] touch-manipulation px-4 py-2 w-full sm:w-auto"
          >
            Back to Picking
          </Button>
        </div>

        {/* Unified dashboard */}
        <UnifiedPickingDashboard onNavigateToMode={handleDashboardModeChange} />
      </div>
    );
  }

  // Show picklist view if active
  if (currentView === "picklist" && activePicklist) {
    return (
      <PicklistScreen
        picklist={activePicklist}
        onBack={handleBackToDestinations}
        onComplete={handleCompletePicklist}
        onUpdateDestination={handleUpdateDestination}
      />
    );
  }

  // Show item picking view for item mode
  if (currentMode === "item") {
    return (
      <div className="space-y-6">
        {/* Mode selector */}
        <PickingModeSelector
          currentMode={currentMode}
          onModeChange={handleModeChange}
        />

        {/* Dashboard toggle - positioned below mode selector */}
        <div className="flex justify-center">
          <Button
            variant="outline"
            onClick={handleToggleDashboard}
            className="min-h-[44px] touch-manipulation px-6 py-2"
          >
            View Dashboard
          </Button>
        </div>

        {/* Item picking interface */}
        <ItemPickingView onBack={handleBackToPalletMode} />
      </div>
    );
  }

  // Show picklist mode selector and overview
  if (currentMode === "picklist") {
    return (
      <div className="space-y-6">
        {/* Mode selector */}
        <PickingModeSelector
          currentMode={currentMode}
          onModeChange={handleModeChange}
        />

        {/* Dashboard toggle - positioned below mode selector */}
        <div className="flex justify-center">
          <Button
            variant="outline"
            onClick={handleToggleDashboard}
            className="min-h-[44px] touch-manipulation px-6 py-2"
          >
            View Dashboard
          </Button>
        </div>

        {/* Picklist overview and creation */}
        <PickingOverview onCreatePicklist={handleOpenCreatePicklist} />

        <CreatePicklistModal
          isOpen={showCreatePicklist}
          onClose={handleCloseCreatePicklist}
          onCreatePicklist={handleCreatePicklist}
        />
      </div>
    );
  }

  // Default: Pallet picking mode (destination-based)
  return (
    <div className="space-y-6">
      {/* Mode selector */}
      <PickingModeSelector
        currentMode={currentMode}
        onModeChange={handleModeChange}
      />

      {/* Dashboard toggle - positioned below mode selector */}
      <div className="flex justify-center">
        <Button
          variant="outline"
          onClick={handleToggleDashboard}
          className="min-h-[44px] touch-manipulation px-6 py-2"
        >
          View Dashboard
        </Button>
      </div>

      {/* Traditional pallet picking interface */}
      <PickingOverview onCreatePicklist={handleOpenCreatePicklist} />

      <DestinationSelectionCard
        destinations={destinations}
        selectedDestination={selectedDestination}
        onDestinationSelect={handleDestinationSelectWithProcessing}
        isProcessing={false}
        onProcessingStart={handleProcessingStart}
      />

      <PalletDisplayCard
        selectedDestination={selectedDestination}
        pallets={pallets}
      />

      <CreatePicklistModal
        isOpen={showCreatePicklist}
        onClose={handleCloseCreatePicklist}
        onCreatePicklist={handleCreatePicklist}
      />
    </div>
  );
}
