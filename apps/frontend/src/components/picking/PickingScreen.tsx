"use client";

import { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useSuspenseQuery } from "@tanstack/react-query";
import { fetchWithAuth } from "@/lib/api";
import { useAuth } from "@/components/providers/auth-provider";
import { useWarehouse } from "@/components/providers/warehouse-provider";
import CreatePicklistModal from "./CreatePicklistModal";
import PicklistScreen from "./PicklistScreen";
import { usePickingOperations } from "@/hooks/usePickingOperations";
import { PickingOverview } from "./PickingOverview";
import { DestinationSelectionCard } from "./DestinationSelectionCard";
import { PalletDisplayCard } from "./PalletDisplayCard";
import { PickingModeSelector, type PickingMode } from "./PickingModeSelector";
import { ItemPickingView } from "./ItemPickingView";
import { useRoutePrefetching } from "@/hooks/usePrefetchingStrategy";
import { useCartPersistence } from "@/hooks/useCartPersistence";

// API functions for data fetching
const fetchDestinations = async (
  warehouseId: string,
  token: string | null
): Promise<string[]> => {
  const searchParams = new URLSearchParams();
  searchParams.append("warehouseId", warehouseId);

  return fetchWithAuth(`/api/pallets/destinations?${searchParams.toString()}`, {
    token,
  });
};

const fetchPallets = async (
  warehouseId: string,
  token: string | null,
  filters?: { shipToDestination?: string }
): Promise<any[]> => {
  const searchParams = new URLSearchParams();
  searchParams.append("warehouseId", warehouseId);

  if (filters?.shipToDestination) {
    searchParams.append("shipToDestination", filters.shipToDestination);
  }

  const url = `/api/pallets${
    searchParams.toString() ? `?${searchParams.toString()}` : ""
  }`;
  return fetchWithAuth(url, { token });
};

export default function PickingScreen() {
  const { appToken } = useAuth();
  const { currentWarehouse } = useWarehouse();
  const router = useRouter();
  const searchParams = useSearchParams();

  // Get workflow mode from URL parameters with fallback to localStorage
  const urlMode = searchParams.get("mode") as PickingMode;
  const [currentMode, setCurrentMode] = useState<PickingMode>(() => {
    // Priority: URL parameter > localStorage > default
    if (urlMode && ["pallet", "item", "picklist"].includes(urlMode)) {
      return urlMode;
    }
    if (typeof window !== "undefined") {
      const saved = localStorage.getItem(
        `picking-mode-${currentWarehouse?.id}`
      );
      return (saved as PickingMode) || "pallet";
    }
    return "pallet";
  });

  // Sync URL with current mode and persist to localStorage
  useEffect(() => {
    if (currentWarehouse?.id) {
      // Update localStorage
      localStorage.setItem(`picking-mode-${currentWarehouse.id}`, currentMode);

      // Update URL if different from current mode
      if (urlMode !== currentMode) {
        const newSearchParams = new URLSearchParams(searchParams.toString());
        newSearchParams.set("mode", currentMode);
        router.replace(`/picking?${newSearchParams.toString()}`, {
          scroll: false,
        });
      }
    }
  }, [currentMode, currentWarehouse?.id, urlMode, searchParams, router]);

  // Enable route-level prefetching for picking operations
  useRoutePrefetching("picking");

  // Initialize cart persistence for picking sessions (used by item picking)
  const {
    cart,
    isLoading: isCartLoading,
    addItem,
    removeItem,
    updateItemQuantity,
    updateDestination,
    clearCart,
  } = useCartPersistence({
    autoSave: true,
    enableCrossSession: true,
  });

  // Prefetch data based on current workflow mode
  useEffect(() => {
    if (!currentWarehouse?.id || !appToken) return;

    // Prefetch data specific to the current workflow mode
    switch (currentMode) {
      case "item":
        // Item picking mode - prefetch all items for client-side filtering
        // This is already handled by useRoutePrefetching("picking")
        break;
      case "pallet":
        // Pallet picking mode - prefetch destinations and common pallets
        // Destinations are already fetched via useSuspenseQuery below
        break;
      case "picklist":
        // Picklist mode - prefetch picklist data
        // This could be enhanced with specific picklist prefetching
        break;
    }
  }, [currentMode, currentWarehouse?.id, appToken]);

  // Use custom hook for picking operations
  const {
    selectedDestination,
    currentView,
    activePicklist,
    showCreatePicklist,
    setSelectedDestination,
    handleCreatePicklist,
    handleBackToDestinations,
    handleCompletePicklist,
    handleUpdateDestination,
    handleOpenCreatePicklist,
    handleCloseCreatePicklist,
  } = usePickingOperations();

  // Fetch destinations using useSuspenseQuery with optimized caching
  const { data: destinations = [] } = useSuspenseQuery<string[], Error>({
    queryKey: ["destinations", currentWarehouse?.id],
    queryFn: () => fetchDestinations(currentWarehouse?.id || "", appToken),
    staleTime: 300000, // 5 minutes - destinations don't change frequently
    gcTime: 600000, // 10 minutes - keep in cache longer
  });

  // Optimized destination selection handler (removed artificial delays)
  const handleDestinationSelectWithProcessing = (destination: string) => {
    // Direct selection without processing delays for better UX
    setSelectedDestination(destination);
  };

  // Handler for when processing starts (optimistic feedback)
  const handleProcessingStart = () => {
    // Removed processing state as it's no longer needed
  };

  // Fetch pallets for selected destination using useSuspenseQuery with optimized caching
  const { data: pallets = [] } = useSuspenseQuery<any[], Error>({
    queryKey: ["pallets", currentWarehouse?.id, selectedDestination || "none"],
    queryFn: () => {
      if (!selectedDestination) {
        return Promise.resolve([]);
      }
      return fetchPallets(currentWarehouse?.id || "", appToken, {
        shipToDestination: selectedDestination,
      });
    },
    staleTime: 60000, // 1 minute - pallets change more frequently than destinations
    gcTime: 300000, // 5 minutes - keep in cache for quick switching
  });

  // All handler functions moved to usePickingOperations hook

  // useSuspenseQuery handles loading and error states automatically

  // Handler for workflow mode changes with URL navigation
  const handleModeChange = (mode: PickingMode) => {
    setCurrentMode(mode);

    // Update URL immediately for better UX
    const newSearchParams = new URLSearchParams(searchParams.toString());
    newSearchParams.set("mode", mode);
    router.push(`/picking?${newSearchParams.toString()}`, { scroll: false });
  };

  // Handler for navigating back from item picking to pallet mode
  const handleBackToPalletMode = () => {
    handleModeChange("pallet");
  };

  // Handle browser back/forward navigation
  useEffect(() => {
    const handlePopState = () => {
      const newSearchParams = new URLSearchParams(window.location.search);
      const newMode = newSearchParams.get("mode") as PickingMode;
      if (newMode && ["pallet", "item", "picklist"].includes(newMode)) {
        setCurrentMode(newMode);
      }
    };

    window.addEventListener("popstate", handlePopState);
    return () => window.removeEventListener("popstate", handlePopState);
  }, []);

  // Show picklist view if active
  if (currentView === "picklist" && activePicklist) {
    return (
      <PicklistScreen
        picklist={activePicklist}
        onBack={handleBackToDestinations}
        onComplete={handleCompletePicklist}
        onUpdateDestination={handleUpdateDestination}
      />
    );
  }

  // Show item picking view for item mode
  if (currentMode === "item") {
    return (
      <div className="space-y-6">
        {/* Mode selector for switching between workflows */}
        <PickingModeSelector
          currentMode={currentMode}
          onModeChange={handleModeChange}
        />

        {/* Item picking interface */}
        <ItemPickingView onBack={handleBackToPalletMode} />
      </div>
    );
  }

  // Show picklist mode selector and overview
  if (currentMode === "picklist") {
    return (
      <div className="space-y-6">
        {/* Mode selector */}
        <PickingModeSelector
          currentMode={currentMode}
          onModeChange={handleModeChange}
        />

        {/* Picklist overview and creation */}
        <PickingOverview onCreatePicklist={handleOpenCreatePicklist} />

        <CreatePicklistModal
          isOpen={showCreatePicklist}
          onClose={handleCloseCreatePicklist}
          onCreatePicklist={handleCreatePicklist}
        />
      </div>
    );
  }

  // Default: Pallet picking mode (destination-based)
  return (
    <div className="space-y-6">
      {/* Mode selector for switching between workflows */}
      <PickingModeSelector
        currentMode={currentMode}
        onModeChange={handleModeChange}
      />

      {/* Traditional pallet picking interface */}
      <PickingOverview onCreatePicklist={handleOpenCreatePicklist} />

      <DestinationSelectionCard
        destinations={destinations}
        selectedDestination={selectedDestination}
        onDestinationSelect={handleDestinationSelectWithProcessing}
        isProcessing={false}
        onProcessingStart={handleProcessingStart}
      />

      <PalletDisplayCard
        selectedDestination={selectedDestination}
        pallets={pallets}
      />

      <CreatePicklistModal
        isOpen={showCreatePicklist}
        onClose={handleCloseCreatePicklist}
        onCreatePicklist={handleCreatePicklist}
      />
    </div>
  );
}
