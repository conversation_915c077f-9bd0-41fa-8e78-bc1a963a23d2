"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  ShoppingCart,
  Package,
  MapPin,
  Trash2,
  Plus,
  Minus,
  AlertCircle,
  Loader2,
  PackageOpen,
} from "lucide-react";
import type { ItemPickingCart, PickedItem } from "@quildora/types";
import { PalletBuildingModal } from "./PalletBuildingModal";

interface PickedItemsCartProps {
  cart: ItemPickingCart;
  onUpdateQuantity: (
    itemId: string,
    sourcePalletId: string | undefined,
    newQuantity: number
  ) => void;
  onRemoveItem: (itemId: string, sourcePalletId: string | undefined) => void;
  onCreateShipment: () => void;
  onBuildPalletAndShip?: () => void;
  isCreatingShipment: boolean;
  isBuildingPallet?: boolean;
}

interface CartItemProps {
  item: PickedItem;
  onUpdateQuantity: (newQuantity: number) => void;
  onRemove: () => void;
}

function CartItem({ item, onUpdateQuantity, onRemove }: CartItemProps) {
  const [quantity, setQuantity] = useState(item.quantity);
  const [isUpdating, setIsUpdating] = useState(false);

  const handleQuantityChange = async (newQuantity: number) => {
    if (newQuantity <= 0) {
      onRemove();
      return;
    }

    setIsUpdating(true);
    setQuantity(newQuantity);

    try {
      // Brief delay for visual feedback
      await new Promise((resolve) => setTimeout(resolve, 100));
      onUpdateQuantity(newQuantity);
    } finally {
      setIsUpdating(false);
    }
  };

  const handleInputChange = (value: string) => {
    const num = parseInt(value) || 0;
    if (num >= 0) {
      setQuantity(num);
    }
  };

  const handleInputBlur = () => {
    handleQuantityChange(quantity);
  };

  const handleIncrement = () => {
    handleQuantityChange(quantity + 1);
  };

  const handleDecrement = () => {
    handleQuantityChange(quantity - 1);
  };

  return (
    <div className="p-3 border rounded-lg space-y-3">
      {/* Item Header */}
      <div className="flex items-start justify-between">
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-1">
            <Package className="h-4 w-4 text-blue-600 flex-shrink-0" />
            <h4 className="font-medium truncate">{item.itemName}</h4>
          </div>
          {item.sku && (
            <Badge variant="outline" className="text-xs mb-2">
              {item.sku}
            </Badge>
          )}
          <div className="space-y-1 text-xs text-muted-foreground">
            {item.locationName && (
              <div className="flex items-center gap-1">
                <MapPin className="h-3 w-3" />
                <span>{item.locationName}</span>
              </div>
            )}
            {item.sourcePalletBarcode && (
              <div className="flex items-center gap-1">
                <Package className="h-3 w-3" />
                <span>Pallet: {item.sourcePalletBarcode}</span>
              </div>
            )}
          </div>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={onRemove}
          className="h-6 w-6 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
        >
          <Trash2 className="h-3 w-3" />
        </Button>
      </div>

      {/* Quantity Controls */}
      <div className="flex items-center gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={handleDecrement}
          disabled={isUpdating}
          className="h-7 w-7 p-0 shrink-0"
        >
          <Minus className="h-3 w-3" />
        </Button>

        <Input
          type="number"
          value={quantity}
          onChange={(e) => handleInputChange(e.target.value)}
          onBlur={handleInputBlur}
          onKeyDown={(e) => {
            if (e.key === "Enter") {
              handleInputBlur();
            }
          }}
          min={1}
          className="text-center h-7 w-16 text-sm"
          disabled={isUpdating}
        />

        <Button
          variant="outline"
          size="sm"
          onClick={handleIncrement}
          disabled={isUpdating}
          className="h-7 w-7 p-0 shrink-0"
        >
          <Plus className="h-3 w-3" />
        </Button>

        <div className="ml-auto text-sm">
          <span className="font-medium">{quantity}</span>
          <span className="text-muted-foreground ml-1">
            {item.unitOfMeasure}
          </span>
        </div>

        {isUpdating && (
          <Loader2 className="h-3 w-3 animate-spin text-muted-foreground" />
        )}
      </div>
    </div>
  );
}

export function PickedItemsCart({
  cart,
  onUpdateQuantity,
  onRemoveItem,
  onCreateShipment,
  onBuildPalletAndShip,
  isCreatingShipment,
  isBuildingPallet = false,
}: PickedItemsCartProps) {
  const [showPalletModal, setShowPalletModal] = useState(false);
  const isEmpty = cart.items.length === 0;

  const handleUpdateQuantity = (item: PickedItem) => (newQuantity: number) => {
    onUpdateQuantity(item.itemId, item.sourcePalletId, newQuantity);
  };

  const handleRemoveItem = (item: PickedItem) => () => {
    onRemoveItem(item.itemId, item.sourcePalletId);
  };

  if (isEmpty) {
    return (
      <div className="text-center py-8">
        <ShoppingCart className="h-12 w-12 mx-auto mb-4 text-muted-foreground opacity-50" />
        <h3 className="font-medium mb-2">Cart is Empty</h3>
        <p className="text-sm text-muted-foreground">
          Search for items and add them to your cart to create a shipment.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Cart Items */}
      <div className="space-y-3 max-h-96 overflow-y-auto">
        {cart.items.map((item, index) => (
          <CartItem
            key={`${item.itemId}-${
              item.sourcePalletId || "no-pallet"
            }-${index}`}
            item={item}
            onUpdateQuantity={handleUpdateQuantity(item)}
            onRemove={handleRemoveItem(item)}
          />
        ))}
      </div>

      <Separator />

      {/* Cart Summary */}
      <div className="space-y-2">
        <div className="flex justify-between text-sm">
          <span className="text-muted-foreground">Total Items:</span>
          <span className="font-medium">{cart.totalItems}</span>
        </div>
        <div className="flex justify-between text-sm">
          <span className="text-muted-foreground">Total Quantity:</span>
          <span className="font-medium">{cart.totalQuantity}</span>
        </div>
      </div>

      <Separator />

      {/* Action Buttons */}
      <div className="space-y-2">
        {/* Direct Shipment Button */}
        <Button
          onClick={onCreateShipment}
          disabled={isEmpty || isCreatingShipment || isBuildingPallet}
          className="w-full bg-green-600 hover:bg-green-700 h-10"
        >
          {isCreatingShipment ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Creating Shipment...
            </>
          ) : (
            <>
              <ShoppingCart className="h-4 w-4 mr-2" />
              Ship Items Directly ({cart.totalItems})
            </>
          )}
        </Button>

        {/* Build Pallet & Ship Button */}
        {onBuildPalletAndShip && (
          <Button
            onClick={() => setShowPalletModal(true)}
            disabled={isEmpty || isCreatingShipment || isBuildingPallet}
            variant="outline"
            className="w-full h-10 border-blue-200 text-blue-700 hover:bg-blue-50"
          >
            {isBuildingPallet ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Building Pallet...
              </>
            ) : (
              <>
                <PackageOpen className="h-4 w-4 mr-2" />
                Build Pallet & Ship ({cart.totalItems})
              </>
            )}
          </Button>
        )}

        {/* Large shipment warning */}
        {cart.items.length > 3 && (
          <div className="flex items-center gap-1 text-xs text-amber-600 bg-amber-50 p-2 rounded">
            <AlertCircle className="h-3 w-3" />
            <span>
              Large shipment - consider building a pallet for better
              organization
            </span>
          </div>
        )}
      </div>

      {/* Destination Preview */}
      {(cart.destination || cart.destinationCode) && (
        <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <h4 className="text-sm font-medium mb-1">Destination</h4>
          <div className="text-sm">
            {cart.destination && <div>{cart.destination}</div>}
            {cart.destinationCode && (
              <div className="text-muted-foreground">
                Code: {cart.destinationCode}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Pallet Building Modal */}
      <PalletBuildingModal
        open={showPalletModal}
        onOpenChange={setShowPalletModal}
        pickedItems={cart.items}
        destination={cart.destination}
        destinationCode={cart.destinationCode}
        onPalletCreated={(pallet) => {
          setShowPalletModal(false);
          onBuildPalletAndShip?.();
        }}
      />
    </div>
  );
}
