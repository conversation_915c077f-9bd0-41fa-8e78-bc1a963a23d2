"use client";

import { useState } from "react";
import { useSuspenseQuery } from "@tanstack/react-query";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Truck,
  Package,
  Search,
  Filter,
  FileText,
  Calendar,
  MapPin,
} from "lucide-react";
import { useAuth } from "@/components/providers/auth-provider";
import { useWarehouse } from "@/components/providers/warehouse-provider";
import { fetchWithAuth } from "@/lib/api";
import { useDebounce } from "@/hooks/useDebounce";
import type {
  ShipmentSearchResponse,
  ShipmentStatus,
  ShipmentSearchResult,
} from "@quildora/types";

interface ShipmentStatusDashboardProps {
  onViewPackingList?: (shipmentId: string) => void;
  onReleaseInventory?: (shipmentId: string) => void;
}

// API function for searching shipments
const searchShipments = async (
  params: {
    status?: ShipmentStatus;
    destination?: string;
    warehouseId?: string;
    limit?: number;
    offset?: number;
  },
  token: string | null
): Promise<ShipmentSearchResponse> => {
  const searchParams = new URLSearchParams();

  if (params.status) searchParams.append("status", params.status);
  if (params.destination)
    searchParams.append("destination", params.destination);
  if (params.warehouseId)
    searchParams.append("warehouseId", params.warehouseId);
  if (params.limit) searchParams.append("limit", params.limit.toString());
  if (params.offset) searchParams.append("offset", params.offset.toString());

  return fetchWithAuth(`/api/shipments/search?${searchParams.toString()}`, {
    token,
  });
};

// Status badge styling
const getStatusBadge = (status: ShipmentStatus) => {
  const styles = {
    PREPARING: "bg-yellow-100 text-yellow-800 border-yellow-200",
    PACKED: "bg-blue-100 text-blue-800 border-blue-200",
    SHIPPED: "bg-green-100 text-green-800 border-green-200",
    DELIVERED: "bg-purple-100 text-purple-800 border-purple-200",
    CANCELLED: "bg-red-100 text-red-800 border-red-200",
  };

  return (
    <Badge variant="outline" className={styles[status]}>
      {status.toLowerCase()}
    </Badge>
  );
};

export function ShipmentStatusDashboard({
  onViewPackingList,
  onReleaseInventory,
}: ShipmentStatusDashboardProps) {
  const { appToken } = useAuth();
  const { currentWarehouse } = useWarehouse();

  const [statusFilter, setStatusFilter] = useState<ShipmentStatus | "all">(
    "all"
  );
  const [destinationSearch, setDestinationSearch] = useState("");
  const [currentPage, setCurrentPage] = useState(0);

  const debouncedDestination = useDebounce(destinationSearch, 300);
  const pageSize = 20;

  // Fetch shipments with filters
  const { data: searchResults } = useSuspenseQuery<
    ShipmentSearchResponse,
    Error
  >({
    queryKey: [
      "shipments-search",
      currentWarehouse?.id,
      statusFilter,
      debouncedDestination,
      currentPage,
    ],
    queryFn: () =>
      searchShipments(
        {
          status: statusFilter === "all" ? undefined : statusFilter,
          destination: debouncedDestination || undefined,
          warehouseId: currentWarehouse?.id,
          limit: pageSize,
          offset: currentPage * pageSize,
        },
        appToken
      ),
    staleTime: 30000, // Cache for 30 seconds
  });

  const shipments = searchResults?.shipments || [];
  const pagination = searchResults?.pagination;
  const filters = searchResults?.filters;

  const handleNextPage = () => {
    if (pagination?.hasMore) {
      setCurrentPage((prev) => prev + 1);
    }
  };

  const handlePrevPage = () => {
    if (currentPage > 0) {
      setCurrentPage((prev) => prev - 1);
    }
  };

  const handleStatusFilter = (value: string) => {
    setStatusFilter(value as ShipmentStatus | "all");
    setCurrentPage(0); // Reset to first page
  };

  const handleDestinationSearch = (value: string) => {
    setDestinationSearch(value);
    setCurrentPage(0); // Reset to first page
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Truck className="h-5 w-5 text-blue-600" />
            Shipment Dashboard
            {currentWarehouse && (
              <Badge variant="outline" className="ml-auto">
                {currentWarehouse.name}
              </Badge>
            )}
          </CardTitle>
        </CardHeader>
      </Card>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center gap-2">
            <Filter className="h-4 w-4" />
            Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Status Filter */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Status</label>
              <Select value={statusFilter} onValueChange={handleStatusFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All statuses" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All statuses</SelectItem>
                  {filters?.status.map((status) => (
                    <SelectItem key={status} value={status}>
                      {status.toLowerCase()}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Destination Search */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Destination</label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search destinations..."
                  value={destinationSearch}
                  onChange={(e) => handleDestinationSearch(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Shipments List */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">
            Shipments ({pagination?.total || 0})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {shipments.length === 0 ? (
            <div className="text-center py-8">
              <Package className="h-12 w-12 mx-auto mb-4 text-muted-foreground opacity-50" />
              <h3 className="font-medium mb-2">No Shipments Found</h3>
              <p className="text-sm text-muted-foreground">
                No shipments match your current filters.
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {shipments.map((shipment) => (
                <ShipmentCard
                  key={shipment.id}
                  shipment={shipment}
                  onViewPackingList={onViewPackingList}
                  onReleaseInventory={onReleaseInventory}
                />
              ))}

              {/* Pagination */}
              {pagination && pagination.total > pageSize && (
                <div className="flex items-center justify-between pt-4 border-t">
                  <div className="text-sm text-muted-foreground">
                    Showing {pagination.offset + 1} to{" "}
                    {Math.min(
                      pagination.offset + pagination.limit,
                      pagination.total
                    )}{" "}
                    of {pagination.total} shipments
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handlePrevPage}
                      disabled={currentPage === 0}
                    >
                      Previous
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleNextPage}
                      disabled={!pagination.hasMore}
                    >
                      Next
                    </Button>
                  </div>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

// Individual shipment card component
interface ShipmentCardProps {
  shipment: ShipmentSearchResult;
  onViewPackingList?: (shipmentId: string) => void;
  onReleaseInventory?: (shipmentId: string) => void;
}

function ShipmentCard({
  shipment,
  onViewPackingList,
  onReleaseInventory,
}: ShipmentCardProps) {
  return (
    <Card className="border-l-4 border-l-blue-500">
      <CardContent className="p-4">
        <div className="flex items-start justify-between">
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <h4 className="font-medium font-mono">
                {shipment.shipmentNumber}
              </h4>
              {getStatusBadge(shipment.status)}
            </div>

            {shipment.destination && (
              <div className="flex items-center gap-1 text-sm text-muted-foreground">
                <MapPin className="h-3 w-3" />
                <span>{shipment.destination}</span>
                {shipment.destinationCode && (
                  <span className="font-mono">
                    ({shipment.destinationCode})
                  </span>
                )}
              </div>
            )}

            <div className="flex items-center gap-4 text-sm text-muted-foreground">
              <div className="flex items-center gap-1">
                <Package className="h-3 w-3" />
                <span>{shipment.itemCount} items</span>
              </div>
              <div className="flex items-center gap-1">
                <Calendar className="h-3 w-3" />
                <span>{new Date(shipment.createdAt).toLocaleDateString()}</span>
              </div>
            </div>
          </div>

          <div className="flex items-center gap-2">
            {onViewPackingList && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onViewPackingList(shipment.id)}
              >
                <FileText className="h-3 w-3 mr-1" />
                Packing List
              </Button>
            )}

            {onReleaseInventory && shipment.status === "PACKED" && (
              <Button
                size="sm"
                onClick={() => onReleaseInventory(shipment.id)}
                className="bg-green-600 hover:bg-green-700"
              >
                <Truck className="h-3 w-3 mr-1" />
                Ship
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
