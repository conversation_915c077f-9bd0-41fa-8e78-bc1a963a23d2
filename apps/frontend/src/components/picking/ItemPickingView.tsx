"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Di<PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { ArrowLeft, Package, ShoppingCart, Plus } from "lucide-react";
import { useWarehouse } from "@/components/providers/warehouse-provider";
import { ItemSearchInput } from "./ItemSearchInput";
import { ItemLocationList } from "./ItemLocationList";
import { PickedItemsCart } from "./PickedItemsCart";
import { ShipmentCreationModal } from "./ShipmentCreationModal";
import { EnhancedShipmentCreationModal } from "./EnhancedShipmentCreationModal";
import { PackingListView } from "./PackingListView";
import { ShipmentStatusDashboard } from "./ShipmentStatusDashboard";
import { InventoryReleaseModal } from "./InventoryReleaseModal";
import { ItemReleaseModal } from "./ItemReleaseModal";
import { useItemPicking } from "@/hooks/useItemPicking";
import { useCartPersistence } from "@/hooks/useCartPersistence";
import { useConfetti } from "@/lib/confetti";
import { useWorkflowDataSync } from "@/hooks/useWorkflowDataSync";
import { usePrefetchingStrategy } from "@/hooks/usePrefetchingStrategy";
import type {
  ItemSearchResult,
  ItemLocationResult,
  PickedItem,
  ItemPickingCart as CartType,
} from "@quildora/types";

interface ItemPickingViewProps {
  onBack: () => void;
}

export function ItemPickingView({ onBack }: ItemPickingViewProps) {
  const { currentWarehouse } = useWarehouse();
  const [selectedItem, setSelectedItem] = useState<ItemSearchResult | null>(
    null
  );
  const [showShipmentModal, setShowShipmentModal] = useState(false);
  const [showEnhancedShipmentModal, setShowEnhancedShipmentModal] =
    useState(false);
  const [showPackingList, setShowPackingList] = useState(false);
  const [showShipmentDashboard, setShowShipmentDashboard] = useState(false);
  const [showReleaseModal, setShowReleaseModal] = useState(false);
  const [showItemReleaseModal, setShowItemReleaseModal] = useState(false);
  const [selectedShipmentId, setSelectedShipmentId] = useState<string>("");
  const [isBuildingPallet, setIsBuildingPallet] = useState(false);
  const [isReleasingItems, setIsReleasingItems] = useState(false);

  // Initialize prefetching for item locations (items are loaded via useItemsSuspense)
  const { prefetchItemLocationOnHover, prefetchAllItems } =
    usePrefetchingStrategy({
      enableItemSearch: true,
      enableLocationData: true,
    });

  // Use persistent cart for cross-session picking
  const {
    cart: persistentCart,
    addItem: addItemToCart,
    removeItem: removeItemFromCart,
    updateItemQuantity: updateCartItemQuantity,
    updateDestination: updateCartDestination,
    clearCart: clearPersistentCart,
    isLoading: isCartLoading,
  } = useCartPersistence({
    autoSave: true,
    enableCrossSession: true,
  });

  // Use custom hook for item picking operations (for shipment creation)
  const { createShipmentFromCart, isCreatingShipment } = useItemPicking();

  // Use confetti hook for celebration animations
  const { triggerPreset } = useConfetti();

  // Use workflow data synchronization
  const { syncAfterShipmentOperation, syncAfterItemOperation } =
    useWorkflowDataSync();

  const handleItemSelect = (item: ItemSearchResult) => {
    setSelectedItem(item);
  };

  const handleBackToSearch = () => {
    setSelectedItem(null);
  };

  const handleAddToCart = (
    item: ItemSearchResult,
    quantity: number,
    location: ItemLocationResult
  ) => {
    const pickedItem: PickedItem = {
      itemId: item.id,
      itemName: item.name,
      sku: item.sku,
      unitOfMeasure: item.unitOfMeasure,
      quantity,
      sourcePalletId: location.palletId,
      sourcePalletBarcode: location.palletBarcode || undefined,
      locationName: location.location.name,
    };

    addItemToCart(pickedItem);

    // Show success feedback
    // TODO: Add toast notification
  };

  const handleCreateShipment = () => {
    if (persistentCart.items.length === 0) {
      // TODO: Show error toast
      return;
    }
    setShowShipmentModal(true);
  };

  const handleCreateEnhancedShipment = () => {
    setShowEnhancedShipmentModal(true);
  };

  const handleReleaseItems = () => {
    if (persistentCart.items.length === 0) {
      // TODO: Show error toast
      return;
    }
    setShowItemReleaseModal(true);
  };

  const handleItemReleaseComplete = () => {
    // Clear the cart after successful release
    clearPersistentCart();
    setShowItemReleaseModal(false);
    setIsReleasingItems(false);
  };

  // Handle building pallet from picked items
  const handleBuildPalletAndShip = async () => {
    setIsBuildingPallet(true);
    try {
      // The PalletBuildingModal will handle pallet creation
      // After pallet is created, we can proceed with shipment
      // For now, just clear the cart as the items are now in a pallet

      // Sync data across workflows after item operations
      await syncAfterItemOperation("pick");

      clearPersistentCart();
      setSelectedItem(null);
    } finally {
      setIsBuildingPallet(false);
    }
  };

  const handleShipmentCreated = async (
    destination: string,
    destinationCode?: string,
    selectedPallets?: string[]
  ) => {
    try {
      // Update cart destination before creating shipment
      updateCartDestination(destination, destinationCode);

      // Create shipment with items and/or pallets
      if (selectedPallets && selectedPallets.length > 0) {
        // Enhanced shipment with pallets - TODO: implement this in the hook
        console.log("Creating shipment with pallets:", selectedPallets);
        await createShipmentFromCart(destination, destinationCode);
      } else {
        // Standard item-only shipment
        await createShipmentFromCart(destination, destinationCode);
      }

      // Sync data across workflows after shipment creation
      await syncAfterShipmentOperation("create");

      // Trigger celebration confetti for successful shipment creation
      triggerPreset("shipmentCreated");

      setShowShipmentModal(false);
      setShowEnhancedShipmentModal(false);
      clearPersistentCart();
      setSelectedItem(null);

      // Show shipment dashboard after a brief delay to enjoy the confetti
      setTimeout(() => {
        setShowShipmentDashboard(true);
      }, 2000);
    } catch (error) {
      console.error("Failed to create shipment:", error);
    }
  };

  const handleViewPackingList = (shipmentId: string) => {
    setSelectedShipmentId(shipmentId);
    setShowPackingList(true);
  };

  const handleReleaseInventory = (shipmentId: string) => {
    setSelectedShipmentId(shipmentId);
    setShowReleaseModal(true);
  };

  const handleReleaseSuccess = () => {
    setShowReleaseModal(false);
    setSelectedShipmentId("");
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader className="pb-4">
          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
            <div className="flex items-center gap-3">
              <Button
                variant="ghost"
                size="sm"
                onClick={onBack}
                className="h-10 w-10 p-0 min-h-[44px] touch-manipulation"
              >
                <ArrowLeft className="h-4 w-4" />
              </Button>
              <div className="flex items-center gap-2">
                <Package className="h-5 w-5 text-blue-600" />
                <CardTitle className="text-base sm:text-lg">
                  Item-Based Picking
                </CardTitle>
              </div>
            </div>
            <div className="flex flex-wrap items-center gap-2 w-full sm:w-auto">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowShipmentDashboard(true)}
                className="min-h-[44px] touch-manipulation px-3 py-2"
              >
                <Package className="h-4 w-4 mr-2" />
                <span className="hidden sm:inline">View Shipments</span>
                <span className="sm:hidden">Shipments</span>
              </Button>
              <Badge variant="outline" className="text-sm px-2 py-1">
                {currentWarehouse?.name}
              </Badge>
              {persistentCart.totalItems > 0 && (
                <div className="flex flex-wrap gap-2">
                  <Button
                    onClick={handleCreateShipment}
                    disabled={isCreatingShipment}
                    className="bg-green-600 hover:bg-green-700 min-h-[44px] touch-manipulation px-3 py-2"
                  >
                    <ShoppingCart className="h-4 w-4 mr-2" />
                    <span className="hidden sm:inline">
                      Ship Items ({persistentCart.totalItems})
                    </span>
                    <span className="sm:hidden">
                      Ship ({persistentCart.totalItems})
                    </span>
                  </Button>
                  <Button
                    onClick={handleCreateEnhancedShipment}
                    disabled={isCreatingShipment}
                    variant="outline"
                    className="min-h-[44px] touch-manipulation px-3 py-2 border-blue-200 text-blue-700 hover:bg-blue-50"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    <span className="hidden sm:inline">Add Pallets</span>
                    <span className="sm:hidden">+Pallets</span>
                  </Button>
                </div>
              )}
            </div>
          </div>
        </CardHeader>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 lg:gap-6">
        {/* Left Column - Item Search and Locations */}
        <div className="lg:col-span-2 space-y-4 lg:space-y-6">
          {/* Item Search */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base flex items-center gap-2">
                <Package className="h-4 w-4" />
                Search Items
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ItemSearchInput
                onItemSelect={handleItemSelect}
                selectedItem={selectedItem}
              />
            </CardContent>
          </Card>

          {/* Item Locations */}
          {selectedItem && (
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-base">
                    Locations for {selectedItem.name}
                  </CardTitle>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleBackToSearch}
                    className="text-muted-foreground"
                  >
                    Back to Search
                  </Button>
                </div>
                {selectedItem.sku && (
                  <p className="text-sm text-muted-foreground">
                    SKU: {selectedItem.sku}
                  </p>
                )}
              </CardHeader>
              <CardContent>
                <ItemLocationList
                  item={selectedItem}
                  warehouseId={currentWarehouse?.id || ""}
                  onAddToCart={handleAddToCart}
                />
              </CardContent>
            </Card>
          )}
        </div>

        {/* Right Column - Picked Items Cart */}
        <div className="space-y-6">
          <Card className="sticky top-6">
            <CardHeader>
              <CardTitle className="text-base flex items-center gap-2">
                <ShoppingCart className="h-4 w-4" />
                Picked Items
                {persistentCart.totalItems > 0 && (
                  <Badge variant="secondary" className="ml-auto">
                    {persistentCart.totalItems}
                  </Badge>
                )}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <PickedItemsCart
                cart={persistentCart}
                onUpdateQuantity={updateCartItemQuantity}
                onRemoveItem={removeItemFromCart}
                onCreateShipment={handleCreateShipment}
                onReleaseItems={handleReleaseItems}
                onBuildPalletAndShip={handleBuildPalletAndShip}
                isCreatingShipment={isCreatingShipment}
                isReleasingItems={isReleasingItems}
                isBuildingPallet={isBuildingPallet}
              />
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Shipment Creation Modal */}
      <ShipmentCreationModal
        isOpen={showShipmentModal}
        onClose={() => setShowShipmentModal(false)}
        onCreateShipment={handleShipmentCreated}
        cart={persistentCart}
        isCreating={isCreatingShipment}
      />

      {/* Enhanced Shipment Creation Modal */}
      <EnhancedShipmentCreationModal
        isOpen={showEnhancedShipmentModal}
        onClose={() => setShowEnhancedShipmentModal(false)}
        onCreateShipment={handleShipmentCreated}
        cart={persistentCart}
        isCreating={isCreatingShipment}
      />

      {/* Item Release Modal */}
      <ItemReleaseModal
        isOpen={showItemReleaseModal}
        onClose={() => setShowItemReleaseModal(false)}
        onReleaseComplete={handleItemReleaseComplete}
        cart={persistentCart}
        isReleasing={isReleasingItems}
      />

      {/* Packing List Modal */}
      {showPackingList && selectedShipmentId && (
        <Dialog open={showPackingList} onOpenChange={setShowPackingList}>
          <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
            <PackingListView
              shipmentId={selectedShipmentId}
              onClose={() => setShowPackingList(false)}
            />
          </DialogContent>
        </Dialog>
      )}

      {/* Shipment Dashboard Modal */}
      {showShipmentDashboard && (
        <Dialog
          open={showShipmentDashboard}
          onOpenChange={setShowShipmentDashboard}
        >
          <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Shipment Management</DialogTitle>
            </DialogHeader>
            <ShipmentStatusDashboard
              onViewPackingList={handleViewPackingList}
              onReleaseInventory={handleReleaseInventory}
            />
          </DialogContent>
        </Dialog>
      )}

      {/* Inventory Release Modal */}
      {showReleaseModal && selectedShipmentId && (
        <InventoryReleaseModal
          isOpen={showReleaseModal}
          onClose={() => setShowReleaseModal(false)}
          shipmentId={selectedShipmentId}
          shipmentNumber={selectedShipmentId}
          onSuccess={handleReleaseSuccess}
        />
      )}
    </div>
  );
}
