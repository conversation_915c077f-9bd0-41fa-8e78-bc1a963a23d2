"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Di<PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { ArrowLeft, Package, ShoppingCart, Plus } from "lucide-react";
import { useWarehouse } from "@/components/providers/warehouse-provider";
import { ItemSearchInput } from "./ItemSearchInput";
import { ItemLocationList } from "./ItemLocationList";
import { PickedItemsCart } from "./PickedItemsCart";
import { ShipmentCreationModal } from "./ShipmentCreationModal";
import { PackingListView } from "./PackingListView";
import { ShipmentStatusDashboard } from "./ShipmentStatusDashboard";
import { InventoryReleaseModal } from "./InventoryReleaseModal";
import { useItemPicking } from "@/hooks/useItemPicking";
import { useCartPersistence } from "@/hooks/useCartPersistence";
import { usePrefetchingStrategy } from "@/hooks/usePrefetchingStrategy";
import type {
  ItemSearchResult,
  ItemLocationResult,
  PickedItem,
  ItemPickingCart as CartType,
} from "@quildora/types";

interface ItemPickingViewProps {
  onBack: () => void;
}

export function ItemPickingView({ onBack }: ItemPickingViewProps) {
  const { currentWarehouse } = useWarehouse();
  const [selectedItem, setSelectedItem] = useState<ItemSearchResult | null>(
    null
  );
  const [showShipmentModal, setShowShipmentModal] = useState(false);
  const [showPackingList, setShowPackingList] = useState(false);
  const [showShipmentDashboard, setShowShipmentDashboard] = useState(false);
  const [showReleaseModal, setShowReleaseModal] = useState(false);
  const [selectedShipmentId, setSelectedShipmentId] = useState<string>("");
  const [isBuildingPallet, setIsBuildingPallet] = useState(false);

  // Initialize prefetching for item search and locations
  const { prefetchItemLocationOnHover, prefetchItemSearch } =
    usePrefetchingStrategy({
      enableItemSearch: true,
      enableLocationData: true,
    });

  // Use persistent cart for cross-session picking
  const {
    cart: persistentCart,
    addItem: addItemToCart,
    removeItem: removeItemFromCart,
    updateItemQuantity: updateCartItemQuantity,
    updateDestination: updateCartDestination,
    clearCart: clearPersistentCart,
    isLoading: isCartLoading,
  } = useCartPersistence({
    autoSave: true,
    enableCrossSession: true,
  });

  // Use custom hook for item picking operations (for shipment creation)
  const { createShipmentFromCart, isCreatingShipment } = useItemPicking();

  const handleItemSelect = (item: ItemSearchResult) => {
    setSelectedItem(item);
  };

  const handleBackToSearch = () => {
    setSelectedItem(null);
  };

  const handleAddToCart = (
    item: ItemSearchResult,
    quantity: number,
    location: ItemLocationResult
  ) => {
    const pickedItem: PickedItem = {
      itemId: item.id,
      itemName: item.name,
      sku: item.sku,
      unitOfMeasure: item.unitOfMeasure,
      quantity,
      sourcePalletId: location.palletId,
      sourcePalletBarcode: location.palletBarcode || undefined,
      locationName: location.location.name,
    };

    addItemToCart(pickedItem);

    // Show success feedback
    // TODO: Add toast notification
  };

  const handleCreateShipment = () => {
    if (persistentCart.items.length === 0) {
      // TODO: Show error toast
      return;
    }
    setShowShipmentModal(true);
  };

  // Handle building pallet from picked items
  const handleBuildPalletAndShip = async () => {
    setIsBuildingPallet(true);
    try {
      // The PalletBuildingModal will handle pallet creation
      // After pallet is created, we can proceed with shipment
      // For now, just clear the cart as the items are now in a pallet
      clearPersistentCart();
      setSelectedItem(null);
    } finally {
      setIsBuildingPallet(false);
    }
  };

  const handleShipmentCreated = async (
    destination: string,
    destinationCode?: string
  ) => {
    try {
      // Update cart destination before creating shipment
      updateCartDestination(destination, destinationCode);
      await createShipmentFromCart(destination, destinationCode);
      setShowShipmentModal(false);
      clearPersistentCart();
      setSelectedItem(null);
      // Show shipment dashboard after creation
      setShowShipmentDashboard(true);
    } catch (error) {
      console.error("Failed to create shipment:", error);
    }
  };

  const handleViewPackingList = (shipmentId: string) => {
    setSelectedShipmentId(shipmentId);
    setShowPackingList(true);
  };

  const handleReleaseInventory = (shipmentId: string) => {
    setSelectedShipmentId(shipmentId);
    setShowReleaseModal(true);
  };

  const handleReleaseSuccess = () => {
    setShowReleaseModal(false);
    setSelectedShipmentId("");
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Button
                variant="ghost"
                size="sm"
                onClick={onBack}
                className="h-8 w-8 p-0"
              >
                <ArrowLeft className="h-4 w-4" />
              </Button>
              <div className="flex items-center gap-2">
                <Package className="h-5 w-5 text-blue-600" />
                <CardTitle className="text-lg">Item-Based Picking</CardTitle>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowShipmentDashboard(true)}
              >
                <Package className="h-4 w-4 mr-2" />
                View Shipments
              </Button>
              <Badge variant="outline" className="text-sm">
                {currentWarehouse?.name}
              </Badge>
              {persistentCart.totalItems > 0 && (
                <Button
                  onClick={handleCreateShipment}
                  disabled={isCreatingShipment}
                  className="bg-green-600 hover:bg-green-700"
                >
                  <ShoppingCart className="h-4 w-4 mr-2" />
                  Create Shipment ({persistentCart.totalItems})
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - Item Search and Locations */}
        <div className="lg:col-span-2 space-y-6">
          {/* Item Search */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base flex items-center gap-2">
                <Package className="h-4 w-4" />
                Search Items
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ItemSearchInput
                onItemSelect={handleItemSelect}
                selectedItem={selectedItem}
                warehouseId={currentWarehouse?.id}
              />
            </CardContent>
          </Card>

          {/* Item Locations */}
          {selectedItem && (
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-base">
                    Locations for {selectedItem.name}
                  </CardTitle>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleBackToSearch}
                    className="text-muted-foreground"
                  >
                    Back to Search
                  </Button>
                </div>
                {selectedItem.sku && (
                  <p className="text-sm text-muted-foreground">
                    SKU: {selectedItem.sku}
                  </p>
                )}
              </CardHeader>
              <CardContent>
                <ItemLocationList
                  item={selectedItem}
                  warehouseId={currentWarehouse?.id || ""}
                  onAddToCart={handleAddToCart}
                />
              </CardContent>
            </Card>
          )}
        </div>

        {/* Right Column - Picked Items Cart */}
        <div className="space-y-6">
          <Card className="sticky top-6">
            <CardHeader>
              <CardTitle className="text-base flex items-center gap-2">
                <ShoppingCart className="h-4 w-4" />
                Picked Items
                {persistentCart.totalItems > 0 && (
                  <Badge variant="secondary" className="ml-auto">
                    {persistentCart.totalItems}
                  </Badge>
                )}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <PickedItemsCart
                cart={persistentCart}
                onUpdateQuantity={updateCartItemQuantity}
                onRemoveItem={removeItemFromCart}
                onCreateShipment={handleCreateShipment}
                onBuildPalletAndShip={handleBuildPalletAndShip}
                isCreatingShipment={isCreatingShipment}
                isBuildingPallet={isBuildingPallet}
              />
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Shipment Creation Modal */}
      <ShipmentCreationModal
        isOpen={showShipmentModal}
        onClose={() => setShowShipmentModal(false)}
        onCreateShipment={handleShipmentCreated}
        cart={persistentCart}
        isCreating={isCreatingShipment}
      />

      {/* Packing List Modal */}
      {showPackingList && selectedShipmentId && (
        <Dialog open={showPackingList} onOpenChange={setShowPackingList}>
          <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
            <PackingListView
              shipmentId={selectedShipmentId}
              onClose={() => setShowPackingList(false)}
            />
          </DialogContent>
        </Dialog>
      )}

      {/* Shipment Dashboard Modal */}
      {showShipmentDashboard && (
        <Dialog
          open={showShipmentDashboard}
          onOpenChange={setShowShipmentDashboard}
        >
          <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Shipment Management</DialogTitle>
            </DialogHeader>
            <ShipmentStatusDashboard
              onViewPackingList={handleViewPackingList}
              onReleaseInventory={handleReleaseInventory}
            />
          </DialogContent>
        </Dialog>
      )}

      {/* Inventory Release Modal */}
      {showReleaseModal && selectedShipmentId && (
        <InventoryReleaseModal
          isOpen={showReleaseModal}
          onClose={() => setShowReleaseModal(false)}
          shipmentId={selectedShipmentId}
          shipmentNumber={selectedShipmentId}
          onSuccess={handleReleaseSuccess}
        />
      )}
    </div>
  );
}
