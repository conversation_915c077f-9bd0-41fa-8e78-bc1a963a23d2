"use client";

import { useState } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { ArrowLeft, Package, ShoppingCart, Plus } from "lucide-react";
import { useWarehouse } from "@/components/providers/warehouse-provider";
import { ItemSearchInput } from "./ItemSearchInput";
import { ItemLocationList } from "./ItemLocationList";
import { PickedItemsCart } from "./PickedItemsCart";
import { ShipmentCreationModal } from "./ShipmentCreationModal";
import { useItemPicking } from "@/hooks/useItemPicking";
import type { 
  ItemSearchResult, 
  ItemLocationResult, 
  PickedItem,
  ItemPickingCart as CartType 
} from "@quildora/types";

interface ItemPickingViewProps {
  onBack: () => void;
}

export function ItemPickingView({ onBack }: ItemPickingViewProps) {
  const { currentWarehouse } = useWarehouse();
  const [selectedItem, setSelectedItem] = useState<ItemSearchResult | null>(null);
  const [showShipmentModal, setShowShipmentModal] = useState(false);

  // Use custom hook for item picking operations
  const {
    cart,
    addItemToCart,
    removeItemFromCart,
    updateItemQuantity,
    clearCart,
    createShipmentFromCart,
    isCreatingShipment,
  } = useItemPicking();

  const handleItemSelect = (item: ItemSearchResult) => {
    setSelectedItem(item);
  };

  const handleBackToSearch = () => {
    setSelectedItem(null);
  };

  const handleAddToCart = (
    item: ItemSearchResult,
    quantity: number,
    location: ItemLocationResult
  ) => {
    const pickedItem: PickedItem = {
      itemId: item.id,
      itemName: item.name,
      sku: item.sku,
      unitOfMeasure: item.unitOfMeasure,
      quantity,
      sourcePalletId: location.palletId,
      sourcePalletBarcode: location.palletBarcode,
      locationName: location.location.name,
    };

    addItemToCart(pickedItem);
    
    // Show success feedback
    // TODO: Add toast notification
  };

  const handleCreateShipment = () => {
    if (cart.items.length === 0) {
      // TODO: Show error toast
      return;
    }
    setShowShipmentModal(true);
  };

  const handleShipmentCreated = async (destination: string, destinationCode?: string) => {
    try {
      await createShipmentFromCart(destination, destinationCode);
      setShowShipmentModal(false);
      clearCart();
      // TODO: Show success toast and confetti
      // TODO: Navigate to shipment details or back to main view
    } catch (error) {
      // TODO: Show error toast
      console.error("Failed to create shipment:", error);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Button
                variant="ghost"
                size="sm"
                onClick={onBack}
                className="h-8 w-8 p-0"
              >
                <ArrowLeft className="h-4 w-4" />
              </Button>
              <div className="flex items-center gap-2">
                <Package className="h-5 w-5 text-blue-600" />
                <CardTitle className="text-lg">Item-Based Picking</CardTitle>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="text-sm">
                {currentWarehouse?.name}
              </Badge>
              {cart.totalItems > 0 && (
                <Button
                  onClick={handleCreateShipment}
                  disabled={isCreatingShipment}
                  className="bg-green-600 hover:bg-green-700"
                >
                  <ShoppingCart className="h-4 w-4 mr-2" />
                  Create Shipment ({cart.totalItems})
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - Item Search and Locations */}
        <div className="lg:col-span-2 space-y-6">
          {/* Item Search */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base flex items-center gap-2">
                <Package className="h-4 w-4" />
                Search Items
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ItemSearchInput
                onItemSelect={handleItemSelect}
                selectedItem={selectedItem}
                warehouseId={currentWarehouse?.id}
              />
            </CardContent>
          </Card>

          {/* Item Locations */}
          {selectedItem && (
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-base">
                    Locations for {selectedItem.name}
                  </CardTitle>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleBackToSearch}
                    className="text-muted-foreground"
                  >
                    Back to Search
                  </Button>
                </div>
                {selectedItem.sku && (
                  <p className="text-sm text-muted-foreground">
                    SKU: {selectedItem.sku}
                  </p>
                )}
              </CardHeader>
              <CardContent>
                <ItemLocationList
                  item={selectedItem}
                  warehouseId={currentWarehouse?.id || ""}
                  onAddToCart={handleAddToCart}
                />
              </CardContent>
            </Card>
          )}
        </div>

        {/* Right Column - Picked Items Cart */}
        <div className="space-y-6">
          <Card className="sticky top-6">
            <CardHeader>
              <CardTitle className="text-base flex items-center gap-2">
                <ShoppingCart className="h-4 w-4" />
                Picked Items
                {cart.totalItems > 0 && (
                  <Badge variant="secondary" className="ml-auto">
                    {cart.totalItems}
                  </Badge>
                )}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <PickedItemsCart
                cart={cart}
                onUpdateQuantity={updateItemQuantity}
                onRemoveItem={removeItemFromCart}
                onCreateShipment={handleCreateShipment}
                isCreatingShipment={isCreatingShipment}
              />
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Shipment Creation Modal */}
      <ShipmentCreationModal
        isOpen={showShipmentModal}
        onClose={() => setShowShipmentModal(false)}
        onCreateShipment={handleShipmentCreated}
        cart={cart}
        isCreating={isCreatingShipment}
      />
    </div>
  );
}
