"use client";

import { useState, useCallback, useRef, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Package, Search, List, ArrowRight, Sparkles } from "lucide-react";

export type PickingMode = "pallet" | "item" | "picklist";

interface PickingModeSelectorProps {
  currentMode: PickingMode;
  onModeChange: (mode: PickingMode) => void;
}

export function PickingModeSelector({
  currentMode,
  onModeChange,
}: PickingModeSelectorProps) {
  const [hoveredMode, setHoveredMode] = useState<PickingMode | null>(null);
  const [focusedMode, setFocusedMode] = useState<PickingMode | null>(null);
  const cardRefs = useRef<Record<PickingMode, HTMLDivElement | null>>({
    pallet: null,
    item: null,
    picklist: null,
  });

  const modes = [
    {
      id: "pallet" as PickingMode,
      title: "<PERSON><PERSON><PERSON> Picking",
      description: "Pick entire pallets for shipment",
      icon: Package,
      color: "blue",
      isDefault: true,
    },
    {
      id: "item" as PickingMode,
      title: "Item Picking",
      description: "Search and pick individual items",
      icon: Search,
      color: "green",
      isNew: true,
    },
    {
      id: "picklist" as PickingMode,
      title: "Picklist Mode",
      description: "Follow structured picking lists",
      icon: List,
      color: "purple",
      isDefault: true,
    },
  ];

  // Keyboard navigation handler
  const handleKeyDown = useCallback(
    (event: React.KeyboardEvent, mode: PickingMode) => {
      const currentIndex = modes.findIndex((m) => m.id === mode);

      switch (event.key) {
        case "Enter":
        case " ":
          event.preventDefault();
          onModeChange(mode);
          break;
        case "ArrowLeft":
        case "ArrowUp":
          event.preventDefault();
          const prevIndex =
            currentIndex > 0 ? currentIndex - 1 : modes.length - 1;
          const prevMode = modes[prevIndex].id;
          setFocusedMode(prevMode);
          cardRefs.current[prevMode]?.focus();
          break;
        case "ArrowRight":
        case "ArrowDown":
          event.preventDefault();
          const nextIndex =
            currentIndex < modes.length - 1 ? currentIndex + 1 : 0;
          const nextMode = modes[nextIndex].id;
          setFocusedMode(nextMode);
          cardRefs.current[nextMode]?.focus();
          break;
        case "Home":
          event.preventDefault();
          const firstMode = modes[0].id;
          setFocusedMode(firstMode);
          cardRefs.current[firstMode]?.focus();
          break;
        case "End":
          event.preventDefault();
          const lastMode = modes[modes.length - 1].id;
          setFocusedMode(lastMode);
          cardRefs.current[lastMode]?.focus();
          break;
      }
    },
    [modes, onModeChange]
  );

  // Handle mode selection
  const handleModeSelect = useCallback(
    (mode: PickingMode) => {
      onModeChange(mode);
      // Announce selection to screen readers
      const announcement = `${
        modes.find((m) => m.id === mode)?.title
      } selected`;
      const announcer = document.createElement("div");
      announcer.setAttribute("aria-live", "polite");
      announcer.setAttribute("aria-atomic", "true");
      announcer.className = "sr-only";
      announcer.textContent = announcement;
      document.body.appendChild(announcer);
      setTimeout(() => document.body.removeChild(announcer), 1000);
    },
    [modes, onModeChange]
  );

  const getCardStyles = (mode: (typeof modes)[0]) => {
    const isActive = currentMode === mode.id;
    const isHovered = hoveredMode === mode.id;

    // Enhanced mobile-first styles with larger touch targets
    const baseStyles =
      "transition-all duration-200 cursor-pointer border-2 min-h-[120px] md:min-h-[140px] touch-manipulation";

    if (isActive) {
      return `${baseStyles} border-${mode.color}-500 bg-${mode.color}-50 shadow-md ring-2 ring-${mode.color}-200`;
    }

    if (isHovered) {
      return `${baseStyles} border-${mode.color}-300 bg-${mode.color}-25 shadow-sm`;
    }

    return `${baseStyles} border-gray-200 hover:border-${mode.color}-200 hover:shadow-sm active:scale-95`;
  };

  const getIconStyles = (mode: (typeof modes)[0]) => {
    const isActive = currentMode === mode.id;
    return isActive
      ? `h-8 w-8 text-${mode.color}-600`
      : `h-8 w-8 text-${mode.color}-500`;
  };

  return (
    <div className="space-y-4">
      <div className="text-center">
        <h2 id="picking-mode-heading" className="text-xl font-semibold mb-2">
          Choose Picking Mode
        </h2>
        <p className="text-muted-foreground">
          Select how you want to pick items for shipment
        </p>
      </div>

      <div
        className="grid grid-cols-1 md:grid-cols-3 gap-4"
        role="radiogroup"
        aria-labelledby="picking-mode-heading"
        aria-describedby="picking-mode-instructions"
      >
        {modes.map((mode) => {
          const Icon = mode.icon;
          const isActive = currentMode === mode.id;

          return (
            <Card
              key={mode.id}
              ref={(el) => {
                cardRefs.current[mode.id] = el;
              }}
              className={getCardStyles(mode)}
              role="radio"
              aria-checked={isActive}
              aria-describedby={`mode-${mode.id}-description`}
              tabIndex={isActive ? 0 : -1}
              onMouseEnter={() => setHoveredMode(mode.id)}
              onMouseLeave={() => setHoveredMode(null)}
              onFocus={() => setFocusedMode(mode.id)}
              onBlur={() => setFocusedMode(null)}
              onKeyDown={(e) => handleKeyDown(e, mode.id)}
              onClick={() => handleModeSelect(mode.id)}
            >
              <CardContent className="p-6 text-center space-y-4">
                <div className="relative">
                  <Icon className={getIconStyles(mode)} />
                  {mode.isNew && (
                    <div className="absolute -top-2 -right-2">
                      <Badge
                        variant="secondary"
                        className="bg-green-100 text-green-700 text-xs"
                      >
                        <Sparkles className="h-3 w-3 mr-1" />
                        New
                      </Badge>
                    </div>
                  )}
                </div>

                <div className="space-y-2">
                  <h3 className="font-semibold">{mode.title}</h3>
                  <p
                    id={`mode-${mode.id}-description`}
                    className="text-sm text-muted-foreground"
                  >
                    {mode.description}
                  </p>
                </div>

                {currentMode === mode.id ? (
                  <Badge
                    variant="secondary"
                    className={`bg-${mode.color}-100 text-${mode.color}-700`}
                  >
                    Active Mode
                  </Badge>
                ) : (
                  <Button
                    variant="outline"
                    size="sm"
                    className={`border-${mode.color}-200 text-${mode.color}-600 hover:bg-${mode.color}-50`}
                  >
                    Select Mode
                    <ArrowRight className="h-3 w-3 ml-1" />
                  </Button>
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Keyboard navigation instructions */}
      <div
        id="picking-mode-instructions"
        className="text-xs text-muted-foreground text-center"
      >
        Use arrow keys to navigate, Enter or Space to select, Home/End to jump
        to first/last option
      </div>

      {/* Mode-specific tips */}
      <Card className="bg-gray-50">
        <CardContent className="p-4">
          <div className="text-sm">
            <h4 className="font-medium mb-2">
              {modes.find((m) => m.id === currentMode)?.title} Tips:
            </h4>
            <div
              className="text-muted-foreground"
              role="region"
              aria-live="polite"
            >
              {currentMode === "pallet" && (
                <ul className="space-y-1">
                  <li>• Scan pallet barcodes to add entire pallets</li>
                  <li>• Best for bulk shipments and full pallet moves</li>
                  <li>• Maintains pallet integrity during shipping</li>
                </ul>
              )}
              {currentMode === "item" && (
                <ul className="space-y-1">
                  <li>• Search for specific items across all locations</li>
                  <li>• Pick exact quantities from multiple pallets</li>
                  <li>• Perfect for mixed shipments and partial quantities</li>
                </ul>
              )}
              {currentMode === "picklist" && (
                <ul className="space-y-1">
                  <li>• Follow pre-generated picking instructions</li>
                  <li>• Optimized routes through the warehouse</li>
                  <li>• Track progress and completion status</li>
                </ul>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
