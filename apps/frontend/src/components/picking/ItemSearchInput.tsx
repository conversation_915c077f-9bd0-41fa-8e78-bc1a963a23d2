"use client";

import { useState, useEffect, useRef } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Search, Package, ArrowRight } from "lucide-react";
import { useItemsSuspense } from "@/hooks/api/useWarehouseData";
import { useItemFiltering } from "@/hooks/useItemFiltering";
import type { ItemSearchResult } from "@quildora/types";

interface ItemSearchInputProps {
  onItemSelect: (item: ItemSearchResult) => void;
  selectedItem: ItemSearchResult | null;
  placeholder?: string;
}

export function ItemSearchInput({
  onItemSelect,
  selectedItem,
  placeholder = "Search items by name or SKU...",
}: ItemSearchInputProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [isOpen, setIsOpen] = useState(false);
  const [highlightedIndex, setHighlightedIndex] = useState(-1);
  const inputRef = useRef<HTMLInputElement>(null);
  const resultsRef = useRef<HTMLDivElement>(null);

  // Fetch all items once and filter client-side for instant results
  const { data: allItems = [] } = useItemsSuspense();

  // Use client-side filtering for instant search results
  const { searchSuggestions, updateSearchQuery, isFiltering, hasResults } =
    useItemFiltering(allItems, {
      enableFuzzySearch: false,
      searchFields: ["name", "sku", "description"],
    });

  const items = searchSuggestions;

  // Handle input change with instant client-side filtering
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchQuery(value);
    updateSearchQuery(value); // Update filter instantly
    setIsOpen(value.length >= 2);
    setHighlightedIndex(-1);
  };

  // Handle item selection
  const handleItemSelect = (item: ItemSearchResult) => {
    onItemSelect(item);
    setSearchQuery(item.name);
    updateSearchQuery(""); // Clear filter
    setIsOpen(false);
    setHighlightedIndex(-1);
  };

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!isOpen || items.length === 0) return;

    switch (e.key) {
      case "ArrowDown":
        e.preventDefault();
        setHighlightedIndex((prev) => (prev < items.length - 1 ? prev + 1 : 0));
        break;
      case "ArrowUp":
        e.preventDefault();
        setHighlightedIndex((prev) => (prev > 0 ? prev - 1 : items.length - 1));
        break;
      case "Enter":
        e.preventDefault();
        if (highlightedIndex >= 0 && highlightedIndex < items.length) {
          handleItemSelect(items[highlightedIndex]);
        }
        break;
      case "Escape":
        setIsOpen(false);
        setHighlightedIndex(-1);
        inputRef.current?.blur();
        break;
    }
  };

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        resultsRef.current &&
        !resultsRef.current.contains(event.target as Node) &&
        !inputRef.current?.contains(event.target as Node)
      ) {
        setIsOpen(false);
        setHighlightedIndex(-1);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  // Clear search when selected item changes externally
  useEffect(() => {
    if (!selectedItem) {
      setSearchQuery("");
      updateSearchQuery("");
      setIsOpen(false);
    }
  }, [selectedItem, updateSearchQuery]);

  const showResults = isOpen && searchQuery.length >= 2;
  // No loading state needed with client-side filtering

  return (
    <div className="relative">
      {/* Search Input */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
        <Input
          ref={inputRef}
          type="text"
          placeholder={placeholder}
          value={searchQuery}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          onFocus={() => {
            if (searchQuery.length >= 2) {
              setIsOpen(true);
            }
          }}
          className="pl-10 h-12 text-base"
          autoComplete="off"
        />
      </div>

      {/* Search Results Dropdown */}
      {showResults && (
        <Card
          ref={resultsRef}
          className="absolute top-full left-0 right-0 z-50 mt-1 max-h-80 overflow-y-auto shadow-lg"
        >
          <CardContent className="p-0">
            {items.length === 0 ? (
              <div className="p-4 text-center text-muted-foreground">
                <Package className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p>No items found for "{searchQuery}"</p>
                <p className="text-sm">Try a different search term</p>
              </div>
            ) : (
              <div className="py-2">
                {items.map((item, index) => (
                  <button
                    key={item.id}
                    onClick={() => handleItemSelect(item)}
                    className={`w-full px-4 py-3 text-left hover:bg-muted/50 focus:bg-muted/50 focus:outline-none transition-colors ${
                      index === highlightedIndex ? "bg-muted/50" : ""
                    }`}
                    onMouseEnter={() => setHighlightedIndex(index)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <Package className="h-4 w-4 text-blue-600 flex-shrink-0" />
                          <span className="font-medium truncate">
                            {item.name}
                          </span>
                          {item.sku && (
                            <Badge variant="outline" className="text-xs">
                              {item.sku}
                            </Badge>
                          )}
                        </div>
                        {item.description && (
                          <p className="text-sm text-muted-foreground truncate">
                            {item.description}
                          </p>
                        )}
                        <div className="flex items-center gap-4 mt-1 text-xs text-muted-foreground">
                          <span>
                            {item.totalQuantity} {item.unitOfMeasure} available
                          </span>
                          <span>
                            {item.locationCount} location
                            {item.locationCount !== 1 ? "s" : ""}
                          </span>
                        </div>
                      </div>
                      <ArrowRight className="h-4 w-4 text-muted-foreground flex-shrink-0 ml-2" />
                    </div>
                  </button>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Selected Item Display */}
      {selectedItem && !isOpen && (
        <div className="mt-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Package className="h-4 w-4 text-blue-600" />
              <span className="font-medium">{selectedItem.name}</span>
              {selectedItem.sku && (
                <Badge variant="outline" className="text-xs">
                  {selectedItem.sku}
                </Badge>
              )}
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                onItemSelect(null as any); // Clear selection
                setSearchQuery("");
              }}
              className="h-6 px-2 text-xs"
            >
              Clear
            </Button>
          </div>
          <p className="text-sm text-muted-foreground mt-1">
            {selectedItem.totalQuantity} {selectedItem.unitOfMeasure} available
            in {selectedItem.locationCount} location
            {selectedItem.locationCount !== 1 ? "s" : ""}
          </p>
        </div>
      )}
    </div>
  );
}
