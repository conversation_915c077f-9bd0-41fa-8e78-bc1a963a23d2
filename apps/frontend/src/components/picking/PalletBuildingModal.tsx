"use client";

import React, { use<PERSON><PERSON>back, useEffect } from "react";
import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { toast } from "sonner";
import { Package, QrCode, RefreshCw, Loader2 } from "lucide-react";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Separator } from "@/components/ui/separator";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { DestinationAutocomplete } from "@/components/ui/destination-autocomplete";
import { useCreatePallet, useLocations } from "@/hooks/api";
import { useDestinationAutoPopulation } from "@/hooks/useDestinationAutoPopulation";
import { useConfetti } from "@/lib/confetti";
import type { PickedItem, Location } from "@quildora/types";

// Form validation schema - reusing CreatePalletDialog patterns
const formSchema = z.object({
  barcode: z.string().min(1, "Barcode is required"),
  locationId: z.string().min(1, "Location is required"),
  description: z.string().optional(),
  shipToDestination: z.string().optional(),
  destinationCode: z.string().optional(),
  items: z.array(
    z.object({
      itemId: z.string().min(1, "Item is required"),
      quantity: z.number().min(1, "Quantity must be at least 1"),
    })
  ),
});

type PalletFormData = z.infer<typeof formSchema>;

// Generate barcode using the same formula as receiving workflow
const generateBarcode = () => {
  const randomPart = Math.floor(Math.random() * 1000)
    .toString()
    .padStart(3, "0");
  const timePart = (Date.now() % 100000).toString().padStart(5, "0");
  return `${randomPart}${timePart}`;
};

interface PalletBuildingModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  pickedItems: PickedItem[];
  destination?: string;
  destinationCode?: string;
  onPalletCreated: (pallet: any) => void;
}

export function PalletBuildingModal({
  open,
  onOpenChange,
  pickedItems,
  destination,
  destinationCode,
  onPalletCreated,
}: PalletBuildingModalProps) {
  const createPalletMutation = useCreatePallet();
  const { data: locations, isLoading: isLoadingLocations } = useLocations();
  const { triggerPreset } = useConfetti();

  // Ref for the location select trigger to enable autofocus
  const locationSelectRef = React.useRef<HTMLButtonElement>(null);

  const form = useForm<PalletFormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      barcode: generateBarcode(),
      locationId: "",
      description: "",
      shipToDestination: destination || "",
      destinationCode: destinationCode || "",
      items: [],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "items",
  });

  // Use unified destination auto-population hook
  const { handleDestinationSelect, handleDestinationCodeLookup, isLookingUp } =
    useDestinationAutoPopulation({ form });

  // Convert picked items to form items when modal opens
  useEffect(() => {
    if (open && pickedItems.length > 0) {
      // Clear existing items
      form.setValue("items", []);

      // Add picked items to form
      const formItems = pickedItems.map((item) => ({
        itemId: item.itemId,
        quantity: item.quantity,
      }));

      form.setValue("items", formItems);

      // Set destination if provided
      if (destination) {
        form.setValue("shipToDestination", destination);
      }
      if (destinationCode) {
        form.setValue("destinationCode", destinationCode);
      }
    }
  }, [open, pickedItems, destination, destinationCode, form]);

  // Memoized function to regenerate barcode
  const regenerateBarcode = useCallback(() => {
    form.setValue("barcode", generateBarcode());
  }, [form]);

  // Focus location field when modal opens
  useEffect(() => {
    if (open && locationSelectRef.current) {
      setTimeout(() => {
        locationSelectRef.current?.focus();
      }, 100);
    }
  }, [open]);

  const onSubmit = useCallback(
    (values: PalletFormData) => {
      // Prepare the payload for the warehouse-aware hook
      const payload = {
        barcode: values.barcode,
        label: values.barcode, // Use barcode for label
        description:
          values.description ||
          `Pallet built from ${pickedItems.length} picked items`,
        shipToDestination: values.shipToDestination || undefined,
        destinationCode: values.destinationCode || undefined,
        locationId: values.locationId,
        status: "Stored",
        items: values.items,
      };

      createPalletMutation.mutate(payload, {
        onSuccess: (data) => {
          // Trigger celebration confetti for pallet creation
          triggerPreset("palletBuilt");

          toast.success("Pallet created successfully!", {
            description: `Pallet ${data.barcode} is ready for shipment`,
          });

          // Reset form for next use
          form.reset({
            barcode: generateBarcode(),
            locationId: "",
            description: "",
            shipToDestination: "",
            destinationCode: "",
            items: [],
          });

          onPalletCreated(data);
        },
        onError: (error) => {
          console.error("Error creating pallet:", error);
          toast.error("Failed to create pallet", {
            description:
              error instanceof Error ? error.message : "Please try again",
          });
        },
      });
    },
    [createPalletMutation, form, onPalletCreated, pickedItems.length]
  );

  const handleCancel = () => {
    form.reset();
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Package className="h-5 w-5 text-blue-600" />
            Build Pallet from Picked Items
          </DialogTitle>
          <DialogDescription>
            Create a new pallet containing your picked items. This pallet will
            be ready for shipment with proper barcode and placard.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Barcode Section */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center gap-2">
                  <QrCode className="h-4 w-4" />
                  Pallet Identification
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex gap-2">
                  <FormField
                    control={form.control}
                    name="barcode"
                    render={({ field }) => (
                      <FormItem className="flex-1">
                        <FormLabel>Barcode</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            className="font-mono text-base md:text-sm"
                            placeholder="Auto-generated barcode"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <div className="flex items-end">
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={regenerateBarcode}
                      className="h-9"
                    >
                      <RefreshCw className="h-3 w-3" />
                    </Button>
                  </div>
                </div>

                <FormField
                  control={form.control}
                  name="locationId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Storage Location *</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                        disabled={isLoadingLocations}
                      >
                        <FormControl>
                          <SelectTrigger
                            ref={locationSelectRef}
                            className="text-base md:text-sm"
                          >
                            <SelectValue placeholder="Select storage location" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {locations?.map((location: Location) => (
                            <SelectItem key={location.id} value={location.id}>
                              {location.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>

            {/* Items Section */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">
                  Items ({pickedItems.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {pickedItems.map((item, index) => (
                    <div
                      key={`${item.itemId}-${
                        item.sourcePalletId || "no-pallet"
                      }-${index}`}
                      className="flex items-center justify-between p-3 border rounded-lg bg-gray-50"
                    >
                      <div className="flex-1">
                        <div className="font-medium">{item.itemName}</div>
                        {item.sku && (
                          <Badge variant="outline" className="text-xs mt-1">
                            {item.sku}
                          </Badge>
                        )}
                        {item.sourcePalletBarcode && (
                          <div className="text-xs text-muted-foreground mt-1">
                            From: {item.sourcePalletBarcode}
                          </div>
                        )}
                      </div>
                      <div className="text-right">
                        <div className="font-medium">
                          {item.quantity} {item.unitOfMeasure}
                        </div>
                        {item.locationName && (
                          <div className="text-xs text-muted-foreground">
                            {item.locationName}
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Destination Section */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">
                  Destination (Optional)
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <FormField
                  control={form.control}
                  name="shipToDestination"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Destination Name</FormLabel>
                      <FormControl>
                        <DestinationAutocomplete
                          value={field.value || ""}
                          onValueChange={field.onChange}
                          onDestinationSelect={handleDestinationSelect}
                          placeholder="Enter destination name"
                          className="text-base md:text-sm"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="destinationCode"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Destination Code</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Input
                            {...field}
                            placeholder="Enter destination code"
                            className="text-base md:text-sm"
                            onBlur={() => {
                              if (field.value) {
                                handleDestinationCodeLookup(field.value);
                              }
                            }}
                          />
                          {isLookingUp && (
                            <Loader2 className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 animate-spin text-muted-foreground" />
                          )}
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>

            {/* Description Section */}
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description (Optional)</FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      placeholder="Add any additional notes about this pallet..."
                      className="text-base md:text-sm resize-none"
                      rows={3}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter className="gap-2">
              <Button
                type="button"
                variant="outline"
                onClick={handleCancel}
                disabled={createPalletMutation.isPending}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={createPalletMutation.isPending}
                className="bg-blue-600 hover:bg-blue-700"
              >
                {createPalletMutation.isPending ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Creating Pallet...
                  </>
                ) : (
                  <>
                    <Package className="h-4 w-4 mr-2" />
                    Create Pallet & Continue
                  </>
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
