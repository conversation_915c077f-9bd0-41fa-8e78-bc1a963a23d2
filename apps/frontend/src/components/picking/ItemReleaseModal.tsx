"use client";

import { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  <PERSON><PERSON>Title,
  DialogFooter,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Package,
  Truck,
  Loader2,
  AlertCircle,
  CheckCircle2,
} from "lucide-react";
import { useAuth } from "@/components/providers/auth-provider";
import { useWarehouse } from "@/components/providers/warehouse-provider";
import { fetchWithAuth } from "@/lib/api";
import { toast } from "sonner";
import { useConfetti } from "@/lib/confetti";
import type { ItemPickingCart, PickedItem } from "@quildora/types";

interface ItemReleaseModalProps {
  isOpen: boolean;
  onClose: () => void;
  onReleaseComplete: () => void;
  cart: ItemPickingCart;
  isReleasing: boolean;
}

interface ReleaseItemData {
  itemId: string;
  quantity: number;
  sourcePalletId?: string;
  notes?: string;
}

export function ItemReleaseModal({
  isOpen,
  onClose,
  onReleaseComplete,
  cart,
  isReleasing,
}: ItemReleaseModalProps) {
  const [releasedTo, setReleasedTo] = useState("");
  const [notes, setNotes] = useState("");
  const [error, setError] = useState<string | null>(null);

  const { appToken } = useAuth();
  const { currentWarehouse } = useWarehouse();
  const queryClient = useQueryClient();
  const { trigger: triggerConfetti } = useConfetti();

  const releaseMutation = useMutation({
    mutationFn: async (data: {
      items: ReleaseItemData[];
      releasedTo?: string;
      notes?: string;
    }) => {
      if (!appToken || !currentWarehouse?.id) {
        throw new Error("Authentication or warehouse context not available");
      }

      return fetchWithAuth("/api/items/release", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-Warehouse-ID": currentWarehouse.id,
        },
        body: JSON.stringify(data),
        token: appToken,
      });
    },
    onSuccess: () => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ["pallets"] });
      queryClient.invalidateQueries({ queryKey: ["items"] });
      queryClient.invalidateQueries({ queryKey: ["audit-logs"] });

      // Show success feedback
      toast.success("Items released successfully!");
      triggerConfetti();

      // Reset form and close
      handleClose();
      onReleaseComplete();
    },
    onError: (error: any) => {
      console.error("Release error:", error);
      setError(error.message || "Failed to release items");
      toast.error("Failed to release items");
    },
  });

  const handleClose = () => {
    setReleasedTo("");
    setNotes("");
    setError(null);
    onClose();
  };

  const handleRelease = () => {
    if (cart.items.length === 0) {
      setError("No items to release");
      return;
    }

    setError(null);

    const releaseItems: ReleaseItemData[] = cart.items.map(
      (item: PickedItem) => ({
        itemId: item.itemId,
        quantity: item.quantity,
        sourcePalletId: item.sourcePalletId,
        notes: notes.trim() || undefined,
      })
    );

    releaseMutation.mutate({
      items: releaseItems,
      releasedTo: releasedTo.trim() || undefined,
      notes: notes.trim() || undefined,
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Truck className="h-5 w-5 text-orange-600" />
            Release Items for Local Pickup
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Items Summary */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="font-medium">Items to Release</h3>
              <Badge variant="secondary">
                {cart.totalItems} {cart.totalItems === 1 ? "item" : "items"}
              </Badge>
            </div>

            <div className="space-y-2 max-h-40 overflow-y-auto">
              {cart.items.map((item: PickedItem, index: number) => (
                <div
                  key={`${item.itemId}-${
                    item.sourcePalletId || "no-pallet"
                  }-${index}`}
                  className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                >
                  <div className="flex items-center gap-3">
                    <Package className="h-4 w-4 text-gray-500" />
                    <div>
                      <p className="font-medium">{item.itemName}</p>
                      {item.sourcePalletBarcode && (
                        <p className="text-sm text-gray-500">
                          From: {item.sourcePalletBarcode}
                        </p>
                      )}
                    </div>
                  </div>
                  <Badge variant="outline">
                    {item.quantity} {item.unitOfMeasure}
                  </Badge>
                </div>
              ))}
            </div>
          </div>

          <Separator />

          {/* Release Information */}
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="releasedTo">Released To (Optional)</Label>
              <Input
                id="releasedTo"
                placeholder="Contractor name, crew leader, etc."
                value={releasedTo}
                onChange={(e) => setReleasedTo(e.target.value)}
                className="min-h-[44px]"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="notes">Notes (Optional)</Label>
              <Textarea
                id="notes"
                placeholder="Additional notes about this release..."
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                rows={3}
                className="min-h-[80px]"
              />
            </div>
          </div>

          {/* Error Display */}
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Warehouse Context */}
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <Package className="h-4 w-4" />
            <span>Releasing from: {currentWarehouse?.name}</span>
          </div>
        </div>

        <DialogFooter className="gap-2">
          <Button
            variant="outline"
            onClick={handleClose}
            disabled={isReleasing || releaseMutation.isPending}
          >
            Cancel
          </Button>
          <Button
            onClick={handleRelease}
            disabled={
              isReleasing ||
              releaseMutation.isPending ||
              cart.items.length === 0
            }
            className="bg-orange-600 hover:bg-orange-700 min-h-[44px]"
          >
            {isReleasing || releaseMutation.isPending ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Releasing Items...
              </>
            ) : (
              <>
                <CheckCircle2 className="h-4 w-4 mr-2" />
                Release {cart.totalItems}{" "}
                {cart.totalItems === 1 ? "Item" : "Items"}
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
