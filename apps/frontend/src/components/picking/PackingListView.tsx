"use client";

import { useSuspenseQuery } from "@tanstack/react-query";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { 
  FileText, 
  Package, 
  MapPin, 
  Printer, 
  Download,
  Calendar,
  Hash
} from "lucide-react";
import { useAuth } from "@/components/providers/auth-provider";
import { fetchWithAuth } from "@/lib/api";
import type { PackingListResponse } from "@quildora/types";

interface PackingListViewProps {
  shipmentId: string;
  onClose?: () => void;
}

// API function for fetching packing list
const fetchPackingList = async (
  shipmentId: string,
  token: string | null
): Promise<PackingListResponse> => {
  return fetchWithAuth(`/api/shipments/${shipmentId}/packing-list`, {
    token,
  });
};

export function PackingListView({ shipmentId, onClose }: PackingListViewProps) {
  const { appToken } = useAuth();

  // Fetch packing list data
  const { data: packingList } = useSuspenseQuery<PackingListResponse, Error>({
    queryKey: ["packing-list", shipmentId],
    queryFn: () => fetchPackingList(shipmentId, appToken),
    staleTime: 300000, // Cache for 5 minutes
  });

  const handlePrint = () => {
    window.print();
  };

  const handleDownload = () => {
    // Create a simple text version for download
    const content = generateTextPackingList(packingList);
    const blob = new Blob([content], { type: "text/plain" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `packing-list-${packingList.shipmentNumber}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6 print:space-y-4">
      {/* Header - Hidden in print */}
      <div className="flex items-center justify-between print:hidden">
        <div className="flex items-center gap-2">
          <FileText className="h-5 w-5 text-blue-600" />
          <h1 className="text-xl font-semibold">Packing List</h1>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={handleDownload}>
            <Download className="h-4 w-4 mr-2" />
            Download
          </Button>
          <Button onClick={handlePrint}>
            <Printer className="h-4 w-4 mr-2" />
            Print
          </Button>
          {onClose && (
            <Button variant="ghost" onClick={onClose}>
              Close
            </Button>
          )}
        </div>
      </div>

      {/* Packing List Document */}
      <Card className="print:shadow-none print:border-none">
        <CardHeader className="text-center border-b print:border-black">
          <CardTitle className="text-2xl font-bold">PACKING LIST</CardTitle>
          <div className="space-y-2 text-sm text-muted-foreground">
            <div className="flex items-center justify-center gap-2">
              <Hash className="h-4 w-4" />
              <span className="font-mono">{packingList.shipmentNumber}</span>
            </div>
            <div className="flex items-center justify-center gap-2">
              <Calendar className="h-4 w-4" />
              <span>Generated: {new Date(packingList.generatedAt).toLocaleString()}</span>
            </div>
          </div>
        </CardHeader>

        <CardContent className="p-6 space-y-6">
          {/* Destination Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="font-semibold mb-2 flex items-center gap-2">
                <MapPin className="h-4 w-4" />
                Destination
              </h3>
              <div className="space-y-1">
                <p className="font-medium">{packingList.destination}</p>
                {packingList.destinationCode && (
                  <p className="text-sm text-muted-foreground">
                    Code: {packingList.destinationCode}
                  </p>
                )}
              </div>
            </div>
            
            <div>
              <h3 className="font-semibold mb-2">Summary</h3>
              <div className="space-y-1 text-sm">
                <div className="flex justify-between">
                  <span>Total Items:</span>
                  <span className="font-medium">{packingList.summary.totalItems}</span>
                </div>
                <div className="flex justify-between">
                  <span>Total Quantity:</span>
                  <span className="font-medium">{packingList.summary.totalQuantity}</span>
                </div>
                <div className="flex justify-between">
                  <span>Total Pallets:</span>
                  <span className="font-medium">{packingList.summary.totalPallets}</span>
                </div>
              </div>
            </div>
          </div>

          <Separator />

          {/* Items Section */}
          {packingList.items.length > 0 && (
            <div>
              <h3 className="font-semibold mb-4 flex items-center gap-2">
                <Package className="h-4 w-4" />
                Items ({packingList.items.length})
              </h3>
              <div className="overflow-x-auto">
                <table className="w-full border-collapse border border-gray-300">
                  <thead>
                    <tr className="bg-gray-50">
                      <th className="border border-gray-300 px-3 py-2 text-left">SKU</th>
                      <th className="border border-gray-300 px-3 py-2 text-left">Item Name</th>
                      <th className="border border-gray-300 px-3 py-2 text-center">Quantity</th>
                      <th className="border border-gray-300 px-3 py-2 text-left">Unit</th>
                      <th className="border border-gray-300 px-3 py-2 text-left">Source Pallet</th>
                    </tr>
                  </thead>
                  <tbody>
                    {packingList.items.map((item, index) => (
                      <tr key={index} className="hover:bg-gray-50">
                        <td className="border border-gray-300 px-3 py-2 font-mono text-sm">
                          {item.sku || "-"}
                        </td>
                        <td className="border border-gray-300 px-3 py-2">
                          {item.name}
                        </td>
                        <td className="border border-gray-300 px-3 py-2 text-center font-medium">
                          {item.quantity}
                        </td>
                        <td className="border border-gray-300 px-3 py-2">
                          {item.unitOfMeasure}
                        </td>
                        <td className="border border-gray-300 px-3 py-2 font-mono text-sm">
                          {item.sourcePallet || "-"}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {/* Pallets Section */}
          {packingList.pallets.length > 0 && (
            <div>
              <h3 className="font-semibold mb-4 flex items-center gap-2">
                <Package className="h-4 w-4" />
                Pallets ({packingList.pallets.length})
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {packingList.pallets.map((pallet, index) => (
                  <Card key={index} className="border">
                    <CardContent className="p-4">
                      <div className="space-y-2">
                        <div className="font-medium font-mono text-sm">
                          {pallet.barcode || `Pallet ${index + 1}`}
                        </div>
                        {pallet.description && (
                          <p className="text-sm text-muted-foreground">
                            {pallet.description}
                          </p>
                        )}
                        <div className="flex justify-between text-sm">
                          <span>Items:</span>
                          <Badge variant="secondary">{pallet.itemCount}</Badge>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span>Total Qty:</span>
                          <Badge variant="outline">{pallet.totalQuantity}</Badge>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

// Helper function to generate text version for download
function generateTextPackingList(packingList: PackingListResponse): string {
  const lines = [
    "PACKING LIST",
    "=" .repeat(50),
    "",
    `Shipment Number: ${packingList.shipmentNumber}`,
    `Generated: ${new Date(packingList.generatedAt).toLocaleString()}`,
    "",
    "DESTINATION:",
    packingList.destination || "Not specified",
    packingList.destinationCode ? `Code: ${packingList.destinationCode}` : "",
    "",
    "SUMMARY:",
    `Total Items: ${packingList.summary.totalItems}`,
    `Total Quantity: ${packingList.summary.totalQuantity}`,
    `Total Pallets: ${packingList.summary.totalPallets}`,
    "",
  ];

  if (packingList.items.length > 0) {
    lines.push("ITEMS:");
    lines.push("-".repeat(50));
    packingList.items.forEach((item, index) => {
      lines.push(
        `${index + 1}. ${item.name} (${item.sku || "No SKU"})`,
        `   Quantity: ${item.quantity} ${item.unitOfMeasure}`,
        `   Source: ${item.sourcePallet || "Not specified"}`,
        ""
      );
    });
  }

  return lines.join("\n");
}
