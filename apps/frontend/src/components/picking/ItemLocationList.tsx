"use client";

import { useState } from "react";
import { useSuspenseQuery } from "@tanstack/react-query";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { 
  MapPin, 
  Package, 
  Plus, 
  Minus, 
  ShoppingCart,
  AlertCircle,
  Loader2 
} from "lucide-react";
import { useAuth } from "@/components/providers/auth-provider";
import { fetchWithAuth } from "@/lib/api";
import type { 
  ItemSearchResult, 
  ItemLocationResponse, 
  ItemLocationResult 
} from "@quildora/types";

interface ItemLocationListProps {
  item: ItemSearchResult;
  warehouseId: string;
  onAddToCart: (
    item: ItemSearchResult,
    quantity: number,
    location: ItemLocationResult
  ) => void;
}

// API function for fetching item locations
const fetchItemLocations = async (
  itemId: string,
  warehouseId: string,
  token: string | null
): Promise<ItemLocationResponse> => {
  const searchParams = new URLSearchParams();
  searchParams.append("warehouseId", warehouseId);

  return fetchWithAuth(`/api/items/${itemId}/locations?${searchParams.toString()}`, {
    token,
  });
};

interface LocationCardProps {
  location: ItemLocationResult;
  item: ItemSearchResult;
  onAddToCart: (quantity: number) => void;
}

function LocationCard({ location, item, onAddToCart }: LocationCardProps) {
  const [quantity, setQuantity] = useState(1);
  const [isAdding, setIsAdding] = useState(false);

  const handleQuantityChange = (value: string) => {
    const num = parseInt(value) || 0;
    setQuantity(Math.max(0, Math.min(num, location.quantity)));
  };

  const handleIncrement = () => {
    setQuantity(prev => Math.min(prev + 1, location.quantity));
  };

  const handleDecrement = () => {
    setQuantity(prev => Math.max(prev - 1, 1));
  };

  const handleAddToCart = async () => {
    if (quantity <= 0 || quantity > location.quantity) return;

    setIsAdding(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 200)); // Brief loading state
      onAddToCart(quantity);
      setQuantity(1); // Reset quantity after adding
    } finally {
      setIsAdding(false);
    }
  };

  const maxQuantity = location.quantity;
  const isValidQuantity = quantity > 0 && quantity <= maxQuantity;

  return (
    <Card className="border-l-4 border-l-blue-500">
      <CardContent className="p-4">
        <div className="space-y-4">
          {/* Location Header */}
          <div className="flex items-start justify-between">
            <div className="flex items-center gap-2">
              <MapPin className="h-4 w-4 text-blue-600 mt-0.5" />
              <div>
                <h4 className="font-medium">{location.location.name}</h4>
                <div className="flex items-center gap-2 mt-1">
                  <Badge variant="outline" className="text-xs">
                    {location.location.category}
                  </Badge>
                  <Badge variant="secondary" className="text-xs">
                    {location.location.type}
                  </Badge>
                </div>
              </div>
            </div>
            <div className="text-right">
              <p className="text-lg font-semibold text-green-600">
                {location.quantity}
              </p>
              <p className="text-xs text-muted-foreground">
                {item.unitOfMeasure}
              </p>
            </div>
          </div>

          {/* Pallet Information */}
          {location.palletBarcode && (
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Package className="h-3 w-3" />
              <span>Pallet: {location.palletBarcode}</span>
            </div>
          )}

          <Separator />

          {/* Quantity Selection */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium">Pick Quantity</label>
              <span className="text-xs text-muted-foreground">
                Max: {maxQuantity}
              </span>
            </div>

            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleDecrement}
                disabled={quantity <= 1}
                className="h-8 w-8 p-0 shrink-0"
              >
                <Minus className="h-3 w-3" />
              </Button>

              <Input
                type="number"
                value={quantity}
                onChange={(e) => handleQuantityChange(e.target.value)}
                min={1}
                max={maxQuantity}
                className="text-center h-8 w-20"
              />

              <Button
                variant="outline"
                size="sm"
                onClick={handleIncrement}
                disabled={quantity >= maxQuantity}
                className="h-8 w-8 p-0 shrink-0"
              >
                <Plus className="h-3 w-3" />
              </Button>

              <Button
                onClick={handleAddToCart}
                disabled={!isValidQuantity || isAdding}
                className="ml-auto bg-green-600 hover:bg-green-700 h-8 px-4"
                size="sm"
              >
                {isAdding ? (
                  <Loader2 className="h-3 w-3 animate-spin mr-1" />
                ) : (
                  <ShoppingCart className="h-3 w-3 mr-1" />
                )}
                Add to Cart
              </Button>
            </div>

            {!isValidQuantity && quantity > 0 && (
              <div className="flex items-center gap-1 text-xs text-red-600">
                <AlertCircle className="h-3 w-3" />
                <span>
                  Quantity must be between 1 and {maxQuantity}
                </span>
              </div>
            )}
          </div>

          {/* Last Updated */}
          <div className="text-xs text-muted-foreground">
            Last updated: {new Date(location.lastUpdated).toLocaleString()}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export function ItemLocationList({
  item,
  warehouseId,
  onAddToCart,
}: ItemLocationListProps) {
  const { appToken } = useAuth();

  // Fetch item locations using useSuspenseQuery
  const { data: locationData } = useSuspenseQuery<ItemLocationResponse, Error>({
    queryKey: ["item-locations", item.id, warehouseId],
    queryFn: () => fetchItemLocations(item.id, warehouseId, appToken),
    staleTime: 60000, // Cache for 1 minute
  });

  const locations = locationData?.locations || [];

  const handleAddToCart = (location: ItemLocationResult) => (quantity: number) => {
    onAddToCart(item, quantity, location);
  };

  if (locations.length === 0) {
    return (
      <Card className="border-dashed">
        <CardContent className="p-8 text-center">
          <Package className="h-12 w-12 mx-auto mb-4 text-muted-foreground opacity-50" />
          <h3 className="font-medium mb-2">No Locations Found</h3>
          <p className="text-sm text-muted-foreground">
            This item is not currently available in any locations in this warehouse.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* Summary */}
      <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
        <div className="flex items-center gap-2">
          <MapPin className="h-4 w-4 text-blue-600" />
          <span className="font-medium">
            {locations.length} location{locations.length !== 1 ? "s" : ""} found
          </span>
        </div>
        <div className="text-sm text-muted-foreground">
          Total: {locationData.totalQuantity} {item.unitOfMeasure}
        </div>
      </div>

      {/* Location Cards */}
      <div className="space-y-3">
        {locations.map((location) => (
          <LocationCard
            key={`${location.palletId}-${location.location.id}`}
            location={location}
            item={item}
            onAddToCart={handleAddToCart(location)}
          />
        ))}
      </div>
    </div>
  );
}
