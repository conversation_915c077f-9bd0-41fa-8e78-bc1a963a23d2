"use client";

import { Suspense, useEffect } from "react";
import { usePageTitle } from "@/components/providers/PageTitleContext";
import { ShipmentStatusDashboard } from "@/components/picking/ShipmentStatusDashboard";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Ship, Package, Clock, CheckCircle } from "lucide-react";
import { useShipments } from "@/hooks/api";

function ShipmentsPageContent() {
  const { setTitle } = usePageTitle();

  useEffect(() => {
    setTitle("Shipments");
  }, [setTitle]);

  // Fetch shipments data using existing warehouse-aware hook
  const { data: shipmentsResponse = { shipments: [] }, isLoading } =
    useShipments();
  const shipments = Array.isArray(shipmentsResponse)
    ? shipmentsResponse
    : shipmentsResponse.shipments || [];

  // Calculate shipment statistics
  const stats = {
    preparing: shipments.filter((s: any) => s.status === "preparing").length,
    ready: shipments.filter((s: any) => s.status === "ready").length,
    inTransit: shipments.filter((s: any) => s.status === "in_transit").length,
    delivered: shipments.filter((s: any) => s.status === "delivered").length,
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center gap-3">
        <div className="p-2 bg-blue-100 rounded-lg">
          <Ship className="h-6 w-6 text-blue-600" />
        </div>
        <div>
          <h1 className="text-2xl font-bold">Shipments</h1>
          <p className="text-muted-foreground">
            Manage outgoing shipments and track delivery status
          </p>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Preparing</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">12</div>
            <p className="text-xs text-muted-foreground">
              Shipments being prepared
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Ready</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">8</div>
            <p className="text-xs text-muted-foreground">Ready for pickup</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">In Transit</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">15</div>
            <p className="text-xs text-muted-foreground">Currently shipping</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Delivered</CardTitle>
            <Ship className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">142</div>
            <p className="text-xs text-muted-foreground">This month</p>
          </CardContent>
        </Card>
      </div>

      {/* Shipment Dashboard */}
      <ShipmentStatusDashboard />
    </div>
  );
}

export default function ShipmentsPage() {
  return (
    <Suspense
      fallback={
        <div className="space-y-6">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-100 rounded-lg animate-pulse">
              <Ship className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <div className="h-8 w-32 bg-gray-200 rounded animate-pulse mb-2" />
              <div className="h-4 w-64 bg-gray-200 rounded animate-pulse" />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {[...Array(4)].map((_, i) => (
              <Card key={i}>
                <CardHeader className="space-y-0 pb-2">
                  <div className="h-4 w-20 bg-gray-200 rounded animate-pulse" />
                </CardHeader>
                <CardContent>
                  <div className="h-8 w-12 bg-gray-200 rounded animate-pulse mb-2" />
                  <div className="h-3 w-24 bg-gray-200 rounded animate-pulse" />
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="h-96 bg-gray-200 rounded animate-pulse" />
        </div>
      }
    >
      <ShipmentsPageContent />
    </Suspense>
  );
}
