"use client";

import confetti from "canvas-confetti";

export interface ConfettiOptions {
  particleCount?: number;
  spread?: number;
  startVelocity?: number;
  origin?: { x: number; y: number };
  colors?: string[];
  duration?: number;
  gravity?: number;
  ticks?: number;
  zIndex?: number;
}

export interface ConfettiPreset {
  name: string;
  options: ConfettiOptions;
  duration: number;
  description: string;
}

// Predefined confetti presets for different workflow completions
export const CONFETTI_PRESETS: Record<string, ConfettiPreset> = {
  shipmentCreated: {
    name: "Shipment Created",
    description: "Celebration for successful shipment creation",
    duration: 2000,
    options: {
      particleCount: 100,
      spread: 70,
      origin: { x: 0.5, y: 0.6 },
      colors: ["#10B981", "#34D399", "#6EE7B7"], // Green theme
      startVelocity: 30,
      gravity: 0.5,
      ticks: 60,
    },
  },

  packingComplete: {
    name: "Packing Complete",
    description: "Gold confetti for packing completion",
    duration: 3000,
    options: {
      particleCount: 150,
      spread: 80,
      origin: { x: 0.5, y: 0.6 },
      colors: ["#F59E0B", "#FBBF24", "#FCD34D"], // Gold theme
      startVelocity: 35,
      gravity: 0.4,
      ticks: 80,
    },
  },

  inventoryReleased: {
    name: "Inventory Released",
    description: "Blue confetti for inventory release completion",
    duration: 3000,
    options: {
      particleCount: 120,
      spread: 90,
      origin: { x: 0.5, y: 0.6 },
      colors: ["#3B82F6", "#60A5FA", "#93C5FD"], // Blue theme
      startVelocity: 30,
      gravity: 0.3,
      ticks: 70,
    },
  },

  itemPicked: {
    name: "Item Picked",
    description: "Quick sparkle for individual item picking",
    duration: 1000,
    options: {
      particleCount: 30,
      spread: 45,
      origin: { x: 0.5, y: 0.7 },
      colors: ["#8B5CF6", "#A78BFA", "#C4B5FD"], // Purple theme
      startVelocity: 20,
      gravity: 0.6,
      ticks: 40,
    },
  },

  palletBuilt: {
    name: "Pallet Built",
    description: "Celebration for hybrid pallet creation",
    duration: 2500,
    options: {
      particleCount: 80,
      spread: 60,
      origin: { x: 0.5, y: 0.6 },
      colors: ["#EF4444", "#F87171", "#FCA5A5"], // Red theme
      startVelocity: 25,
      gravity: 0.4,
      ticks: 60,
    },
  },

  workflowComplete: {
    name: "Workflow Complete",
    description: "Multi-colored celebration for major workflow completion",
    duration: 4000,
    options: {
      particleCount: 200,
      spread: 100,
      origin: { x: 0.5, y: 0.6 },
      colors: [
        "#3B82F6",
        "#10B981",
        "#F59E0B",
        "#EF4444",
        "#8B5CF6",
        "#F97316",
      ],
      startVelocity: 40,
      gravity: 0.3,
      ticks: 100,
    },
  },
};

/**
 * Trigger a single confetti burst with specified options
 */
export function triggerConfetti(options: ConfettiOptions = {}) {
  if (typeof window === "undefined") {
    console.warn("Confetti not available in server environment");
    return;
  }

  const defaultOptions: ConfettiOptions = {
    particleCount: 100,
    spread: 70,
    origin: { x: 0.5, y: 0.6 },
    colors: ["#3B82F6", "#10B981", "#F59E0B", "#EF4444", "#8B5CF6"],
    startVelocity: 30,
    gravity: 0.5,
    ticks: 60,
    zIndex: 9999,
  };

  confetti({ ...defaultOptions, ...options });
}

/**
 * Trigger a preset confetti animation
 */
export function triggerConfettiPreset(
  presetName: keyof typeof CONFETTI_PRESETS
) {
  const preset = CONFETTI_PRESETS[presetName];
  if (!preset) {
    console.warn(`Confetti preset "${presetName}" not found`);
    return;
  }

  triggerConfetti(preset.options);
}

/**
 * Trigger a multi-burst confetti animation over time
 */
export function triggerConfettiBurst(
  presetName: keyof typeof CONFETTI_PRESETS,
  onComplete?: () => void
) {
  const preset = CONFETTI_PRESETS[presetName];
  if (!preset) {
    console.warn(`Confetti preset "${presetName}" not found`);
    onComplete?.();
    return;
  }

  const duration = preset.duration;
  const animationEnd = Date.now() + duration;

  const randomInRange = (min: number, max: number) => {
    return Math.random() * (max - min) + min;
  };

  const interval = setInterval(() => {
    const timeLeft = animationEnd - Date.now();

    if (timeLeft <= 0) {
      clearInterval(interval);
      onComplete?.();
      return;
    }

    const particleCount =
      (preset.options.particleCount || 100) * (timeLeft / duration);

    // Left side burst
    triggerConfetti({
      ...preset.options,
      particleCount: Math.floor(particleCount / 2),
      origin: {
        x: randomInRange(0.1, 0.3),
        y: Math.random() - 0.2,
      },
    });

    // Right side burst
    triggerConfetti({
      ...preset.options,
      particleCount: Math.floor(particleCount / 2),
      origin: {
        x: randomInRange(0.7, 0.9),
        y: Math.random() - 0.2,
      },
    });
  }, 250);

  return () => clearInterval(interval);
}

/**
 * Trigger warehouse-optimized confetti (mobile/tablet friendly)
 */
export function triggerWarehouseConfetti(
  type: "success" | "milestone" | "completion" = "success"
) {
  const warehousePresets = {
    success: {
      particleCount: 60,
      spread: 50,
      origin: { x: 0.5, y: 0.7 },
      colors: ["#10B981", "#34D399"],
      startVelocity: 20,
      gravity: 0.6,
      ticks: 40,
    },
    milestone: {
      particleCount: 40,
      spread: 40,
      origin: { x: 0.5, y: 0.8 },
      colors: ["#F59E0B", "#FBBF24"],
      startVelocity: 15,
      gravity: 0.7,
      ticks: 30,
    },
    completion: {
      particleCount: 100,
      spread: 70,
      origin: { x: 0.5, y: 0.6 },
      colors: ["#3B82F6", "#10B981", "#F59E0B"],
      startVelocity: 25,
      gravity: 0.5,
      ticks: 50,
    },
  };

  triggerConfetti(warehousePresets[type]);
}

/**
 * React hook for confetti animations with cleanup
 */
export function useConfetti() {
  const trigger = (options: ConfettiOptions = {}) => {
    triggerConfetti(options);
  };

  const triggerPreset = (presetName: keyof typeof CONFETTI_PRESETS) => {
    triggerConfettiPreset(presetName);
  };

  const triggerBurst = (
    presetName: keyof typeof CONFETTI_PRESETS,
    onComplete?: () => void
  ) => {
    return triggerConfettiBurst(presetName, onComplete);
  };

  const triggerWarehouse = (
    type: "success" | "milestone" | "completion" = "success"
  ) => {
    triggerWarehouseConfetti(type);
  };

  return {
    trigger,
    triggerPreset,
    triggerBurst,
    triggerWarehouse,
    presets: CONFETTI_PRESETS,
  };
}

// Export types
export type ConfettiPresetName = keyof typeof CONFETTI_PRESETS;
export type WarehouseConfettiType = "success" | "milestone" | "completion";
