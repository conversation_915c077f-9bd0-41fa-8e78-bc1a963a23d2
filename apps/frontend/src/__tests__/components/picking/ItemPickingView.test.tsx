/**
 * ItemPickingView Component Tests
 *
 * Tests the main item picking interface including search, cart management,
 * and workflow actions (shipment creation, item release, pallet building).
 */

import {
  render,
  screen,
  fireEvent,
  waitFor,
  within,
} from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import "@testing-library/jest-dom";
import { vi } from "vitest";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { ItemPickingView } from "@/components/picking/ItemPickingView";

// Mock Next.js router
const mockPush = vi.fn();
vi.mock("next/navigation", () => ({
  useRouter: () => ({
    push: mockPush,
    back: vi.fn(),
  }),
  usePathname: () => "/picking",
}));

// Mock providers
vi.mock("@/components/providers/auth-provider", () => ({
  useAuth: () => ({
    appUser: { id: "user-1", name: "Test User" },
    appToken: "test-token",
    isLoading: false,
  }),
}));

vi.mock("@/components/providers/warehouse-provider", () => ({
  useWarehouse: () => ({
    currentWarehouse: { id: "warehouse-1", name: "Test Warehouse" },
    isLoading: false,
  }),
}));

// Mock confetti
vi.mock("@/hooks/useConfetti", () => ({
  useConfetti: () => ({
    trigger: vi.fn(),
    triggerPreset: vi.fn(),
  }),
}));

// Mock API hooks
const mockSearchItems = vi.fn();
const mockGetItemLocations = vi.fn();
const mockCreateShipment = vi.fn();
const mockReleaseItems = vi.fn();

vi.mock("@/hooks/api", () => ({
  useItemSearch: () => ({
    data: [],
    isLoading: false,
    error: null,
  }),
  useItemLocations: () => ({
    data: [],
    isLoading: false,
  }),
  useCreateShipment: () => ({
    mutateAsync: mockCreateShipment,
    isLoading: false,
  }),
  useReleaseItems: () => ({
    mutateAsync: mockReleaseItems,
    isLoading: false,
  }),
}));

// Mock item picking hook
const mockAddItemToCart = vi.fn();
const mockRemoveItemFromCart = vi.fn();
const mockUpdateCartItemQuantity = vi.fn();
const mockClearPersistentCart = vi.fn();
const mockUpdateCartDestination = vi.fn();

vi.mock("@/hooks/useItemPicking", () => ({
  useItemPicking: () => ({
    persistentCart: {
      items: [],
      totalItems: 0,
      destination: "",
      destinationCode: "",
    },
    addItemToCart: mockAddItemToCart,
    removeItemFromCart: mockRemoveItemFromCart,
    updateCartItemQuantity: mockUpdateCartItemQuantity,
    clearPersistentCart: mockClearPersistentCart,
    updateCartDestination: mockUpdateCartDestination,
    createShipmentFromCart: mockCreateShipment,
    syncAfterShipmentOperation: vi.fn(),
  }),
}));

// Test wrapper component
const TestWrapper = ({ children }: { children: React.ReactNode }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );
};

describe("ItemPickingView", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("renders the main picking interface", () => {
    render(
      <TestWrapper>
        <ItemPickingView />
      </TestWrapper>
    );

    expect(screen.getByText("Item Picking")).toBeInTheDocument();
    expect(screen.getByPlaceholderText(/search items/i)).toBeInTheDocument();
    expect(screen.getByText("Cart (0 items)")).toBeInTheDocument();
  });

  it("displays search input with proper placeholder", () => {
    render(
      <TestWrapper>
        <ItemPickingView />
      </TestWrapper>
    );

    const searchInput = screen.getByPlaceholderText(/search items/i);
    expect(searchInput).toBeInTheDocument();
    expect(searchInput).toHaveAttribute("type", "text");
  });

  it("shows empty cart state initially", () => {
    render(
      <TestWrapper>
        <ItemPickingView />
      </TestWrapper>
    );

    expect(screen.getByText("Cart (0 items)")).toBeInTheDocument();
    expect(screen.getByText(/your cart is empty/i)).toBeInTheDocument();
  });

  it("handles item search input", async () => {
    const user = userEvent.setup();

    render(
      <TestWrapper>
        <ItemPickingView />
      </TestWrapper>
    );

    const searchInput = screen.getByPlaceholderText(/search items/i);
    await user.type(searchInput, "widget");

    expect(searchInput).toHaveValue("widget");
  });

  it("displays cart with items when items are added", () => {
    // Mock cart with items
    vi.mocked(require("@/hooks/useItemPicking").useItemPicking).mockReturnValue(
      {
        persistentCart: {
          items: [
            {
              itemId: "item-1",
              itemName: "Test Widget",
              quantity: 5,
              unitOfMeasure: "pcs",
              sourcePalletId: "pallet-1",
              sourcePalletBarcode: "PAL-001",
            },
          ],
          totalItems: 5,
          destination: "",
          destinationCode: "",
        },
        addItemToCart: mockAddItemToCart,
        removeItemFromCart: mockRemoveItemFromCart,
        updateCartItemQuantity: mockUpdateCartItemQuantity,
        clearPersistentCart: mockClearPersistentCart,
        updateCartDestination: mockUpdateCartDestination,
        createShipmentFromCart: mockCreateShipment,
        syncAfterShipmentOperation: vi.fn(),
      }
    );

    render(
      <TestWrapper>
        <ItemPickingView />
      </TestWrapper>
    );

    expect(screen.getByText("Cart (5 items)")).toBeInTheDocument();
    expect(screen.getByText("Test Widget")).toBeInTheDocument();
    expect(screen.getByText("5 pcs")).toBeInTheDocument();
  });

  it("shows shipment creation button when cart has items", () => {
    // Mock cart with items
    jest
      .mocked(require("@/hooks/useItemPicking").useItemPicking)
      .mockReturnValue({
        persistentCart: {
          items: [
            {
              itemId: "item-1",
              itemName: "Test Widget",
              quantity: 5,
              unitOfMeasure: "pcs",
              sourcePalletId: "pallet-1",
              sourcePalletBarcode: "PAL-001",
            },
          ],
          totalItems: 5,
          destination: "",
          destinationCode: "",
        },
        addItemToCart: mockAddItemToCart,
        removeItemFromCart: mockRemoveItemFromCart,
        updateCartItemQuantity: mockUpdateCartItemQuantity,
        clearPersistentCart: mockClearPersistentCart,
        updateCartDestination: mockUpdateCartDestination,
        createShipmentFromCart: mockCreateShipment,
        syncAfterShipmentOperation: vi.fn(),
      });

    render(
      <TestWrapper>
        <ItemPickingView />
      </TestWrapper>
    );

    expect(screen.getByText(/ship items \(5\)/i)).toBeInTheDocument();
    expect(screen.getByText(/add pallets/i)).toBeInTheDocument();
  });

  it("opens shipment creation modal when ship items button is clicked", async () => {
    const user = userEvent.setup();

    // Mock cart with items
    vi.mocked(require("@/hooks/useItemPicking").useItemPicking).mockReturnValue(
      {
        persistentCart: {
          items: [
            {
              itemId: "item-1",
              itemName: "Test Widget",
              quantity: 5,
              unitOfMeasure: "pcs",
              sourcePalletId: "pallet-1",
              sourcePalletBarcode: "PAL-001",
            },
          ],
          totalItems: 5,
          destination: "",
          destinationCode: "",
        },
        addItemToCart: mockAddItemToCart,
        removeItemFromCart: mockRemoveItemFromCart,
        updateCartItemQuantity: mockUpdateCartItemQuantity,
        clearPersistentCart: mockClearPersistentCart,
        updateCartDestination: mockUpdateCartDestination,
        createShipmentFromCart: mockCreateShipment,
        syncAfterShipmentOperation: vi.fn(),
      }
    );

    render(
      <TestWrapper>
        <ItemPickingView />
      </TestWrapper>
    );

    const shipButton = screen.getByText(/ship items \(5\)/i);
    await user.click(shipButton);

    await waitFor(() => {
      expect(screen.getByText("Create Shipment")).toBeInTheDocument();
    });
  });

  it("opens enhanced shipment modal when add pallets button is clicked", async () => {
    const user = userEvent.setup();

    // Mock cart with items
    vi
      .mocked(require("@/hooks/useItemPicking").useItemPicking)
      .mockReturnValue({
        persistentCart: {
          items: [
            {
              itemId: "item-1",
              itemName: "Test Widget",
              quantity: 5,
              unitOfMeasure: "pcs",
              sourcePalletId: "pallet-1",
              sourcePalletBarcode: "PAL-001",
            },
          ],
          totalItems: 5,
          destination: "",
          destinationCode: "",
        },
        addItemToCart: mockAddItemToCart,
        removeItemFromCart: mockRemoveItemFromCart,
        updateCartItemQuantity: mockUpdateCartItemQuantity,
        clearPersistentCart: mockClearPersistentCart,
        updateCartDestination: mockUpdateCartDestination,
        createShipmentFromCart: mockCreateShipment,
        syncAfterShipmentOperation: vi.fn(),
      });

    render(
      <TestWrapper>
        <ItemPickingView />
      </TestWrapper>
    );

    const addPalletsButton = screen.getByText(/add pallets/i);
    await user.click(addPalletsButton);

    await waitFor(() => {
      expect(screen.getByText("Create Mixed Shipment")).toBeInTheDocument();
    });
  });

  it("handles cart item quantity updates", async () => {
    const user = userEvent.setup();

    // Mock cart with items
    vi
      .mocked(require("@/hooks/useItemPicking").useItemPicking)
      .mockReturnValue({
        persistentCart: {
          items: [
            {
              itemId: "item-1",
              itemName: "Test Widget",
              quantity: 5,
              unitOfMeasure: "pcs",
              sourcePalletId: "pallet-1",
              sourcePalletBarcode: "PAL-001",
            },
          ],
          totalItems: 5,
          destination: "",
          destinationCode: "",
        },
        addItemToCart: mockAddItemToCart,
        removeItemFromCart: mockRemoveItemFromCart,
        updateCartItemQuantity: mockUpdateCartItemQuantity,
        clearPersistentCart: mockClearPersistentCart,
        updateCartDestination: mockUpdateCartDestination,
        createShipmentFromCart: mockCreateShipment,
        syncAfterShipmentOperation: vi.fn(),
      });

    render(
      <TestWrapper>
        <ItemPickingView />
      </TestWrapper>
    );

    // Find quantity input and update it
    const quantityInput = screen.getByDisplayValue("5");
    await user.clear(quantityInput);
    await user.type(quantityInput, "10");

    // Verify the update function was called
    expect(mockUpdateCartItemQuantity).toHaveBeenCalled();
  });

  it("handles item removal from cart", async () => {
    const user = userEvent.setup();

    // Mock cart with items
    vi.mocked(require("@/hooks/useItemPicking").useItemPicking).mockReturnValue(
      {
        persistentCart: {
          items: [
            {
              itemId: "item-1",
              itemName: "Test Widget",
              quantity: 5,
              unitOfMeasure: "pcs",
              sourcePalletId: "pallet-1",
              sourcePalletBarcode: "PAL-001",
            },
          ],
          totalItems: 5,
          destination: "",
          destinationCode: "",
        },
        addItemToCart: mockAddItemToCart,
        removeItemFromCart: mockRemoveItemFromCart,
        updateCartItemQuantity: mockUpdateCartItemQuantity,
        clearPersistentCart: mockClearPersistentCart,
        updateCartDestination: mockUpdateCartDestination,
        createShipmentFromCart: mockCreateShipment,
        syncAfterShipmentOperation: vi.fn(),
      }
    );

    render(
      <TestWrapper>
        <ItemPickingView />
      </TestWrapper>
    );

    // Find and click remove button
    const removeButton = screen.getByLabelText(/remove item/i);
    await user.click(removeButton);

    expect(mockRemoveItemFromCart).toHaveBeenCalledWith(0);
  });

  it("displays mobile-optimized interface", () => {
    render(
      <TestWrapper>
        <ItemPickingView />
      </TestWrapper>
    );

    const searchInput = screen.getByPlaceholderText(/search items/i);
    expect(searchInput).toHaveClass("min-h-[44px]"); // Touch target size
  });
});
