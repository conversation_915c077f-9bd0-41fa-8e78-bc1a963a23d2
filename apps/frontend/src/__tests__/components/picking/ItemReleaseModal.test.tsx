/**
 * ItemReleaseModal Component Tests
 * 
 * Tests the item release modal for direct item release without shipment creation,
 * used for local pickup and stock pull scenarios.
 */

import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ItemReleaseModal } from '@/components/picking/ItemReleaseModal';
import type { ItemPickingCart } from '@quildora/types';

// Mock providers
jest.mock('@/components/providers/auth-provider', () => ({
  useAuth: () => ({
    appUser: { id: 'user-1', name: 'Test User' },
    appToken: 'test-token',
    isLoading: false,
  }),
}));

jest.mock('@/components/providers/warehouse-provider', () => ({
  useWarehouse: () => ({
    currentWarehouse: { id: 'warehouse-1', name: 'Test Warehouse' },
    isLoading: false,
  }),
}));

// Mock confetti hook
const mockTrigger = jest.fn();
jest.mock('@/hooks/useConfetti', () => ({
  useConfetti: () => ({
    trigger: mockTrigger,
  }),
}));

// Mock toast
jest.mock('sonner', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
  },
}));

// Mock API hook
const mockReleaseItems = jest.fn();
jest.mock('@/hooks/api', () => ({
  useReleaseItems: () => ({
    mutateAsync: mockReleaseItems,
    isLoading: false,
  }),
}));

// Test wrapper component
const TestWrapper = ({ children }: { children: React.ReactNode }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

// Test data
const mockCart: ItemPickingCart = {
  items: [
    {
      itemId: 'item-1',
      itemName: 'Test Widget',
      quantity: 5,
      unitOfMeasure: 'pcs',
      sourcePalletId: 'pallet-1',
      sourcePalletBarcode: 'PAL-001',
    },
    {
      itemId: 'item-2',
      itemName: 'Test Gadget',
      quantity: 3,
      unitOfMeasure: 'pcs',
      sourcePalletId: 'pallet-2',
      sourcePalletBarcode: 'PAL-002',
    },
  ],
  totalItems: 8,
  destination: '',
  destinationCode: '',
};

const mockOnReleaseComplete = jest.fn();
const mockOnClose = jest.fn();

describe('ItemReleaseModal', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders when open', () => {
    render(
      <TestWrapper>
        <ItemReleaseModal
          isOpen={true}
          onClose={mockOnClose}
          onReleaseComplete={mockOnReleaseComplete}
          cart={mockCart}
          isReleasing={false}
        />
      </TestWrapper>
    );

    expect(screen.getByText('Release Items Directly')).toBeInTheDocument();
    expect(screen.getByLabelText(/released to/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/notes/i)).toBeInTheDocument();
  });

  it('does not render when closed', () => {
    render(
      <TestWrapper>
        <ItemReleaseModal
          isOpen={false}
          onClose={mockOnClose}
          onReleaseComplete={mockOnReleaseComplete}
          cart={mockCart}
          isReleasing={false}
        />
      </TestWrapper>
    );

    expect(screen.queryByText('Release Items Directly')).not.toBeInTheDocument();
  });

  it('displays cart items in the modal', () => {
    render(
      <TestWrapper>
        <ItemReleaseModal
          isOpen={true}
          onClose={mockOnClose}
          onReleaseComplete={mockOnReleaseComplete}
          cart={mockCart}
          isReleasing={false}
        />
      </TestWrapper>
    );

    expect(screen.getByText('Test Widget')).toBeInTheDocument();
    expect(screen.getByText('Test Gadget')).toBeInTheDocument();
    expect(screen.getByText('5 pcs')).toBeInTheDocument();
    expect(screen.getByText('3 pcs')).toBeInTheDocument();
  });

  it('shows source pallet information', () => {
    render(
      <TestWrapper>
        <ItemReleaseModal
          isOpen={true}
          onClose={mockOnClose}
          onReleaseComplete={mockOnReleaseComplete}
          cart={mockCart}
          isReleasing={false}
        />
      </TestWrapper>
    );

    expect(screen.getByText('From: PAL-001')).toBeInTheDocument();
    expect(screen.getByText('From: PAL-002')).toBeInTheDocument();
  });

  it('handles released to input', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ItemReleaseModal
          isOpen={true}
          onClose={mockOnClose}
          onReleaseComplete={mockOnReleaseComplete}
          cart={mockCart}
          isReleasing={false}
        />
      </TestWrapper>
    );

    const releasedToInput = screen.getByLabelText(/released to/i);
    await user.type(releasedToInput, 'Local Crew Team A');

    expect(releasedToInput).toHaveValue('Local Crew Team A');
  });

  it('handles notes input', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ItemReleaseModal
          isOpen={true}
          onClose={mockOnClose}
          onReleaseComplete={mockOnReleaseComplete}
          cart={mockCart}
          isReleasing={false}
        />
      </TestWrapper>
    );

    const notesInput = screen.getByLabelText(/notes/i);
    await user.type(notesInput, 'Emergency stock pull for production line');

    expect(notesInput).toHaveValue('Emergency stock pull for production line');
  });

  it('validates required released to field', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ItemReleaseModal
          isOpen={true}
          onClose={mockOnClose}
          onReleaseComplete={mockOnReleaseComplete}
          cart={mockCart}
          isReleasing={false}
        />
      </TestWrapper>
    );

    const releaseButton = screen.getByText('Release Items');
    await user.click(releaseButton);

    await waitFor(() => {
      expect(screen.getByText('Released to is required')).toBeInTheDocument();
    });

    expect(mockReleaseItems).not.toHaveBeenCalled();
  });

  it('submits form with valid data', async () => {
    const user = userEvent.setup();
    mockReleaseItems.mockResolvedValue({ success: true });
    
    render(
      <TestWrapper>
        <ItemReleaseModal
          isOpen={true}
          onClose={mockOnClose}
          onReleaseComplete={mockOnReleaseComplete}
          cart={mockCart}
          isReleasing={false}
        />
      </TestWrapper>
    );

    const releasedToInput = screen.getByLabelText(/released to/i);
    const notesInput = screen.getByLabelText(/notes/i);
    const releaseButton = screen.getByText('Release Items');

    await user.type(releasedToInput, 'Local Crew Team A');
    await user.type(notesInput, 'Stock pull for production');
    await user.click(releaseButton);

    await waitFor(() => {
      expect(mockReleaseItems).toHaveBeenCalledWith({
        items: mockCart.items,
        releasedTo: 'Local Crew Team A',
        notes: 'Stock pull for production',
      });
    });
  });

  it('triggers confetti and calls onReleaseComplete on success', async () => {
    const user = userEvent.setup();
    mockReleaseItems.mockResolvedValue({ success: true });
    
    render(
      <TestWrapper>
        <ItemReleaseModal
          isOpen={true}
          onClose={mockOnClose}
          onReleaseComplete={mockOnReleaseComplete}
          cart={mockCart}
          isReleasing={false}
        />
      </TestWrapper>
    );

    const releasedToInput = screen.getByLabelText(/released to/i);
    const releaseButton = screen.getByText('Release Items');

    await user.type(releasedToInput, 'Local Crew Team A');
    await user.click(releaseButton);

    await waitFor(() => {
      expect(mockTrigger).toHaveBeenCalled();
      expect(mockOnReleaseComplete).toHaveBeenCalled();
    });
  });

  it('handles close button click', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ItemReleaseModal
          isOpen={true}
          onClose={mockOnClose}
          onReleaseComplete={mockOnReleaseComplete}
          cart={mockCart}
          isReleasing={false}
        />
      </TestWrapper>
    );

    const cancelButton = screen.getByText('Cancel');
    await user.click(cancelButton);

    expect(mockOnClose).toHaveBeenCalled();
  });

  it('shows loading state when releasing items', () => {
    render(
      <TestWrapper>
        <ItemReleaseModal
          isOpen={true}
          onClose={mockOnClose}
          onReleaseComplete={mockOnReleaseComplete}
          cart={mockCart}
          isReleasing={true}
        />
      </TestWrapper>
    );

    expect(screen.getByText('Releasing Items...')).toBeInTheDocument();
    expect(screen.getByText('Release Items')).toBeDisabled();
  });

  it('disables form inputs when releasing', () => {
    render(
      <TestWrapper>
        <ItemReleaseModal
          isOpen={true}
          onClose={mockOnClose}
          onReleaseComplete={mockOnReleaseComplete}
          cart={mockCart}
          isReleasing={true}
        />
      </TestWrapper>
    );

    expect(screen.getByLabelText(/released to/i)).toBeDisabled();
    expect(screen.getByLabelText(/notes/i)).toBeDisabled();
  });

  it('handles empty cart gracefully', () => {
    const emptyCart: ItemPickingCart = {
      items: [],
      totalItems: 0,
      destination: '',
      destinationCode: '',
    };

    render(
      <TestWrapper>
        <ItemReleaseModal
          isOpen={true}
          onClose={mockOnClose}
          onReleaseComplete={mockOnReleaseComplete}
          cart={emptyCart}
          isReleasing={false}
        />
      </TestWrapper>
    );

    expect(screen.getByText(/no items to release/i)).toBeInTheDocument();
  });

  it('displays mobile-optimized touch targets', () => {
    render(
      <TestWrapper>
        <ItemReleaseModal
          isOpen={true}
          onClose={mockOnClose}
          onReleaseComplete={mockOnReleaseComplete}
          cart={mockCart}
          isReleasing={false}
        />
      </TestWrapper>
    );

    const releasedToInput = screen.getByLabelText(/released to/i);
    const releaseButton = screen.getByText('Release Items');

    expect(releasedToInput).toHaveClass('min-h-[44px]');
    expect(releaseButton).toHaveClass('min-h-[44px]');
  });

  it('handles API errors gracefully', async () => {
    const user = userEvent.setup();
    mockReleaseItems.mockRejectedValue(new Error('API Error'));
    
    render(
      <TestWrapper>
        <ItemReleaseModal
          isOpen={true}
          onClose={mockOnClose}
          onReleaseComplete={mockOnReleaseComplete}
          cart={mockCart}
          isReleasing={false}
        />
      </TestWrapper>
    );

    const releasedToInput = screen.getByLabelText(/released to/i);
    const releaseButton = screen.getByText('Release Items');

    await user.type(releasedToInput, 'Local Crew Team A');
    await user.click(releaseButton);

    await waitFor(() => {
      expect(screen.getByText('Failed to release items')).toBeInTheDocument();
    });

    expect(mockOnReleaseComplete).not.toHaveBeenCalled();
  });
});
