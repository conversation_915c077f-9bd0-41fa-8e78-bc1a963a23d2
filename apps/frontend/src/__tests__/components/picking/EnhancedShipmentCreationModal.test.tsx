/**
 * EnhancedShipmentCreationModal Component Tests
 * 
 * Tests the enhanced shipment creation modal that supports both items
 * and complete pallets in mixed shipments.
 */

import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { EnhancedShipmentCreationModal } from '@/components/picking/EnhancedShipmentCreationModal';
import type { ItemPickingCart, Pallet } from '@quildora/types';

// Mock API hooks
const mockPallets: Pallet[] = [
  {
    id: 'pallet-1',
    barcode: 'PAL-001',
    status: 'Stored',
    shipToDestination: 'Customer ABC',
    destinationCode: '12345',
    location: { id: 'loc-1', name: 'A-01-01', category: 'Storage', warehouseId: 'warehouse-1' },
    warehouseId: 'warehouse-1',
    tenantId: 'tenant-1',
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'pallet-2',
    barcode: 'PAL-002',
    status: 'Stored',
    shipToDestination: 'Customer XYZ',
    destinationCode: '67890',
    location: { id: 'loc-2', name: 'B-02-01', category: 'Storage', warehouseId: 'warehouse-1' },
    warehouseId: 'warehouse-1',
    tenantId: 'tenant-1',
    createdAt: new Date(),
    updatedAt: new Date(),
  },
];

jest.mock('@/hooks/api', () => ({
  usePalletsSuspense: () => ({
    data: mockPallets,
  }),
  useDestinationsWithCodes: () => ({
    data: [
      { name: 'Customer ABC', code: '12345' },
      { name: 'Customer XYZ', code: '67890' },
    ],
  }),
}));

// Mock destination autocomplete hook
jest.mock('@/hooks/useDestinationAutocomplete', () => ({
  useDestinationAutocomplete: () => ({
    suggestions: [],
    showSuggestions: false,
    selectedIndex: -1,
    handleInputChange: jest.fn(),
    handleKeyDown: jest.fn(),
    handleSuggestionSelect: jest.fn(),
    clearSuggestions: jest.fn(),
  }),
}));

// Test wrapper component
const TestWrapper = ({ children }: { children: React.ReactNode }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

// Test data
const mockCart: ItemPickingCart = {
  items: [
    {
      itemId: 'item-1',
      itemName: 'Test Widget',
      quantity: 5,
      unitOfMeasure: 'pcs',
      sourcePalletId: 'pallet-1',
      sourcePalletBarcode: 'PAL-001',
    },
  ],
  totalItems: 5,
  destination: '',
  destinationCode: '',
};

const mockOnCreateShipment = jest.fn();
const mockOnClose = jest.fn();

describe('EnhancedShipmentCreationModal', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders when open', () => {
    render(
      <TestWrapper>
        <EnhancedShipmentCreationModal
          isOpen={true}
          onClose={mockOnClose}
          onCreateShipment={mockOnCreateShipment}
          cart={mockCart}
          isCreating={false}
        />
      </TestWrapper>
    );

    expect(screen.getByText('Create Mixed Shipment')).toBeInTheDocument();
    expect(screen.getByLabelText(/destination/i)).toBeInTheDocument();
  });

  it('displays items and pallets tabs', () => {
    render(
      <TestWrapper>
        <EnhancedShipmentCreationModal
          isOpen={true}
          onClose={mockOnClose}
          onCreateShipment={mockOnCreateShipment}
          cart={mockCart}
          isCreating={false}
        />
      </TestWrapper>
    );

    expect(screen.getByText('Items (5)')).toBeInTheDocument();
    expect(screen.getByText('Pallets (0)')).toBeInTheDocument();
  });

  it('shows items tab content by default', () => {
    render(
      <TestWrapper>
        <EnhancedShipmentCreationModal
          isOpen={true}
          onClose={mockOnClose}
          onCreateShipment={mockOnCreateShipment}
          cart={mockCart}
          isCreating={false}
        />
      </TestWrapper>
    );

    expect(screen.getByText('Items from Cart')).toBeInTheDocument();
    expect(screen.getByText('Test Widget')).toBeInTheDocument();
  });

  it('switches to pallets tab when clicked', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <EnhancedShipmentCreationModal
          isOpen={true}
          onClose={mockOnClose}
          onCreateShipment={mockOnCreateShipment}
          cart={mockCart}
          isCreating={false}
        />
      </TestWrapper>
    );

    const palletsTab = screen.getByText('Pallets (0)');
    await user.click(palletsTab);

    expect(screen.getByText('Available Pallets')).toBeInTheDocument();
    expect(screen.getByText('PAL-001')).toBeInTheDocument();
    expect(screen.getByText('PAL-002')).toBeInTheDocument();
  });

  it('displays available pallets for selection', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <EnhancedShipmentCreationModal
          isOpen={true}
          onClose={mockOnClose}
          onCreateShipment={mockOnCreateShipment}
          cart={mockCart}
          isCreating={false}
        />
      </TestWrapper>
    );

    // Switch to pallets tab
    const palletsTab = screen.getByText('Pallets (0)');
    await user.click(palletsTab);

    expect(screen.getByText('PAL-001')).toBeInTheDocument();
    expect(screen.getByText('PAL-002')).toBeInTheDocument();
    expect(screen.getByText('Destination: Customer ABC')).toBeInTheDocument();
    expect(screen.getByText('Destination: Customer XYZ')).toBeInTheDocument();
  });

  it('allows pallet selection with checkboxes', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <EnhancedShipmentCreationModal
          isOpen={true}
          onClose={mockOnClose}
          onCreateShipment={mockOnCreateShipment}
          cart={mockCart}
          isCreating={false}
        />
      </TestWrapper>
    );

    // Switch to pallets tab
    const palletsTab = screen.getByText('Pallets (0)');
    await user.click(palletsTab);

    // Select a pallet
    const checkbox = screen.getByLabelText('pallet-pallet-1');
    await user.click(checkbox);

    expect(checkbox).toBeChecked();
  });

  it('updates pallet count in tab when pallets are selected', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <EnhancedShipmentCreationModal
          isOpen={true}
          onClose={mockOnClose}
          onCreateShipment={mockOnCreateShipment}
          cart={mockCart}
          isCreating={false}
        />
      </TestWrapper>
    );

    // Switch to pallets tab
    const palletsTab = screen.getByText('Pallets (0)');
    await user.click(palletsTab);

    // Select a pallet
    const checkbox = screen.getByLabelText('pallet-pallet-1');
    await user.click(checkbox);

    // Check that the tab count updated
    expect(screen.getByText('Pallets (1)')).toBeInTheDocument();
  });

  it('validates that either items or pallets are selected', async () => {
    const user = userEvent.setup();
    
    const emptyCart: ItemPickingCart = {
      items: [],
      totalItems: 0,
      destination: '',
      destinationCode: '',
    };

    render(
      <TestWrapper>
        <EnhancedShipmentCreationModal
          isOpen={true}
          onClose={mockOnClose}
          onCreateShipment={mockOnCreateShipment}
          cart={emptyCart}
          isCreating={false}
        />
      </TestWrapper>
    );

    const destinationInput = screen.getByLabelText(/destination/i);
    const createButton = screen.getByText('Create Shipment');

    await user.type(destinationInput, 'Customer ABC');
    await user.click(createButton);

    await waitFor(() => {
      expect(screen.getByText('Must include either items or pallets in shipment')).toBeInTheDocument();
    });

    expect(mockOnCreateShipment).not.toHaveBeenCalled();
  });

  it('submits form with selected pallets', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <EnhancedShipmentCreationModal
          isOpen={true}
          onClose={mockOnClose}
          onCreateShipment={mockOnCreateShipment}
          cart={mockCart}
          isCreating={false}
        />
      </TestWrapper>
    );

    // Fill destination
    const destinationInput = screen.getByLabelText(/destination/i);
    await user.type(destinationInput, 'Customer ABC');

    // Switch to pallets tab and select a pallet
    const palletsTab = screen.getByText('Pallets (0)');
    await user.click(palletsTab);

    const checkbox = screen.getByLabelText('pallet-pallet-1');
    await user.click(checkbox);

    // Submit form
    const createButton = screen.getByText('Create Shipment');
    await user.click(createButton);

    await waitFor(() => {
      expect(mockOnCreateShipment).toHaveBeenCalledWith(
        'Customer ABC',
        undefined,
        ['pallet-1']
      );
    });
  });

  it('submits form with both items and pallets', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <EnhancedShipmentCreationModal
          isOpen={true}
          onClose={mockOnClose}
          onCreateShipment={mockOnCreateShipment}
          cart={mockCart}
          isCreating={false}
        />
      </TestWrapper>
    );

    // Fill destination
    const destinationInput = screen.getByLabelText(/destination/i);
    await user.type(destinationInput, 'Customer ABC');

    // Select a pallet
    const palletsTab = screen.getByText('Pallets (0)');
    await user.click(palletsTab);

    const checkbox = screen.getByLabelText('pallet-pallet-1');
    await user.click(checkbox);

    // Submit form
    const createButton = screen.getByText('Create Shipment');
    await user.click(createButton);

    await waitFor(() => {
      expect(mockOnCreateShipment).toHaveBeenCalledWith(
        'Customer ABC',
        undefined,
        ['pallet-1']
      );
    });
  });

  it('shows loading state when creating shipment', () => {
    render(
      <TestWrapper>
        <EnhancedShipmentCreationModal
          isOpen={true}
          onClose={mockOnClose}
          onCreateShipment={mockOnCreateShipment}
          cart={mockCart}
          isCreating={true}
        />
      </TestWrapper>
    );

    expect(screen.getByText('Creating Shipment...')).toBeInTheDocument();
    expect(screen.getByText('Create Shipment')).toBeDisabled();
  });

  it('handles empty pallets list', async () => {
    // Mock empty pallets
    jest.mocked(require('@/hooks/api').usePalletsSuspense).mockReturnValue({
      data: [],
    });

    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <EnhancedShipmentCreationModal
          isOpen={true}
          onClose={mockOnClose}
          onCreateShipment={mockOnCreateShipment}
          cart={mockCart}
          isCreating={false}
        />
      </TestWrapper>
    );

    // Switch to pallets tab
    const palletsTab = screen.getByText('Pallets (0)');
    await user.click(palletsTab);

    expect(screen.getByText('No pallets available for shipping')).toBeInTheDocument();
  });

  it('displays mobile-optimized interface', () => {
    render(
      <TestWrapper>
        <EnhancedShipmentCreationModal
          isOpen={true}
          onClose={mockOnClose}
          onCreateShipment={mockOnCreateShipment}
          cart={mockCart}
          isCreating={false}
        />
      </TestWrapper>
    );

    const destinationInput = screen.getByLabelText(/destination/i);
    const createButton = screen.getByText('Create Shipment');

    expect(destinationInput).toHaveClass('min-h-[44px]');
    expect(createButton).toHaveClass('min-h-[44px]');
  });
});
