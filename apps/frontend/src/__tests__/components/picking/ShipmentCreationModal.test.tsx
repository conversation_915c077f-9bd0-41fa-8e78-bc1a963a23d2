/**
 * ShipmentCreationModal Component Tests
 * 
 * Tests the shipment creation modal including destination input,
 * autocomplete functionality, and form validation.
 */

import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ShipmentCreationModal } from '@/components/picking/ShipmentCreationModal';
import type { ItemPickingCart } from '@quildora/types';

// Mock providers
jest.mock('@/components/providers/auth-provider', () => ({
  useAuth: () => ({
    appUser: { id: 'user-1', name: 'Test User' },
    appToken: 'test-token',
    isLoading: false,
  }),
}));

jest.mock('@/components/providers/warehouse-provider', () => ({
  useWarehouse: () => ({
    currentWarehouse: { id: 'warehouse-1', name: 'Test Warehouse' },
    isLoading: false,
  }),
}));

// Mock destination autocomplete hook
const mockDestinations = [
  { name: 'Customer ABC', code: '12345' },
  { name: 'Customer XYZ', code: '67890' },
  { name: 'Customer DEF', code: undefined },
];

jest.mock('@/hooks/useDestinationAutocomplete', () => ({
  useDestinationAutocomplete: () => ({
    suggestions: mockDestinations,
    showSuggestions: false,
    selectedIndex: -1,
    handleInputChange: jest.fn(),
    handleKeyDown: jest.fn(),
    handleSuggestionSelect: jest.fn(),
    clearSuggestions: jest.fn(),
  }),
}));

// Test wrapper component
const TestWrapper = ({ children }: { children: React.ReactNode }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

// Test data
const mockCart: ItemPickingCart = {
  items: [
    {
      itemId: 'item-1',
      itemName: 'Test Widget',
      quantity: 5,
      unitOfMeasure: 'pcs',
      sourcePalletId: 'pallet-1',
      sourcePalletBarcode: 'PAL-001',
    },
    {
      itemId: 'item-2',
      itemName: 'Test Gadget',
      quantity: 3,
      unitOfMeasure: 'pcs',
      sourcePalletId: 'pallet-2',
      sourcePalletBarcode: 'PAL-002',
    },
  ],
  totalItems: 8,
  destination: '',
  destinationCode: '',
};

const mockOnCreateShipment = jest.fn();
const mockOnClose = jest.fn();

describe('ShipmentCreationModal', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders when open', () => {
    render(
      <TestWrapper>
        <ShipmentCreationModal
          isOpen={true}
          onClose={mockOnClose}
          onCreateShipment={mockOnCreateShipment}
          cart={mockCart}
          isCreating={false}
        />
      </TestWrapper>
    );

    expect(screen.getByText('Create Shipment')).toBeInTheDocument();
    expect(screen.getByLabelText(/destination/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/destination code/i)).toBeInTheDocument();
  });

  it('does not render when closed', () => {
    render(
      <TestWrapper>
        <ShipmentCreationModal
          isOpen={false}
          onClose={mockOnClose}
          onCreateShipment={mockOnCreateShipment}
          cart={mockCart}
          isCreating={false}
        />
      </TestWrapper>
    );

    expect(screen.queryByText('Create Shipment')).not.toBeInTheDocument();
  });

  it('displays cart items in the modal', () => {
    render(
      <TestWrapper>
        <ShipmentCreationModal
          isOpen={true}
          onClose={mockOnClose}
          onCreateShipment={mockOnCreateShipment}
          cart={mockCart}
          isCreating={false}
        />
      </TestWrapper>
    );

    expect(screen.getByText('Test Widget')).toBeInTheDocument();
    expect(screen.getByText('Test Gadget')).toBeInTheDocument();
    expect(screen.getByText('5 pcs')).toBeInTheDocument();
    expect(screen.getByText('3 pcs')).toBeInTheDocument();
  });

  it('handles destination input', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ShipmentCreationModal
          isOpen={true}
          onClose={mockOnClose}
          onCreateShipment={mockOnCreateShipment}
          cart={mockCart}
          isCreating={false}
        />
      </TestWrapper>
    );

    const destinationInput = screen.getByLabelText(/destination/i);
    await user.type(destinationInput, 'Customer ABC');

    expect(destinationInput).toHaveValue('Customer ABC');
  });

  it('handles destination code input', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ShipmentCreationModal
          isOpen={true}
          onClose={mockOnClose}
          onCreateShipment={mockOnCreateShipment}
          cart={mockCart}
          isCreating={false}
        />
      </TestWrapper>
    );

    const codeInput = screen.getByLabelText(/destination code/i);
    await user.type(codeInput, '12345');

    expect(codeInput).toHaveValue('12345');
  });

  it('validates required destination field', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ShipmentCreationModal
          isOpen={true}
          onClose={mockOnClose}
          onCreateShipment={mockOnCreateShipment}
          cart={mockCart}
          isCreating={false}
        />
      </TestWrapper>
    );

    const createButton = screen.getByText('Create Shipment');
    await user.click(createButton);

    await waitFor(() => {
      expect(screen.getByText('Destination is required')).toBeInTheDocument();
    });

    expect(mockOnCreateShipment).not.toHaveBeenCalled();
  });

  it('submits form with valid data', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ShipmentCreationModal
          isOpen={true}
          onClose={mockOnClose}
          onCreateShipment={mockOnCreateShipment}
          cart={mockCart}
          isCreating={false}
        />
      </TestWrapper>
    );

    const destinationInput = screen.getByLabelText(/destination/i);
    const codeInput = screen.getByLabelText(/destination code/i);
    const createButton = screen.getByText('Create Shipment');

    await user.type(destinationInput, 'Customer ABC');
    await user.type(codeInput, '12345');
    await user.click(createButton);

    await waitFor(() => {
      expect(mockOnCreateShipment).toHaveBeenCalledWith('Customer ABC', '12345');
    });
  });

  it('handles close button click', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ShipmentCreationModal
          isOpen={true}
          onClose={mockOnClose}
          onCreateShipment={mockOnCreateShipment}
          cart={mockCart}
          isCreating={false}
        />
      </TestWrapper>
    );

    const cancelButton = screen.getByText('Cancel');
    await user.click(cancelButton);

    expect(mockOnClose).toHaveBeenCalled();
  });

  it('shows loading state when creating shipment', () => {
    render(
      <TestWrapper>
        <ShipmentCreationModal
          isOpen={true}
          onClose={mockOnClose}
          onCreateShipment={mockOnCreateShipment}
          cart={mockCart}
          isCreating={true}
        />
      </TestWrapper>
    );

    expect(screen.getByText('Creating Shipment...')).toBeInTheDocument();
    expect(screen.getByText('Create Shipment')).toBeDisabled();
  });

  it('disables form inputs when creating shipment', () => {
    render(
      <TestWrapper>
        <ShipmentCreationModal
          isOpen={true}
          onClose={mockOnClose}
          onCreateShipment={mockOnCreateShipment}
          cart={mockCart}
          isCreating={true}
        />
      </TestWrapper>
    );

    expect(screen.getByLabelText(/destination/i)).toBeDisabled();
    expect(screen.getByLabelText(/destination code/i)).toBeDisabled();
  });

  it('displays mobile-optimized touch targets', () => {
    render(
      <TestWrapper>
        <ShipmentCreationModal
          isOpen={true}
          onClose={mockOnClose}
          onCreateShipment={mockOnCreateShipment}
          cart={mockCart}
          isCreating={false}
        />
      </TestWrapper>
    );

    const destinationInput = screen.getByLabelText(/destination/i);
    const createButton = screen.getByText('Create Shipment');

    expect(destinationInput).toHaveClass('min-h-[44px]');
    expect(createButton).toHaveClass('min-h-[44px]');
  });

  it('shows item source pallet information', () => {
    render(
      <TestWrapper>
        <ShipmentCreationModal
          isOpen={true}
          onClose={mockOnClose}
          onCreateShipment={mockOnCreateShipment}
          cart={mockCart}
          isCreating={false}
        />
      </TestWrapper>
    );

    expect(screen.getByText('From: PAL-001')).toBeInTheDocument();
    expect(screen.getByText('From: PAL-002')).toBeInTheDocument();
  });

  it('handles empty cart gracefully', () => {
    const emptyCart: ItemPickingCart = {
      items: [],
      totalItems: 0,
      destination: '',
      destinationCode: '',
    };

    render(
      <TestWrapper>
        <ShipmentCreationModal
          isOpen={true}
          onClose={mockOnClose}
          onCreateShipment={mockOnCreateShipment}
          cart={emptyCart}
          isCreating={false}
        />
      </TestWrapper>
    );

    expect(screen.getByText(/no items in cart/i)).toBeInTheDocument();
  });

  it('pre-fills destination from cart', () => {
    const cartWithDestination: ItemPickingCart = {
      ...mockCart,
      destination: 'Pre-filled Customer',
      destinationCode: '99999',
    };

    render(
      <TestWrapper>
        <ShipmentCreationModal
          isOpen={true}
          onClose={mockOnClose}
          onCreateShipment={mockOnCreateShipment}
          cart={cartWithDestination}
          isCreating={false}
        />
      </TestWrapper>
    );

    expect(screen.getByDisplayValue('Pre-filled Customer')).toBeInTheDocument();
    expect(screen.getByDisplayValue('99999')).toBeInTheDocument();
  });
});
