/**
 * Picking Workflows Integration Tests
 * 
 * Tests the complete picking workflows end-to-end:
 * 1. Pick → Release (Direct item release for local pickup)
 * 2. Pick → Pack → Ship (Traditional shipment creation)
 * 3. Pick → Pack → Ship with Pallets (Enhanced shipment with complete pallets)
 */

import { describe, it, expect, beforeEach } from 'vitest';

// Mock data structures for testing workflows
interface MockItem {
  id: string;
  name: string;
  sku: string;
  unitOfMeasure: string;
}

interface MockPallet {
  id: string;
  barcode: string;
  status: 'Stored' | 'Shipped' | 'Receiving';
  shipToDestination?: string;
  destinationCode?: string;
  items: Array<{
    itemId: string;
    quantity: number;
  }>;
}

interface MockPickingCart {
  items: Array<{
    itemId: string;
    itemName: string;
    quantity: number;
    unitOfMeasure: string;
    sourcePalletId: string;
    sourcePalletBarcode: string;
  }>;
  totalItems: number;
  destination: string;
  destinationCode: string;
}

interface MockShipment {
  id: string;
  destination: string;
  destinationCode?: string;
  status: 'PREPARING' | 'PACKED' | 'SHIPPED';
  items: Array<{
    itemId: string;
    quantity: number;
    sourcePalletId: string;
  }>;
  palletIds?: string[];
}

interface MockItemRelease {
  id: string;
  releasedTo: string;
  notes?: string;
  items: Array<{
    itemId: string;
    quantity: number;
    sourcePalletId: string;
  }>;
  releasedAt: Date;
}

// Mock workflow functions
class MockPickingWorkflow {
  private cart: MockPickingCart = {
    items: [],
    totalItems: 0,
    destination: '',
    destinationCode: '',
  };

  private shipments: MockShipment[] = [];
  private releases: MockItemRelease[] = [];

  addItemToCart(item: MockPickingCart['items'][0]) {
    this.cart.items.push(item);
    this.cart.totalItems = this.cart.items.reduce((total, item) => total + item.quantity, 0);
  }

  updateCartDestination(destination: string, destinationCode?: string) {
    this.cart.destination = destination;
    this.cart.destinationCode = destinationCode || '';
  }

  createShipment(destination: string, destinationCode?: string, selectedPallets?: string[]): MockShipment {
    const shipment: MockShipment = {
      id: `shipment-${Date.now()}`,
      destination,
      destinationCode,
      status: 'PREPARING',
      items: this.cart.items.map(item => ({
        itemId: item.itemId,
        quantity: item.quantity,
        sourcePalletId: item.sourcePalletId,
      })),
      palletIds: selectedPallets,
    };

    this.shipments.push(shipment);
    this.clearCart();
    return shipment;
  }

  releaseItems(releasedTo: string, notes?: string): MockItemRelease {
    const release: MockItemRelease = {
      id: `release-${Date.now()}`,
      releasedTo,
      notes,
      items: this.cart.items.map(item => ({
        itemId: item.itemId,
        quantity: item.quantity,
        sourcePalletId: item.sourcePalletId,
      })),
      releasedAt: new Date(),
    };

    this.releases.push(release);
    this.clearCart();
    return release;
  }

  updateShipmentStatus(shipmentId: string, status: MockShipment['status']) {
    const shipment = this.shipments.find(s => s.id === shipmentId);
    if (shipment) {
      shipment.status = status;
    }
    return shipment;
  }

  clearCart() {
    this.cart = {
      items: [],
      totalItems: 0,
      destination: '',
      destinationCode: '',
    };
  }

  getCart() {
    return { ...this.cart };
  }

  getShipments() {
    return [...this.shipments];
  }

  getReleases() {
    return [...this.releases];
  }
}

// Test data
const mockItems: MockItem[] = [
  { id: 'item-1', name: 'Widget A', sku: 'WID-001', unitOfMeasure: 'pcs' },
  { id: 'item-2', name: 'Widget B', sku: 'WID-002', unitOfMeasure: 'pcs' },
  { id: 'item-3', name: 'Gadget C', sku: 'GAD-001', unitOfMeasure: 'pcs' },
];

const mockPallets: MockPallet[] = [
  {
    id: 'pallet-1',
    barcode: 'PAL-001',
    status: 'Stored',
    shipToDestination: 'Customer ABC',
    destinationCode: '12345',
    items: [
      { itemId: 'item-1', quantity: 50 },
      { itemId: 'item-2', quantity: 30 },
    ],
  },
  {
    id: 'pallet-2',
    barcode: 'PAL-002',
    status: 'Stored',
    shipToDestination: 'Customer XYZ',
    destinationCode: '67890',
    items: [
      { itemId: 'item-3', quantity: 25 },
    ],
  },
];

describe('Picking Workflows Integration', () => {
  let workflow: MockPickingWorkflow;

  beforeEach(() => {
    workflow = new MockPickingWorkflow();
  });

  describe('Workflow 1: Pick → Release (Direct Item Release)', () => {
    it('should complete direct item release workflow', () => {
      // Step 1: Add items to cart
      workflow.addItemToCart({
        itemId: 'item-1',
        itemName: 'Widget A',
        quantity: 10,
        unitOfMeasure: 'pcs',
        sourcePalletId: 'pallet-1',
        sourcePalletBarcode: 'PAL-001',
      });

      workflow.addItemToCart({
        itemId: 'item-2',
        itemName: 'Widget B',
        quantity: 5,
        unitOfMeasure: 'pcs',
        sourcePalletId: 'pallet-1',
        sourcePalletBarcode: 'PAL-001',
      });

      // Verify cart state
      const cart = workflow.getCart();
      expect(cart.items).toHaveLength(2);
      expect(cart.totalItems).toBe(15);

      // Step 2: Release items directly
      const release = workflow.releaseItems(
        'Local Crew Team A',
        'Emergency stock pull for production line'
      );

      // Verify release
      expect(release.releasedTo).toBe('Local Crew Team A');
      expect(release.notes).toBe('Emergency stock pull for production line');
      expect(release.items).toHaveLength(2);
      expect(release.items[0].quantity).toBe(10);
      expect(release.items[1].quantity).toBe(5);
      expect(release.releasedAt).toBeInstanceOf(Date);

      // Verify cart is cleared
      const clearedCart = workflow.getCart();
      expect(clearedCart.items).toHaveLength(0);
      expect(clearedCart.totalItems).toBe(0);

      // Verify release is recorded
      const releases = workflow.getReleases();
      expect(releases).toHaveLength(1);
      expect(releases[0].id).toBe(release.id);
    });

    it('should handle multiple release operations', () => {
      // First release
      workflow.addItemToCart({
        itemId: 'item-1',
        itemName: 'Widget A',
        quantity: 5,
        unitOfMeasure: 'pcs',
        sourcePalletId: 'pallet-1',
        sourcePalletBarcode: 'PAL-001',
      });

      const release1 = workflow.releaseItems('Team A', 'First release');

      // Second release
      workflow.addItemToCart({
        itemId: 'item-2',
        itemName: 'Widget B',
        quantity: 3,
        unitOfMeasure: 'pcs',
        sourcePalletId: 'pallet-1',
        sourcePalletBarcode: 'PAL-001',
      });

      const release2 = workflow.releaseItems('Team B', 'Second release');

      // Verify both releases
      const releases = workflow.getReleases();
      expect(releases).toHaveLength(2);
      expect(releases[0].releasedTo).toBe('Team A');
      expect(releases[1].releasedTo).toBe('Team B');
    });
  });

  describe('Workflow 2: Pick → Pack → Ship (Traditional Shipment)', () => {
    it('should complete traditional shipment workflow', () => {
      // Step 1: Add items to cart
      workflow.addItemToCart({
        itemId: 'item-1',
        itemName: 'Widget A',
        quantity: 20,
        unitOfMeasure: 'pcs',
        sourcePalletId: 'pallet-1',
        sourcePalletBarcode: 'PAL-001',
      });

      workflow.addItemToCart({
        itemId: 'item-3',
        itemName: 'Gadget C',
        quantity: 8,
        unitOfMeasure: 'pcs',
        sourcePalletId: 'pallet-2',
        sourcePalletBarcode: 'PAL-002',
      });

      // Step 2: Set destination
      workflow.updateCartDestination('Customer ABC', '12345');

      const cart = workflow.getCart();
      expect(cart.destination).toBe('Customer ABC');
      expect(cart.destinationCode).toBe('12345');

      // Step 3: Create shipment
      const shipment = workflow.createShipment('Customer ABC', '12345');

      // Verify shipment
      expect(shipment.destination).toBe('Customer ABC');
      expect(shipment.destinationCode).toBe('12345');
      expect(shipment.status).toBe('PREPARING');
      expect(shipment.items).toHaveLength(2);
      expect(shipment.items[0].quantity).toBe(20);
      expect(shipment.items[1].quantity).toBe(8);

      // Step 4: Update shipment status (Pack)
      const packedShipment = workflow.updateShipmentStatus(shipment.id, 'PACKED');
      expect(packedShipment?.status).toBe('PACKED');

      // Step 5: Update shipment status (Ship)
      const shippedShipment = workflow.updateShipmentStatus(shipment.id, 'SHIPPED');
      expect(shippedShipment?.status).toBe('SHIPPED');

      // Verify cart is cleared
      const clearedCart = workflow.getCart();
      expect(clearedCart.items).toHaveLength(0);

      // Verify shipment is recorded
      const shipments = workflow.getShipments();
      expect(shipments).toHaveLength(1);
      expect(shipments[0].status).toBe('SHIPPED');
    });
  });

  describe('Workflow 3: Pick → Pack → Ship with Pallets (Enhanced Shipment)', () => {
    it('should complete enhanced shipment workflow with pallets', () => {
      // Step 1: Add individual items to cart
      workflow.addItemToCart({
        itemId: 'item-1',
        itemName: 'Widget A',
        quantity: 5,
        unitOfMeasure: 'pcs',
        sourcePalletId: 'pallet-1',
        sourcePalletBarcode: 'PAL-001',
      });

      // Step 2: Create shipment with both items and complete pallets
      const selectedPallets = ['pallet-2']; // Complete pallet
      const shipment = workflow.createShipment(
        'Customer XYZ',
        '67890',
        selectedPallets
      );

      // Verify enhanced shipment
      expect(shipment.destination).toBe('Customer XYZ');
      expect(shipment.destinationCode).toBe('67890');
      expect(shipment.items).toHaveLength(1); // Individual items
      expect(shipment.palletIds).toEqual(['pallet-2']); // Complete pallets
      expect(shipment.status).toBe('PREPARING');

      // Verify mixed content
      expect(shipment.items[0].itemId).toBe('item-1');
      expect(shipment.items[0].quantity).toBe(5);
      expect(shipment.palletIds).toContain('pallet-2');

      // Step 3: Process shipment through statuses
      workflow.updateShipmentStatus(shipment.id, 'PACKED');
      const finalShipment = workflow.updateShipmentStatus(shipment.id, 'SHIPPED');

      expect(finalShipment?.status).toBe('SHIPPED');
    });

    it('should handle pallet-only shipments', () => {
      // Create shipment with only complete pallets (no individual items)
      const selectedPallets = ['pallet-1', 'pallet-2'];
      const shipment = workflow.createShipment(
        'Customer DEF',
        '11111',
        selectedPallets
      );

      expect(shipment.items).toHaveLength(0); // No individual items
      expect(shipment.palletIds).toEqual(['pallet-1', 'pallet-2']);
      expect(shipment.destination).toBe('Customer DEF');
    });
  });

  describe('Cross-workflow Integration', () => {
    it('should handle multiple workflows in sequence', () => {
      // Workflow 1: Direct release
      workflow.addItemToCart({
        itemId: 'item-1',
        itemName: 'Widget A',
        quantity: 3,
        unitOfMeasure: 'pcs',
        sourcePalletId: 'pallet-1',
        sourcePalletBarcode: 'PAL-001',
      });

      const release = workflow.releaseItems('Team A');

      // Workflow 2: Traditional shipment
      workflow.addItemToCart({
        itemId: 'item-2',
        itemName: 'Widget B',
        quantity: 10,
        unitOfMeasure: 'pcs',
        sourcePalletId: 'pallet-1',
        sourcePalletBarcode: 'PAL-001',
      });

      const shipment1 = workflow.createShipment('Customer ABC', '12345');

      // Workflow 3: Enhanced shipment
      workflow.addItemToCart({
        itemId: 'item-3',
        itemName: 'Gadget C',
        quantity: 2,
        unitOfMeasure: 'pcs',
        sourcePalletId: 'pallet-2',
        sourcePalletBarcode: 'PAL-002',
      });

      const shipment2 = workflow.createShipment(
        'Customer XYZ',
        '67890',
        ['pallet-2']
      );

      // Verify all operations completed
      expect(workflow.getReleases()).toHaveLength(1);
      expect(workflow.getShipments()).toHaveLength(2);
      expect(workflow.getCart().items).toHaveLength(0);

      // Verify operation details
      expect(release.releasedTo).toBe('Team A');
      expect(shipment1.destination).toBe('Customer ABC');
      expect(shipment2.destination).toBe('Customer XYZ');
      expect(shipment2.palletIds).toContain('pallet-2');
    });

    it('should maintain data integrity across workflows', () => {
      const initialReleases = workflow.getReleases().length;
      const initialShipments = workflow.getShipments().length;

      // Perform multiple operations
      workflow.addItemToCart({
        itemId: 'item-1',
        itemName: 'Widget A',
        quantity: 1,
        unitOfMeasure: 'pcs',
        sourcePalletId: 'pallet-1',
        sourcePalletBarcode: 'PAL-001',
      });

      workflow.releaseItems('Test Team');

      workflow.addItemToCart({
        itemId: 'item-2',
        itemName: 'Widget B',
        quantity: 2,
        unitOfMeasure: 'pcs',
        sourcePalletId: 'pallet-1',
        sourcePalletBarcode: 'PAL-001',
      });

      workflow.createShipment('Test Customer');

      // Verify counts increased correctly
      expect(workflow.getReleases()).toHaveLength(initialReleases + 1);
      expect(workflow.getShipments()).toHaveLength(initialShipments + 1);

      // Verify cart is always cleared after operations
      expect(workflow.getCart().items).toHaveLength(0);
      expect(workflow.getCart().totalItems).toBe(0);
    });
  });
});
